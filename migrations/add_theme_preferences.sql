-- Add theme preferences to users table
-- This migration adds theme preferences to the existing user preferences structure

-- Add theme preference to content_preferences JSONB column
-- The content_preferences column will now include:
-- {
--   "theme": "light" | "dark" | "system",
--   "notifications": {
--     "email": boolean,
--     "push": boolean, 
--     "marketing": boolean
--   },
--   "publishing": {
--     "autoPublish": boolean,
--     "defaultCategory": string,
--     "targetAudience": string
--   }
-- }

-- Update existing users to have theme preference if they don't already
UPDATE public.users 
SET content_preferences = jsonb_set(
    COALESCE(content_preferences, '{}'),
    '{theme}',
    '"system"'
)
WHERE content_preferences IS NULL 
   OR NOT content_preferences ? 'theme';

-- Update existing users to have notifications object if they don't already
UPDATE public.users 
SET content_preferences = jsonb_set(
    content_preferences,
    '{notifications}',
    '{"email": true, "push": false, "marketing": false}'
)
WHERE NOT content_preferences ? 'notifications';

-- Update existing users to have publishing object if they don't already  
UPDATE public.users 
SET content_preferences = jsonb_set(
    content_preferences,
    '{publishing}',
    '{"autoPublish": false, "defaultCategory": "general", "targetAudience": "adults"}'
)
WHERE NOT content_preferences ? 'publishing';

-- Set default content_preferences for new users
ALTER TABLE public.users 
ALTER COLUMN content_preferences 
SET DEFAULT '{"theme": "system", "notifications": {"email": true, "push": false, "marketing": false}, "publishing": {"autoPublish": false, "defaultCategory": "general", "targetAudience": "adults"}}';

-- Add a check constraint to ensure theme is valid (optional)
ALTER TABLE public.users 
ADD CONSTRAINT valid_theme_preference 
CHECK (
    content_preferences IS NULL OR 
    content_preferences->'theme' IS NULL OR
    content_preferences->>'theme' IN ('light', 'dark', 'system')
);