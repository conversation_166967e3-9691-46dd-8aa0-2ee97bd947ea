-- Add avatar_url to users table
-- This migration adds an avatar_url field for storing user profile pictures

-- Add avatar_url column to users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS avatar_url TEXT;

-- Add a check constraint to ensure avatar_url is a valid URL format (optional)
ALTER TABLE public.users 
ADD CONSTRAINT valid_avatar_url 
CHECK (
    avatar_url IS NULL OR 
    avatar_url ~ '^https?://.*'
);