# NextAuth.js Integration Plan

## Summary

This document outlines the complete plan for integrating NextAuth.js into the Next.js frontend application. The integration will support multiple authentication methods including credentials-based login (via external API), Google OAuth, and Apple OAuth. The plan includes cleanup of legacy authentication code and comprehensive testing strategies.

## Current State Analysis

### Existing Implementation

- **NextAuth.js**: Already configured with multiple providers in `/src/app/api/auth/[...nextauth]/route.ts`
- **API Client**: Uses `apiClient` from `@/lib/api` for backend communication
- **Session Strategy**: JWT-based sessions
- **Providers**: Currently supports 11 providers (Credentials + 10 OAuth providers)

### Required Changes

1. Simplify to only support Credentials, Google, and Apple providers
2. Remove legacy authentication code
3. Ensure proper backend API integration
4. Add comprehensive error handling
5. Update all components to use NextAuth hooks

## Files to Create/Edit/Delete

### 1. Core NextAuth Configuration

#### Edit: `/src/app/api/auth/[...nextauth]/route.ts`

**Purpose**: Simplify NextAuth configuration to only support required providers
**Changes**:

- Remove unnecessary OAuth providers (keep only Google and Apple)
- Enhance credentials provider with better error handling
- Add proper callbacks for session management
- Implement secure token handling

### 2. Authentication Helpers

#### Create: `/src/lib/auth/authHelpers.ts`

**Purpose**: Centralized authentication utilities
**Responsibilities**:

- Token management functions
- Session validation helpers
- Error response standardization
- API authentication headers setup

### 3. Type Definitions

#### Create: `/src/types/auth.d.ts`

**Purpose**: TypeScript definitions for authentication
**Contents**:

- Extended NextAuth session types
- User model interfaces
- Authentication response types
- Error types for auth failures

### 4. Environment Configuration

#### Edit: `/app_frontend/.env.local`

**Add Required Variables**:

```bash
# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Apple OAuth
APPLE_CLIENT_ID=your-apple-client-id
APPLE_TEAM_ID=your-apple-team-id
APPLE_PRIVATE_KEY=your-apple-private-key
APPLE_KEY_ID=your-apple-key-id

# Backend API (already exists)
NEXT_PUBLIC_API_URL=http://localhost:8000
```

#### Create: `/app_frontend/.env.local.example`

**Purpose**: Template for environment variables

## Configuration Details

### NextAuth.js Provider Configuration

#### 1. CredentialsProvider

- **Endpoint**: `POST ${NEXT_PUBLIC_API_URL}/api/auth/login`
- **Request Format**: `{ email: string, password: string }`
- **Response Handling**: Extract user object and access token
- **Error States**: Invalid credentials, API timeout, network errors

#### 2. GoogleProvider

- **OAuth Flow**: Standard Google OAuth 2.0
- **Scopes**: email, profile
- **Callback Handling**: Send OAuth data to backend `/api/auth/oauth/callback`

#### 3. AppleProvider

- **OAuth Flow**: Sign in with Apple
- **Configuration**: Requires team ID, key ID, and private key
- **Special Considerations**: Apple's unique email relay service

### Callback Functions

#### jwt Callback

- Store user ID in JWT token
- Add access token from backend
- Handle token refresh logic

#### session Callback

- Attach user ID to session object
- Include user metadata
- Set session expiry

#### signIn Callback

- Handle OAuth provider data
- Call backend OAuth endpoints
- Manage user creation/update

## Cleanup Plan

### Files to Delete

1. `/src/hooks/auth/*` (if using custom auth hooks)
2. `/src/contexts/AuthContext.tsx` (if exists)
3. `/src/utils/auth.ts` (legacy auth utilities)
4. `/src/components/auth/LoginForm.tsx` (if using custom form)
5. `/src/components/auth/RegisterForm.tsx` (if using custom form)

### Code to Remove

1. Custom authentication state management
2. Manual token handling in API client
3. Legacy login/logout functions
4. Custom session checking logic

### Migration Steps

1. Update all imports from custom auth to NextAuth
2. Replace custom hooks with `useSession`
3. Update API client to use NextAuth session
4. Remove manual token storage logic

## Component Updates

### Pages to Update

#### `/src/app/auth/login/page.tsx`

- Replace custom form with `signIn()` from next-auth/react
- Add OAuth provider buttons
- Handle redirect after successful login

#### `/src/app/auth/register/page.tsx`

- Integrate with backend registration endpoint
- Auto-login after successful registration
- Add OAuth options for signup

#### `/src/app/dashboard/*`

- Add session checks using `useSession`
- Implement loading states
- Handle unauthenticated access

### Components to Update

#### Navigation/Header Components

- Use `useSession` for user state
- Update logout to use `signOut()`
- Show appropriate UI based on session

#### Protected Route Components

- Create middleware for auth protection
- Use NextAuth session validation
- Handle redirect to login

## Testing Plan

### Unit Tests

1. **Auth Helpers Testing**

   - Token validation functions
   - Error response handling
   - Session data transformation

2. **Provider Configuration**
   - Credentials provider login flow
   - OAuth callback handling
   - Session callback data

### Integration Tests

1. **Login Flow**

   - Successful credentials login
   - Failed login attempts
   - OAuth provider redirects

2. **Session Management**

   - Session persistence
   - Token refresh
   - Logout functionality

3. **Protected Routes**
   - Authenticated access
   - Redirect when unauthenticated
   - Session expiry handling

### E2E Tests

1. **Complete Auth Journey**
   - Register → Login → Access protected content
   - OAuth login → Profile completion
   - Session timeout → Re-authentication

### Testing Checklist

- [ ] Credentials login works with backend API
- [ ] Google OAuth completes full flow
- [ ] Apple OAuth completes full flow
- [ ] Session persists across page reloads
- [ ] Logout clears session completely
- [ ] Protected routes redirect properly
- [ ] API requests include auth headers
- [ ] Error messages display correctly
- [ ] Loading states show during auth
- [ ] Redirect URLs work as expected

## Edge Case Handling

### API Failures

1. **Backend Unavailable**

   - Show user-friendly error message
   - Provide retry mechanism
   - Log errors for debugging

2. **Invalid Credentials**

   - Clear error messaging
   - Prevent credential enumeration
   - Rate limiting consideration

3. **Network Timeouts**
   - Set reasonable timeout (30s)
   - Show timeout message
   - Allow manual retry

### Session Management

1. **Session Expiration**

   - Detect expired sessions
   - Prompt for re-authentication
   - Preserve user's intended destination

2. **Token Refresh Failure**
   - Fallback to login
   - Clear invalid session
   - Notify user appropriately

### OAuth Failures

1. **Provider Errors**

   - Handle denied permissions
   - Manage provider outages
   - Clear error messages

2. **Email Conflicts**

   - Handle existing accounts
   - Merge account options
   - Clear user guidance

3. **Callback Errors**
   - Validate state parameter
   - Handle missing data
   - Secure error logging

## Security Considerations

### Best Practices

1. **CSRF Protection**

   - Use NextAuth's built-in CSRF tokens
   - Validate on all state-changing operations

2. **Secure Headers**

   - Set appropriate CORS headers
   - Use secure cookie settings
   - Enable HTTPS in production

3. **Input Validation**
   - Sanitize all user inputs
   - Validate email formats
   - Check password complexity

### Production Checklist

- [ ] NEXTAUTH_SECRET is strong and unique
- [ ] OAuth redirect URLs are whitelisted
- [ ] HTTPS is enforced
- [ ] Secure cookie settings enabled
- [ ] Rate limiting implemented
- [ ] Error messages don't leak info
- [ ] Logs don't contain sensitive data

## Implementation Steps

### Phase 1: Setup and Configuration

1. Create type definitions file
2. Create auth helpers file
3. Update environment variables
4. Simplify NextAuth configuration

### Phase 2: Backend Integration

1. Update credentials provider
2. Configure OAuth callbacks
3. Implement token management
4. Add error handling

### Phase 3: Component Migration

1. Update login/register pages
2. Replace custom hooks with NextAuth
3. Update navigation components
4. Add session checks to protected routes

### Phase 4: Cleanup

1. Remove legacy auth code
2. Delete unused auth utilities
3. Clean up old auth components
4. Update imports throughout app

### Phase 5: Testing and Validation

1. Write unit tests for helpers
2. Test all auth flows manually
3. Implement E2E tests
4. Verify production readiness

## Monitoring and Maintenance

### Logging Strategy

- Log authentication attempts (success/failure)
- Track OAuth provider usage
- Monitor session durations
- Alert on unusual patterns

### Performance Considerations

- Implement session caching
- Optimize JWT size
- Lazy load OAuth providers
- Minimize auth check overhead

### Future Enhancements

- Add more OAuth providers as needed
- Implement passwordless auth
- Add two-factor authentication
- Enhanced session management

## References

- [NextAuth.js Documentation](https://next-auth.js.org/configuration/nextjs)
- [NextAuth.js Providers](https://next-auth.js.org/providers/)
- [NextAuth.js Callbacks](https://next-auth.js.org/configuration/callbacks)
- [NextAuth.js TypeScript](https://next-auth.js.org/getting-started/typescript)
- [Apple OAuth Setup](https://next-auth.js.org/providers/apple)
- [Google OAuth Setup](https://next-auth.js.org/providers/google)
