// Authentication type definitions

export enum AuthErrorType {
  CredentialsSignIn = "CredentialsSignIn",
  OAuthSignIn = "OAuthSignIn",
  OAuthCallback = "OAuthCallback",
  OAuthCreateAccount = "OAuthCreateAccount",
  EmailCreateAccount = "EmailCreateAccount",
  Callback = "Callback",
  OAuthAccountNotLinked = "OAuthAccountNotLinked",
  EmailSignIn = "EmailSignIn",
  CredentialsCreate = "CredentialsCreate",
  SessionRequired = "SessionRequired",
  Default = "Default",
}

export interface AuthError {
  type: AuthErrorType;
  message: string;
  code?: string;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  error?: AuthError;
  data?: any;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials extends LoginCredentials {
  name: string;
  confirmPassword?: string;
}

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  image?: string;
  role?: string;
}

export interface AuthSession {
  user: AuthUser;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: number;
}