import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'

// Import authOptions from the main NextAuth handler
import { authOptions } from '../[...nextauth]/route'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    // TODO: Backend /api/auth/me endpoint needs auth token support
    // For now, skip backend calls and use session data directly
    // if (session.accessToken && process.env.NEXT_PUBLIC_API_URL) {
    //   try {
    //     const backendResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/me`, {
    //       headers: {
    //         'Authorization': `Bearer ${session.accessToken}`,
    //         'Content-Type': 'application/json',
    //       },
    //     });

    //     if (backendResponse.ok) {
    //       const backendUserData = await backendResponse.json();
    //       return NextResponse.json(backendUserData);
    //     }
    //   } catch (backendError) {
    //     console.log('Backend auth request failed:', backendError);
    //   }
    // }

    // Fallback: Return user data from session if backend is unavailable
    const user = {
      id: session.user.id || '71e41718-8f74-443b-a68a-f140977fc84a',
      email: session.user.email || '<EMAIL>',
      name: session.user.name || 'Test User',
      avatar_url: session.user.image || null,
      role: 'user' as const,
      subscription_tier: 'free' as const, // Set to 'free' to show upgrade prompt
      preferences: {
        theme: 'system' as const,
        notifications: {
          email: true,
          push: false,
          marketing: false,
        },
        publishing: {
          autoPublish: false,
          defaultCategory: 'general',
          targetAudience: 'adults',
        },
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: new Date().toISOString(),
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('Error in /api/auth/me:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}