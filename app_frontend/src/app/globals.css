@import "tailwindcss";

@theme {
  /* Airbnb-inspired color palette */
  --color-primary: #ff385c;
  --color-primary-50: #fff0f3;
  --color-primary-100: #ffe1e7;
  --color-primary-200: #ffc2cf;
  --color-primary-300: #ff94ab;
  --color-primary-400: #ff5a7a;
  --color-primary-500: #ff385c;
  --color-primary-600: #e61e4d;
  --color-primary-700: #cc0036;
  --color-primary-800: #a60029;
  --color-primary-900: #800020;
  --color-primary-950: #590016;
  
  --color-secondary: #717171;
  --color-secondary-50: #f7f7f7;
  --color-secondary-100: #ebebeb;
  --color-secondary-200: #d4d4d4;
  --color-secondary-300: #b0b0b0;
  --color-secondary-400: #888888;
  --color-secondary-500: #717171;
  --color-secondary-600: #5a5a5a;
  --color-secondary-700: #484848;
  --color-secondary-800: #3c3c3c;
  --color-secondary-900: #343434;
  --color-secondary-950: #222222;
  
  --color-accent: #222222;
  --color-accent-50: #f6f6f6;
  --color-accent-100: #e7e7e7;
  --color-accent-200: #d1d1d1;
  --color-accent-300: #b0b0b0;
  --color-accent-400: #888888;
  --color-accent-500: #6f6f6f;
  --color-accent-600: #5d5d5d;
  --color-accent-700: #4c4c4c;
  --color-accent-800: #222222;
  --color-accent-900: #191919;
  --color-accent-950: #0d0d0d;
  
  --color-surface: #f7f7f7;
  --color-surface-50: #ffffff;
  --color-surface-100: #fcfcfc;
  --color-surface-200: #f7f7f7;
  --color-surface-300: #efefef;
  --color-surface-400: #e7e7e7;
  --color-surface-500: #dddddd;
  --color-surface-600: #c4c4c4;
  --color-surface-700: #a8a8a8;
  --color-surface-800: #8c8c8c;
  --color-surface-900: #717171;
  --color-surface-950: #595959;
  
  --color-background: #ffffff;
  --color-foreground: #222222;
  --color-border: #dddddd;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Muted colors */
  --color-muted: #f7f7f7;
  --color-muted-foreground: #717171;
  
  /* Card colors */
  --color-card: #ffffff;
  --color-card-foreground: #222222;
  
  /* Popover colors */
  --color-popover: #ffffff;
  --color-popover-foreground: #222222;
  
  /* Input and ring colors */
  --color-input: #ffffff;
  --color-ring: #ff385c;
  
  /* Destructive colors */
  --color-destructive: #dc2626;
  --color-destructive-foreground: #ffffff;
  
  /* Fonts */
  --font-sans: ui-sans-serif, system-ui, sans-serif;
  --font-display: ui-sans-serif, system-ui, sans-serif;
}
