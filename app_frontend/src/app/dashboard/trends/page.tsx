'use client'

import { Suspense } from 'react'
import { TrendsPage } from '@/components/dashboard/trends'
import { TrendsErrorBoundary } from '@/components/dashboard/trends/TrendsErrorBoundary'
import { LoadingSkeleton } from '@/components/dashboard/trends/LoadingSkeleton'

export default function TrendsPageRoute() {
  return (
    <TrendsErrorBoundary>
      <Suspense fallback={<LoadingSkeleton type="page" />}>
        <TrendsPage />
      </Suspense>
    </TrendsErrorBoundary>
  )
}