"use client"

import { useSession } from "next-auth/react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { PageHeader } from "@/components/layout/page-header"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { StatsCard } from "@/components/ui/stats-card"
import { QuickActions, type QuickActionItem } from "@/components/ui/quick-actions"
import { ActivityTimeline, type TimelineItem } from "@/components/ui/activity-timeline"
import { BookCard, type BookData } from "@/components/ui/book-card"
import { ChartWrapper, ChartPlaceholder } from "@/components/ui/chart-wrapper"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { ErrorMessage } from "@/components/ui/error-message"
import { useAnalyticsOverview } from "@/hooks/api/use-analytics"
import { useBooks } from "@/hooks/api/use-books"
import { useUser } from "@/hooks/api/use-auth"
import { 
  PlusCircle, 
  BookOpen, 
  TrendingUp, 
  DollarSign, 
  Users, 
  BarChart3,
  FileText, 
  Settings, 
  Download,
  Eye,
  AlertCircle,
  Menu
} from "lucide-react"

export default function DashboardPage() {
  const { data: session } = useSession()
  
  // API hooks for real data
  const { data: user, isLoading: userLoading, error: userError } = useUser()
  const { data: analytics, isLoading: analyticsLoading, error: analyticsError } = useAnalyticsOverview()
  const { data: booksData, isLoading: booksLoading, error: booksError } = useBooks({ limit: 6 })
  
  // Loading state
  if (userLoading || analyticsLoading || booksLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }
  
  // Error state
  if (userError || analyticsError || booksError) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">
                  Failed to load dashboard
                </h3>
                <p className="text-muted-foreground">
                  {String(
                    userError ||
                      analyticsError ||
                      booksError ||
                      "An error occurred while loading the dashboard"
                  )}
                </p>
                <Button onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Extract data with fallbacks
  const books = booksData?.books || []
  const totalBooks = booksData?.total || 0
  
  const quickActions: QuickActionItem[] = [
    {
      id: "1",
      title: "Generate New Book",
      description: "Create a new book with AI assistance",
      icon: <PlusCircle className="h-5 w-5" />,
      variant: "primary",
      onClick: () => window.location.href = '/dashboard/books/new'
    },
    {
      id: "2", 
      title: "View Analytics",
      description: "Check your sales and performance data",
      icon: <BarChart3 className="h-5 w-5" />,
      onClick: () => window.location.href = '/dashboard/analytics'
    },
    {
      id: "3",
      title: "Manage Library",
      description: "Browse and edit your book collection",
      icon: <BookOpen className="h-5 w-5" />,
      badge: totalBooks > 0 ? totalBooks.toString() : undefined,
      onClick: () => window.location.href = '/dashboard/books'
    },
    {
      id: "4",
      title: "Account Settings",
      description: "Update your profile and preferences",
      icon: <Settings className="h-5 w-5" />,
      onClick: () => window.location.href = '/dashboard/settings'
    }
  ]

  const recentActivity: TimelineItem[] = [
    {
      id: "1",
      title: "Welcome to Publish AI!",
      description: "Your account has been created successfully. Start by generating your first book.",
      timestamp: "Just now",
      status: "completed"
    },
    {
      id: "2",
      title: "Explore trending topics",
      description: "Use our AI trend analyzer to discover profitable book niches.",
      timestamp: "Getting started",
      status: "pending"
    },
    {
      id: "3",
      title: "Generate your first manuscript",
      description: "Let AI create high-quality content tailored to your chosen niche.",
      timestamp: "Next step",
      status: "pending"
    }
  ]

  const recentBooks: BookData[] = [
    {
      id: "demo-1",
      title: "Getting Started Guide",
      description: "Learn how to use Publish AI to create and publish books effortlessly.",
      author: "Publish AI",
      status: "draft",
      genre: "Tutorial",
      lastModified: "Just created",
      pages: 25
    }
  ]

  const salesData = [
    { month: "Jan", sales: 0, revenue: 0 },
    { month: "Feb", sales: 0, revenue: 0 },
    { month: "Mar", sales: 0, revenue: 0 },
    { month: "Apr", sales: 0, revenue: 0 },
    { month: "May", sales: 0, revenue: 0 },
    { month: "Jun", sales: 0, revenue: 0 }
  ]

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="p-6">
          <PageHeader
            title="Dashboard"
            description={`Welcome back${user?.name ? `, ${user.name}` : ''}! Here's an overview of your publishing activity.`}
            actions={
              <Button className="gap-2" onClick={() => console.log('Create new book')}>
                <PlusCircle className="h-4 w-4" />
                Create New Book
              </Button>
            }
          />
          
          {/* Stats Cards Section */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              value={analytics?.activeBooks?.toString() || totalBooks.toString()}
              label="Total Books"
              description={totalBooks > 0 ? "Your book collection" : "Start creating your first book"}
              icon={<BookOpen className="h-5 w-5" />}
              trend={analytics?.booksChange ? {
                direction: analytics.booksChange > 0 ? "up" : analytics.booksChange < 0 ? "down" : "neutral",
                value: Math.abs(analytics.booksChange),
                label: "vs last month"
              } : undefined}
            />
            <StatsCard
              value={books.filter(book => book.status === 'published').length.toString()}
              label="Published"
              description="Books available to readers"
              icon={<TrendingUp className="h-5 w-5" />}
              variant="success"
            />
            <StatsCard
              value={analytics?.totalRevenue ? `$${analytics.totalRevenue.toFixed(2)}` : "$0"}
              label="Total Revenue"
              description={analytics?.totalRevenue ? "Lifetime earnings" : "Start earning today"}
              icon={<DollarSign className="h-5 w-5" />}
              variant="info"
              trend={analytics?.revenueChange ? {
                direction: analytics.revenueChange > 0 ? "up" : analytics.revenueChange < 0 ? "down" : "neutral",
                value: Math.abs(analytics.revenueChange),
                label: "vs last month"
              } : undefined}
            />
            <StatsCard
              value={analytics?.totalSales?.toString() || "0"}
              label="Total Sales"
              description={analytics?.totalSales ? "Books sold" : "Build your audience"}
              icon={<Users className="h-5 w-5" />}
              trend={analytics?.salesChange ? {
                direction: analytics.salesChange > 0 ? "up" : analytics.salesChange < 0 ? "down" : "neutral",
                value: Math.abs(analytics.salesChange),
                label: "vs last month"
              } : undefined}
            />
          </div>

          {/* Quick Actions Section */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <QuickActions items={quickActions} columns={2} />
          </div>

          {/* Main Content Grid */}
          <div className="mt-8 grid gap-8 lg:grid-cols-3">
            {/* Recent Activity Timeline */}
            <div className="lg:col-span-2">
              <div className="space-y-6">
                {/* Recent Books */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold">Recent Books</h2>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View All
                    </Button>
                  </div>
                  {books.length > 0 ? (
                    <div className="grid gap-4 md:grid-cols-2">
                      {books.slice(0, 4).map((book) => (
                        <BookCard
                          key={book.id}
                          book={{
                            id: book.id,
                            title: book.title,
                            description: book.description,
                            author: user?.name || "You",
                            status: book.status,
                            genre: book.category,
                            lastModified: new Date(book.updated_at).toLocaleDateString(),
                            pages: Math.floor(book.word_count / 250) // Approximate pages
                          }}
                          variant="compact"
                          onView={(book) => window.location.href = `/dashboard/books/${book.id}`}
                          onEdit={(book) => window.location.href = `/dashboard/books/${book.id}/edit`}
                        />
                      ))}
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="py-8 text-center">
                        <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <h3 className="font-medium mb-2">No books yet</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Start by creating your first book with AI assistance
                        </p>
                        <Button onClick={() => console.log('Create first book')}>
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Create Your First Book
                        </Button>
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Performance Chart */}
                <div>
                  <h2 className="text-xl font-semibold mb-4">Performance Overview</h2>
                  <ChartWrapper
                    title="Sales & Revenue"
                    description="Track your publishing performance over time"
                    height="sm"
                    empty={true}
                    emptyMessage="Start publishing to see your performance data"
                    actions={
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                    }
                  >
                    <ChartPlaceholder type="area" data={salesData} />
                  </ChartWrapper>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Activity Timeline */}
              <div>
                <h2 className="text-xl font-semibold mb-4">Getting Started</h2>
                <ActivityTimeline 
                  items={recentActivity} 
                  compact 
                  size="sm"
                />
              </div>

              {/* Getting Started Guide */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Start Guide</CardTitle>
                  <CardDescription>
                    Follow these steps to publish your first book
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-semibold">
                        1
                      </div>
                      <div>
                        <div className="font-medium">Discover Trends</div>
                        <div className="text-sm text-muted-foreground">
                          Find profitable niches
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-semibold">
                        2
                      </div>
                      <div>
                        <div className="font-medium">Generate Content</div>
                        <div className="text-sm text-muted-foreground">
                          AI creates your manuscript
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-semibold">
                        3
                      </div>
                      <div>
                        <div className="font-medium">Publish & Earn</div>
                        <div className="text-sm text-muted-foreground">
                          One-click to Amazon KDP
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-6">
                    <Button className="w-full" onClick={() => console.log('Start tutorial')}>
                      <FileText className="h-4 w-4 mr-2" />
                      Start Tutorial
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}