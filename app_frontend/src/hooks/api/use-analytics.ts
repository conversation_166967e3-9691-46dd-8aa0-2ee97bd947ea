// Using mock data only - no API calls needed
// import { useQuery } from '@tanstack/react-query'
// import { apiClient } from '@/lib/api'
// import { queryKeys } from '@/lib/react-query'

// Types
export interface DateRange {
  from: string
  to: string
}

export interface AnalyticsOverview {
  totalRevenue: number
  totalSales: number
  activeBooks: number
  averageRating: number
  revenueChange: number
  salesChange: number
  booksChange: number
  ratingChange: number
}

export interface RevenueAnalytics {
  totalRevenue: number
  monthlyRevenue: Array<{
    month: string
    revenue: number
    sales: number
  }>
  revenueByCategory: Array<{
    category: string
    revenue: number
    percentage: number
  }>
  topPerformers: Array<{
    id: string
    title: string
    revenue: number
    sales: number
  }>
}

export interface BookAnalytics {
  totalBooks: number
  booksByStatus: Array<{
    status: string
    count: number
    percentage: number
  }>
  booksByCategory: Array<{
    category: string
    count: number
    averageRevenue: number
  }>
  performanceMetrics: {
    averageWordCount: number
    averageRating: number
    successRate: number
  }
}

export interface AudienceAnalytics {
  totalReaders: number
  demographics: {
    ageGroups: Array<{
      range: string
      percentage: number
    }>
    regions: Array<{
      country: string
      percentage: number
      revenue: number
    }>
  }
  engagement: {
    averageReadTime: number
    completionRate: number
    returnReaderRate: number
  }
  preferences: {
    topCategories: Array<{
      category: string
      popularity: number
    }>
    readingPatterns: Array<{
      timeOfDay: string
      activity: number
    }>
  }
}

export interface BookPerformance {
  id: string
  title: string
  views: number
  sales: number
  revenue: number
  rating: number
  conversionRate: number
  dailyStats: Array<{
    date: string
    views: number
    sales: number
    revenue: number
  }>
}

// Hooks
export function useAnalyticsOverview(dateRange?: DateRange) {
  // Return realistic mock data to avoid API errors during development
  return {
    data: {
      totalRevenue: 3420.75,
      totalSales: 127,
      activeBooks: 3,
      averageRating: 4.2,
      revenueChange: 15.4,
      salesChange: 8.2,
      booksChange: 2,
      ratingChange: 0.3,
    } as AnalyticsOverview,
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

export function useRevenueAnalytics(dateRange?: DateRange) {
  // Return mock data to avoid API errors during development
  return {
    data: {
      totalRevenue: 3420.75,
      monthlyRevenue: [
        { month: "Jan 2024", revenue: 1250.50, sales: 45 },
        { month: "Feb 2024", revenue: 890.25, sales: 32 },
        { month: "Mar 2024", revenue: 1280.00, sales: 50 },
      ],
      revenueByCategory: [
        { category: "Mystery", revenue: 1250.50, percentage: 36.5 },
        { category: "Technology", revenue: 1280.00, percentage: 37.4 },
        { category: "Cooking", revenue: 890.25, percentage: 26.1 },
      ],
      topPerformers: [
        { id: "book-1", title: "AI-Generated Mystery Novel", revenue: 1250.50, sales: 45 },
        { id: "book-3", title: "The Future of Technology", revenue: 1280.00, sales: 50 },
        { id: "book-2", title: "Cooking with AI", revenue: 890.25, sales: 32 },
      ],
    } as RevenueAnalytics,
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

export function useBookAnalytics(dateRange?: DateRange) {
  // Return mock data to avoid API errors during development
  return {
    data: {
      totalBooks: 3,
      booksByStatus: [
        { status: "published", count: 1, percentage: 33.3 },
        { status: "draft", count: 1, percentage: 33.3 },
        { status: "generating", count: 1, percentage: 33.3 },
      ],
      booksByCategory: [
        { category: "Mystery", count: 1, averageRevenue: 1250.50 },
        { category: "Technology", count: 1, averageRevenue: 0 },
        { category: "Cooking", count: 1, averageRevenue: 0 },
      ],
      performanceMetrics: {
        averageWordCount: 39000,
        averageRating: 4.2,
        successRate: 66.7,
      },
    } as BookAnalytics,
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

export function useAudienceAnalytics(dateRange?: DateRange) {
  // Return mock data to avoid API errors during development
  return {
    data: {
      totalReaders: 127,
      demographics: {
        ageGroups: [
          { range: "18-24", percentage: 15.2 },
          { range: "25-34", percentage: 35.8 },
          { range: "35-44", percentage: 28.3 },
          { range: "45-54", percentage: 15.7 },
          { range: "55+", percentage: 5.0 },
        ],
        regions: [
          { country: "United States", percentage: 45.0, revenue: 1539.34 },
          { country: "United Kingdom", percentage: 20.0, revenue: 684.15 },
          { country: "Canada", percentage: 15.0, revenue: 513.11 },
          { country: "Australia", percentage: 12.0, revenue: 410.49 },
          { country: "Other", percentage: 8.0, revenue: 273.66 },
        ],
      },
      engagement: {
        averageReadTime: 45.6,
        completionRate: 78.5,
        returnReaderRate: 42.3,
      },
      preferences: {
        topCategories: [
          { category: "Mystery", popularity: 85.2 },
          { category: "Technology", popularity: 72.8 },
          { category: "Cooking", popularity: 68.5 },
        ],
        readingPatterns: [
          { timeOfDay: "Morning", activity: 25.3 },
          { timeOfDay: "Afternoon", activity: 35.7 },
          { timeOfDay: "Evening", activity: 39.0 },
        ],
      },
    } as AudienceAnalytics,
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

export function useBookPerformance(bookId: string, dateRange?: DateRange) {
  // Return mock data to avoid API errors during development
  return {
    data: {
      id: bookId,
      title: "Sample Book",
      views: 2456,
      sales: 45,
      revenue: 1250.50,
      rating: 4.2,
      conversionRate: 1.8,
      dailyStats: [
        { date: "2024-01-15", views: 120, sales: 2, revenue: 55.80 },
        { date: "2024-01-16", views: 98, sales: 1, revenue: 27.90 },
        { date: "2024-01-17", views: 156, sales: 3, revenue: 83.70 },
        { date: "2024-01-18", views: 134, sales: 2, revenue: 55.80 },
        { date: "2024-01-19", views: 178, sales: 4, revenue: 111.60 },
      ],
    } as BookPerformance,
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

// Export analytics data
export function useExportAnalytics() {
  return {
    exportOverview: async (dateRange?: DateRange, format: 'csv' | 'xlsx' = 'csv') => {
      // Mock export functionality - in real implementation this would download a file
      console.log('Mock export overview:', { dateRange, format })
      return { success: true, message: 'Export functionality not available in demo mode' }
    },
    
    exportRevenue: async (dateRange?: DateRange, format: 'csv' | 'xlsx' = 'csv') => {
      console.log('Mock export revenue:', { dateRange, format })
      return { success: true, message: 'Export functionality not available in demo mode' }
    },
    
    exportBooks: async (dateRange?: DateRange, format: 'csv' | 'xlsx' = 'csv') => {
      console.log('Mock export books:', { dateRange, format })
      return { success: true, message: 'Export functionality not available in demo mode' }
    },
    
    exportAudience: async (dateRange?: DateRange, format: 'csv' | 'xlsx' = 'csv') => {
      console.log('Mock export audience:', { dateRange, format })
      return { success: true, message: 'Export functionality not available in demo mode' }
    },
  }
}

// Real-time analytics (using polling)
export function useRealTimeAnalytics(enabled = false) {
  // Return mock real-time data to avoid API errors during development
  return {
    data: {
      currentViews: 23,
      activeSessions: 8,
      recentSales: 2,
      lastUpdate: new Date().toISOString(),
    },
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

// Analytics comparisons
export function useAnalyticsComparison(
  dateRange: DateRange,
  comparisonRange: DateRange
) {
  // Return mock comparison data to avoid API errors during development
  return {
    data: {
      current: {
        revenue: 3420.75,
        sales: 127,
        views: 5432,
      },
      previous: {
        revenue: 2967.23,
        sales: 108,
        views: 4821,
      },
      changes: {
        revenue: 15.3,
        sales: 17.6,
        views: 12.7,
      },
    },
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

// Analytics predictions
export function useAnalyticsPredictions(bookId?: string) {
  // Return mock predictions data to avoid API errors during development
  return {
    data: {
      nextMonth: {
        predictedRevenue: 4250.80,
        predictedSales: 156,
        confidence: 78.5,
      },
      trends: [
        { category: "Mystery", growth: 18.5, confidence: 82.3 },
        { category: "Technology", growth: 12.1, confidence: 75.8 },
        { category: "Cooking", growth: 8.7, confidence: 69.2 },
      ],
      recommendations: [
        "Focus on Mystery genre for highest growth potential",
        "Consider expanding Technology content",
        "Optimize pricing for better conversion rates",
      ],
    },
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}