// Using mock data for main hooks, keeping mutations for future use
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'react-hot-toast'

// Types
export interface Book {
  id: string;
  title: string;
  description: string;
  category: string;
  status:
    | "draft"
    | "published"
    | "generating"
    | "failed"
    | "in-review"
    | "archived";
  cover_url?: string;
  created_at: string;
  updated_at: string;
  word_count: number;
  revenue: number;
  sales: number;
  rating: number;
}

export interface BookFilters {
  status?: string
  category?: string
  search?: string
  sort?: string
  limit?: number
  offset?: number
}

export interface CreateBookRequest {
  title: string
  description: string
  category: string
  niche?: string
  target_audience?: string
  keywords?: string[]
}

export interface UpdateBookRequest {
  title?: string
  description?: string
  category?: string
  status?: string
}

// Hooks
export function useBooks(filters?: BookFilters) {
  // Return mock data to avoid API errors during development
  const mockBooks: Book[] = [
    {
      id: "book-1",
      title: "AI-Generated Mystery Novel",
      description: "A thrilling mystery novel generated with advanced AI",
      category: "Mystery",
      status: "published",
      cover_url: "/images/mystery-book-cover.jpg",
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-20T10:00:00Z",
      word_count: 75000,
      revenue: 1250.50,
      sales: 45,
      rating: 4.2,
    },
    {
      id: "book-2", 
      title: "The Future of Technology",
      description: "An insightful guide to emerging technologies",
      category: "Technology",
      status: "draft",
      created_at: "2024-01-10T10:00:00Z",
      updated_at: "2024-01-18T10:00:00Z",
      word_count: 42000,
      revenue: 0,
      sales: 0,
      rating: 0,
    },
    {
      id: "book-3",
      title: "Cooking with AI",
      description: "Innovative recipes created with artificial intelligence",
      category: "Cooking",
      status: "generating",
      created_at: "2024-01-22T10:00:00Z",
      updated_at: "2024-01-22T10:00:00Z",
      word_count: 0,
      revenue: 0,
      sales: 0,
      rating: 0,
    }
  ];

  // Apply filters if provided
  let filteredBooks = mockBooks;
  if (filters?.status) {
    filteredBooks = filteredBooks.filter(book => book.status === filters.status);
  }
  if (filters?.category) {
    filteredBooks = filteredBooks.filter(book => book.category === filters.category);
  }
  if (filters?.search) {
    const searchLower = filters.search.toLowerCase();
    filteredBooks = filteredBooks.filter(book => 
      book.title.toLowerCase().includes(searchLower) ||
      book.description.toLowerCase().includes(searchLower)
    );
  }

  return {
    data: {
      books: filteredBooks,
      total: filteredBooks.length,
    },
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

export function useBook(id: string) {
  // Return mock data for specific book to avoid API errors during development
  const mockBooks = [
    {
      id: "book-1",
      title: "AI-Generated Mystery Novel",
      description: "A thrilling mystery novel generated with advanced AI",
      category: "Mystery",
      status: "published" as const,
      cover_url: "/images/mystery-book-cover.jpg",
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-20T10:00:00Z",
      word_count: 75000,
      revenue: 1250.50,
      sales: 45,
      rating: 4.2,
    },
    {
      id: "book-2", 
      title: "The Future of Technology",
      description: "An insightful guide to emerging technologies",
      category: "Technology",
      status: "draft" as const,
      created_at: "2024-01-10T10:00:00Z",
      updated_at: "2024-01-18T10:00:00Z",
      word_count: 42000,
      revenue: 0,
      sales: 0,
      rating: 0,
    },
    {
      id: "book-3",
      title: "Cooking with AI",
      description: "Innovative recipes created with artificial intelligence",
      category: "Cooking",
      status: "generating" as const,
      created_at: "2024-01-22T10:00:00Z",
      updated_at: "2024-01-22T10:00:00Z",
      word_count: 0,
      revenue: 0,
      sales: 0,
      rating: 0,
    }
  ];

  const book = mockBooks.find(b => b.id === id) || mockBooks[0];

  return {
    data: book,
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

export function useBookAnalytics(id: string) {
  // Return mock analytics data to avoid API errors during development
  return {
    data: {
      views: 2456,
      downloads: 127,
      revenue: 1250.50,
      rating: 4.2,
      conversionRate: 5.2,
      dailyStats: [
        { date: "2024-01-15", views: 120, downloads: 6, revenue: 167.40 },
        { date: "2024-01-16", views: 98, downloads: 4, revenue: 111.60 },
        { date: "2024-01-17", views: 156, downloads: 8, revenue: 223.20 },
        { date: "2024-01-18", views: 134, downloads: 5, revenue: 139.50 },
        { date: "2024-01-19", views: 178, downloads: 9, revenue: 251.10 },
      ],
    },
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  }
}

export function useCreateBook() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: CreateBookRequest) => apiClient.post<Book>('/api/books', data),
    onMutate: async (newBook) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.books.lists() })
      
      // Snapshot the previous value
      const previousBooks = queryClient.getQueryData(queryKeys.books.lists())
      
      // Optimistically update to the new value
      const optimisticBook: Book = {
        id: crypto.randomUUID(),
        ...newBook,
        status: 'generating' as const,
        cover_url: undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        word_count: 0,
        revenue: 0,
        sales: 0,
        rating: 0,
      }
      
      // Add to all book lists
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books) {
            return {
              ...old,
              books: [optimisticBook, ...old.books],
              total: old.total + 1,
            }
          }
          return old
        }
      )
      
      return { previousBooks, optimisticBook }
    },
    onError: (err, newBook, context) => {
      // Revert the optimistic update
      if (context?.previousBooks) {
        queryClient.setQueriesData(
          { queryKey: queryKeys.books.lists() },
          context.previousBooks
        )
      }
      toast.error('Failed to create book')
    },
    onSuccess: (data, variables, context) => {
      // Replace optimistic book with real data
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books && context?.optimisticBook) {
            return {
              ...old,
              books: old.books.map((book: Book) =>
                book.id === context.optimisticBook.id ? data : book
              ),
            }
          }
          return old
        }
      )
      
      // Set individual book data
      queryClient.setQueryData(queryKeys.books.detail(data.id), data)
      
      toast.success('Book created successfully!')
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })
    },
  })
}

export function useUpdateBook() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateBookRequest }) =>
      apiClient.patch<Book>(`/api/books/${id}`, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.books.detail(id) })
      
      // Snapshot the previous value
      const previousBook = queryClient.getQueryData(queryKeys.books.detail(id))
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({
        ...old,
        ...data,
        updated_at: new Date().toISOString(),
      }))
      
      // Update in lists as well
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books) {
            return {
              ...old,
              books: old.books.map((book: Book) =>
                book.id === id ? { ...book, ...data, updated_at: new Date().toISOString() } : book
              ),
            }
          }
          return old
        }
      )
      
      return { previousBook }
    },
    onError: (err, { id }, context) => {
      // Revert the optimistic update
      if (context?.previousBook) {
        queryClient.setQueryData(queryKeys.books.detail(id), context.previousBook)
      }
      toast.error('Failed to update book')
    },
    onSuccess: (data) => {
      toast.success('Book updated successfully!')
    },
    onSettled: (data, error, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.books.detail(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })
    },
  })
}

export function useDeleteBook() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => apiClient.delete(`/api/books/${id}`),
    onMutate: async (id) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.books.lists() })
      
      // Snapshot the previous value
      const previousBooks = queryClient.getQueryData(queryKeys.books.lists())
      
      // Optimistically remove from lists
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books) {
            return {
              ...old,
              books: old.books.filter((book: Book) => book.id !== id),
              total: Math.max(0, old.total - 1),
            }
          }
          return old
        }
      )
      
      return { previousBooks }
    },
    onError: (err, id, context) => {
      // Revert the optimistic update
      if (context?.previousBooks) {
        queryClient.setQueriesData(
          { queryKey: queryKeys.books.lists() },
          context.previousBooks
        )
      }
      toast.error('Failed to delete book')
    },
    onSuccess: (data, id) => {
      // Remove individual book data
      queryClient.removeQueries({ queryKey: queryKeys.books.detail(id) })
      queryClient.removeQueries({ queryKey: queryKeys.books.analytics(id) })
      
      toast.success('Book deleted successfully!')
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })
    },
  })
}

export function usePublishBook() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => apiClient.post(`/api/books/${id}/publish`),
    onMutate: async (id) => {
      // Optimistically update status
      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({
        ...old,
        status: 'published' as const,
      }))
      
      // Update in lists
      queryClient.setQueriesData(
        { queryKey: queryKeys.books.lists() },
        (old: any) => {
          if (old?.books) {
            return {
              ...old,
              books: old.books.map((book: Book) =>
                book.id === id ? { ...book, status: 'published' as const } : book
              ),
            }
          }
          return old
        }
      )
    },
    onError: (err, id) => {
      // Revert status on error
      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({
        ...old,
        status: 'draft' as const,
      }))
      toast.error('Failed to publish book')
    },
    onSuccess: () => {
      toast.success('Book published successfully!')
    },
    onSettled: (data, error, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.books.detail(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })
    },
  })
}