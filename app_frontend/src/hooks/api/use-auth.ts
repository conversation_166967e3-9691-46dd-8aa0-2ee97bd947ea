import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useSession, signIn, signOut } from 'next-auth/react'
import { apiClient } from '@/lib/api'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'react-hot-toast'

// Types - Updated to match database schema
export interface User {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  subscription_tier: "free" | "pro" | "enterprise";
  
  // Publishing preferences from database
  preferred_ai_provider: string;
  default_writing_style: string;
  default_target_audience: string;
  
  // KDP Integration
  kdp_email?: string;
  kdp_settings: Record<string, any>;
  
  // User preferences and settings from database
  notification_preferences: {
    email: boolean;
    in_app: boolean;
  };
  content_preferences: {
    theme: 'light' | 'dark' | 'system';
    notifications: {
      email: boolean;
      push: boolean;
      marketing: boolean;
    };
    publishing: {
      autoPublish: boolean;
      defaultCategory: string;
      targetAudience: string;
    };
  };
  privacy_settings: {
    data_sharing: boolean;
    analytics: boolean;
  };
  
  // Analytics and performance metrics
  total_books_generated: number;
  total_books_published: number;
  total_revenue: number;
  avg_quality_score: number;
  
  // Security and compliance
  last_password_change: string;
  two_factor_enabled: boolean;
  data_retention_consent: boolean;
  marketing_consent: boolean;
  
  // Timestamps
  created_at: string;
  updated_at: string;
  last_login?: string;
}

// Legacy interface for backward compatibility
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  notifications: {
    email: boolean
    push: boolean
    marketing: boolean
  }
  publishing: {
    autoPublish: boolean
    defaultCategory: string
    targetAudience: string
  }
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
}

export interface UpdateProfileRequest {
  name?: string
  avatar_url?: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

// Helper function to transform database user to legacy format
function transformUserForLegacyComponents(dbUser: User): TransformedUser {
  return {
    ...dbUser,
    name: dbUser.full_name, // Map full_name to name for backward compatibility
    role: 'user' as const, // Default role
    preferences: {
      theme: dbUser.content_preferences?.theme || 'system',
      notifications: dbUser.content_preferences?.notifications || {
        email: true,
        push: false,
        marketing: false,
      },
      publishing: dbUser.content_preferences?.publishing || {
        autoPublish: false,
        defaultCategory: 'general',
        targetAudience: 'adults',
      },
    },
  };
}

// Type for the transformed user that includes legacy fields
export type TransformedUser = User & { 
  name: string; 
  role: 'user' | 'admin'; 
  preferences: UserPreferences;
};

// Hooks
export function useUser(): {
  data: TransformedUser | undefined;
  isLoading: boolean;
  error: any;
  refetch: () => Promise<any>;
} {
  const { data: session, status } = useSession();

  // Query to fetch real user data from backend
  const userQuery = useQuery({
    queryKey: queryKeys.auth.user(),
    queryFn: async () => {
      const response = await apiClient.get<User>('/api/auth/me');
      return response;
    },
    enabled: status === 'authenticated' && !!session?.user,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401/403 errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Fallback to session data if backend query fails or is loading
  const fallbackUser = session?.user ? {
    id: session.user.id || "71e41718-8f74-443b-a68a-f140977fc84a",
    email: session.user.email || "",
    full_name: session.user.name || "",
    avatar_url: session.user.image || undefined,
    subscription_tier: "free" as const,
    preferred_ai_provider: "openai",
    default_writing_style: "professional",
    default_target_audience: "general adults",
    kdp_settings: {},
    notification_preferences: { email: true, in_app: true },
    content_preferences: {
      theme: "system" as const,
      notifications: { email: true, push: false, marketing: false },
      publishing: { autoPublish: false, defaultCategory: "general", targetAudience: "adults" },
    },
    privacy_settings: { data_sharing: false, analytics: true },
    total_books_generated: 0,
    total_books_published: 0,
    total_revenue: 0,
    avg_quality_score: 0,
    last_password_change: new Date().toISOString(),
    two_factor_enabled: false,
    data_retention_consent: true,
    marketing_consent: false,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: new Date().toISOString(),
  } as User : undefined;

  // Use real data if available, otherwise fallback
  const userData = userQuery.data || fallbackUser;
  const transformedUser = userData ? transformUserForLegacyComponents(userData) : undefined;

  return {
    data: transformedUser,
    isLoading: status === "loading" || (status === 'authenticated' && userQuery.isLoading),
    error: status === "unauthenticated" ? new Error("Not authenticated") : userQuery.error,
    refetch: userQuery.refetch,
  };
}

export function useLogin() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (credentials: LoginRequest) => {
      const result = await signIn('credentials', {
        ...credentials,
        redirect: false,
      })
      
      if (result?.error) {
        throw new Error(result.error)
      }
      
      return result
    },
    onSuccess: () => {
      // Invalidate auth queries to fetch fresh user data
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() })
      toast.success('Logged in successfully!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Login failed')
    },
  })
}

export function useRegister() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: RegisterRequest) => apiClient.post<User>('/api/auth/register', data),
    onSuccess: async (user, variables) => {
      // Auto-login after successful registration
      const result = await signIn('credentials', {
        email: variables.email,
        password: variables.password,
        redirect: false,
      })
      
      if (!result?.error) {
        queryClient.setQueryData(queryKeys.auth.user(), user)
        toast.success('Account created successfully!')
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Registration failed')
    },
  })
}

export function useLogout() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async () => {
      await signOut({ redirect: false })
      apiClient.removeAuthToken()
    },
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear()
      toast.success('Logged out successfully!')
    },
  })
}

export function useUpdateProfile() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: UpdateProfileRequest) => 
      apiClient.patch<User>('/api/auth/profile', data),
    onMutate: async (data) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.auth.user() })
      
      // Snapshot the previous value
      const previousUser = queryClient.getQueryData(queryKeys.auth.user())
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.auth.user(), (old: User) => ({
        ...old,
        ...data,
        updated_at: new Date().toISOString(),
      }))
      
      return { previousUser }
    },
    onError: (err, data, context) => {
      // Revert the optimistic update
      if (context?.previousUser) {
        queryClient.setQueryData(queryKeys.auth.user(), context.previousUser)
      }
      toast.error('Failed to update profile')
    },
    onSuccess: () => {
      toast.success('Profile updated successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() })
    },
  })
}

export function useChangePassword() {
  return useMutation({
    mutationFn: (data: ChangePasswordRequest) => 
      apiClient.post('/api/auth/change-password', data),
    onSuccess: () => {
      toast.success('Password changed successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to change password')
    },
  })
}

export function useUpdatePreferences() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (preferences: Partial<UserPreferences>) =>
      apiClient.patch<User>('/api/auth/preferences', preferences),
    onMutate: async (preferences) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.auth.user() })
      
      // Snapshot the previous value
      const previousUser = queryClient.getQueryData(queryKeys.auth.user())
      
      // Optimistically update the content_preferences in the database format
      queryClient.setQueryData(queryKeys.auth.user(), (old: User | undefined) => {
        if (!old) return old;
        
        return {
          ...old,
          content_preferences: {
            ...old.content_preferences,
            theme: preferences.theme ?? old.content_preferences.theme,
            notifications: preferences.notifications ? {
              ...old.content_preferences.notifications,
              ...preferences.notifications,
            } : old.content_preferences.notifications,
            publishing: preferences.publishing ? {
              ...old.content_preferences.publishing,
              ...preferences.publishing,
            } : old.content_preferences.publishing,
          },
          updated_at: new Date().toISOString(),
        };
      })
      
      return { previousUser }
    },
    onError: (err, preferences, context) => {
      // Revert the optimistic update
      if (context?.previousUser) {
        queryClient.setQueryData(queryKeys.auth.user(), context.previousUser)
      }
      toast.error('Failed to update preferences')
    },
    onSuccess: () => {
      toast.success('Preferences updated successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() })
    },
  })
}

export function useForgotPassword() {
  return useMutation({
    mutationFn: (email: string) => 
      apiClient.post('/api/auth/forgot-password', { email }),
    onSuccess: () => {
      toast.success('Password reset email sent!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send reset email')
    },
  })
}

export function useResetPassword() {
  return useMutation({
    mutationFn: ({ token, password }: { token: string; password: string }) =>
      apiClient.post('/api/auth/reset-password', { token, password }),
    onSuccess: () => {
      toast.success('Password reset successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reset password')
    },
  })
}

export function useVerifyEmail() {
  return useMutation({
    mutationFn: (token: string) => 
      apiClient.post('/api/auth/verify-email', { token }),
    onSuccess: () => {
      toast.success('Email verified successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Email verification failed')
    },
  })
}

export function useResendVerification() {
  return useMutation({
    mutationFn: () => apiClient.post('/api/auth/resend-verification'),
    onSuccess: () => {
      toast.success('Verification email sent!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send verification email')
    },
  })
}

// OAuth providers
export function useOAuthLogin() {
  const loginWithProvider = (provider: string) => 
    signIn(provider, { callbackUrl: '/dashboard' })

  return {
    loginWithProvider,
    loginWithGoogle: () => loginWithProvider('google'),
    loginWithGithub: () => loginWithProvider('github'),
    loginWithFacebook: () => loginWithProvider('facebook'),
    loginWithTwitter: () => loginWithProvider('twitter'),
    loginWithDiscord: () => loginWithProvider('discord'),
    loginWithLinkedIn: () => loginWithProvider('linkedin'),
    loginWithSpotify: () => loginWithProvider('spotify'),
    loginWithSlack: () => loginWithProvider('slack'),
    loginWithMicrosoft: () => loginWithProvider('azure-ad'),
    loginWithApple: () => loginWithProvider('apple'),
  }
}

// SSO login
export function useSSOLogin() {
  return useMutation({
    mutationFn: async (data: { domain?: string; providerId?: string }) => {
      const response = await apiClient.post<{ url: string }>('/api/auth/sso/signin', {
        domain: data.domain,
        provider_id: data.providerId,
      })
      
      if (response.url) {
        window.location.href = response.url
      }
      
      return response
    },
    onError: (error: any) => {
      toast.error(error.message || 'SSO sign-in failed')
    },
  })
}

// Session management
export function useSessionStatus() {
  const { status } = useSession()
  
  return {
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated',
    isUnauthenticated: status === 'unauthenticated',
  }
}

// User permissions
export function useUserPermissions() {
  const { data: user } = useUser()
  
  return {
    isAdmin: user?.role === 'admin',
    canCreateBooks: !!user,
    canPublishBooks: !!user,
    canViewAnalytics: !!user,
    canManageSettings: !!user,
  }
}