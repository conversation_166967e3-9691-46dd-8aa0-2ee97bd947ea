'use client'

import { useState, useEffect } from 'react'
import { TrendsFilters, TrendsData } from '@/types/trends'

interface UseTrendsDataResult {
  data: TrendsData | undefined
  isLoading: boolean
  error: Error | null
  refetch: () => void
}

// Enhanced mock data with Airbnb-style theming  
const mockTrendsData: TrendsData = {
  metrics: {
    totalVolume: 24500000, // $24.5M
    avgPrice: 485000, // $485K
    avgHoldingDuration: 22, // 22 months
    volumeChange: 18.7, // +18.7%
    priceChange: -2.3, // -2.3%
    durationChange: 8.4, // +8.4%
  },
  chartData: [
    { timestamp: '2024-06-01', value: 18200000, volume: 18200000, assetType: 'residential' },
    { timestamp: '2024-06-08', value: 19800000, volume: 19800000, assetType: 'residential' },
    { timestamp: '2024-06-15', value: 17500000, volume: 17500000, assetType: 'commercial' },
    { timestamp: '2024-06-22', value: 22100000, volume: 22100000, assetType: 'residential' },
    { timestamp: '2024-06-29', value: 24500000, volume: 24500000, assetType: 'mixed' },
    { timestamp: '2024-07-01', value: 23800000, volume: 23800000, assetType: 'commercial' },
  ],
  heatmapData: [
    {
      region: 'San Francisco Bay Area',
      lat: 37.7749,
      lng: -122.4194,
      volume: 8500000,
      avgPrice: 1200000,
      sentiment: 85,
      count: 42,
    },
    {
      region: 'New York Metro',
      lat: 40.7128,
      lng: -74.0060,
      volume: 6200000,
      avgPrice: 890000,
      sentiment: 72,
      count: 38,
    },
    {
      region: 'London',
      lat: 51.5074,
      lng: -0.1278,
      volume: 4800000,
      avgPrice: 650000,
      sentiment: 68,
      count: 29,
    },
    {
      region: 'Toronto',
      lat: 43.6532,
      lng: -79.3832,
      volume: 3200000,
      avgPrice: 520000,
      sentiment: 74,
      count: 23,
    },
    {
      region: 'Singapore',
      lat: 1.3521,
      lng: 103.8198,
      volume: 2800000,
      avgPrice: 720000,
      sentiment: 79,
      count: 18,
    },
    {
      region: 'Sydney',
      lat: -33.8688,
      lng: 151.2093,
      volume: 2100000,
      avgPrice: 580000,
      sentiment: 66,
      count: 15,
    },
  ],
  forecasts: {
    priceForecasts: [
      {
        assetType: 'residential',
        currentPrice: 485000,
        predictedPrice: 558000,
        confidenceInterval: [542000, 574000],
        timeHorizon: '3M',
      },
      {
        assetType: 'commercial',
        currentPrice: 720000,
        predictedPrice: 795000,
        confidenceInterval: [778000, 812000],
        timeHorizon: '3M',
      },
    ],
    yieldForecasts: [
      {
        assetType: 'residential',
        currentYield: 5.8,
        predictedYield: 6.2,
        confidenceInterval: [5.7, 6.7],
        timeHorizon: '12M',
      },
      {
        assetType: 'commercial',
        currentYield: 7.2,
        predictedYield: 7.8,
        confidenceInterval: [7.3, 8.3],
        timeHorizon: '12M',
      },
    ],
    sentimentIndex: {
      value: 75,
      trend: 'up',
      confidence: 87,
      factors: [
        'institutional_adoption',
        'regulatory_clarity', 
        'market_liquidity',
        'urban_growth_trends',
        'interest_rate_environment'
      ],
    },
  },
  commentary: {
    content: `Market momentum continues to strengthen across major metropolitan areas, with institutional investors driving significant volume increases. The tokenization of real estate assets has gained substantial traction, particularly in high-value residential markets.

Key developments include enhanced liquidity protocols, expanded geographical coverage, and improved price discovery mechanisms. The integration of AI-powered valuation models has increased investor confidence and reduced settlement times.

Regional analysis shows particularly strong performance in North American and European markets, with Asia-Pacific regions showing steady growth. The residential sector maintains its leadership position, while commercial tokenization is experiencing accelerated adoption.`,
    sources: [
      'real_time_market_data',
      'institutional_trading_patterns', 
      'regulatory_filings',
      'blockchain_analytics',
      'sentiment_analysis',
      'economic_indicators',
      'property_valuations',
      'liquidity_metrics'
    ],
    confidence: 89,
    lastUpdated: new Date(),
    validatedSchema: true,
  },
  filters: {
    assetTypes: ['residential', 'commercial'],
    regions: ['north-america', 'europe'],
    timePeriod: '30D',
  },
}

export const useTrendsData = (filters: TrendsFilters): UseTrendsDataResult => {
  const [data, setData] = useState<TrendsData | undefined>(undefined)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call delay with more realistic timing
      await new Promise(resolve => setTimeout(resolve, 1200))
      
      // In a real implementation, this would make an API call:
      // const response = await fetch('/api/dashboard/trends/data', {
      //   method: 'POST',
      //   headers: { 
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${getAuthToken()}`
      //   },
      //   body: JSON.stringify({
      //     filters,
      //     includeForecasts: true,
      //     includeCommentary: true
      //   }),
      // })
      // 
      // if (!response.ok) {
      //   throw new Error(`API Error: ${response.status} ${response.statusText}`)
      // }
      //
      // const data = await response.json()
      
      // Simulate data filtering based on filters
      const filteredData = {
        ...mockTrendsData,
        filters: filters,
        // Simulate filtered chart data
        chartData: mockTrendsData.chartData.filter(point => {
          if (filters.assetTypes.length > 0) {
            return filters.assetTypes.some(type => 
              point.assetType?.includes(type) || point.assetType === 'mixed'
            )
          }
          return true
        }),
        // Simulate filtered heatmap data
        heatmapData: mockTrendsData.heatmapData.filter(point => {
          if (filters.regions.length > 0) {
            // Simple region matching - in real app would be more sophisticated
            return filters.regions.some(region => 
              region.includes('america') && point.region.includes('Francisco') ||
              region.includes('america') && point.region.includes('New York') ||
              region.includes('america') && point.region.includes('Toronto') ||
              region.includes('europe') && point.region.includes('London') ||
              region.includes('asia') && point.region.includes('Singapore') ||
              region.includes('asia') && point.region.includes('Sydney')
            )
          }
          return true
        })
      }
      
      setData(filteredData)
    } catch (err) {
      setError(err as Error)
      console.error('Error fetching PublishAI trends data:', err)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [filters.assetTypes, filters.regions, filters.timePeriod])

  const refetch = () => {
    fetchData()
  }

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Additional specialized hooks for specific data types
export const useMetrics = (filters: TrendsFilters) => {
  const { data, isLoading, error, refetch } = useTrendsData(filters)
  
  return {
    data: data?.metrics,
    isLoading,
    error,
    refetch,
  }
}

export const useChartData = (filters: TrendsFilters) => {
  const { data, isLoading, error, refetch } = useTrendsData(filters)
  
  return {
    data: data?.chartData,
    isLoading,
    error,
    refetch,
  }
}

export const useForecasts = (filters: TrendsFilters) => {
  const { data, isLoading, error, refetch } = useTrendsData(filters)
  
  return {
    data: data?.forecasts,
    isLoading,
    error,
    refetch,
  }
}

export const useCommentary = (filters: TrendsFilters) => {
  const { data, isLoading, error, refetch } = useTrendsData(filters)
  
  return {
    data: data?.commentary,
    isLoading,
    error,
    refetch,
  }
}