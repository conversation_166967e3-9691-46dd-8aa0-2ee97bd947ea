'use client'

import React, { useState, useCallback } from 'react'
import { TrendsHeader } from './TrendsHeader'
import { TrendsFilters } from './TrendsFilters'
import { MetricsGrid } from './MetricsGrid'
import { ChartsGrid } from './ChartsGrid'
import { GeoHeatmap } from './GeoHeatmap'
import { AIForecasts } from './AIForecasts'
import { ActionsBar } from './ActionsBar'
import { useTrendsData } from '@/hooks/dashboard/useTrendsData'
import { TrendsFilters as TrendsFiltersType, ChartEvent } from '@/types/trends'

const defaultFilters: TrendsFiltersType = {
  assetTypes: ['residential', 'commercial'],
  regions: ['north-america', 'europe'],
  timePeriod: '30D',
}

export const TrendsPage: React.FC = () => {
  const [filters, setFilters] = useState<TrendsFiltersType>(defaultFilters)
  const [selectedTimeRange, setSelectedTimeRange] = useState<[Date, Date]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    new Date(),
  ])

  const {
    data: trendsData,
    isLoading,
    error,
    refetch,
  } = useTrendsData(filters)

  const handleFiltersChange = useCallback((newFilters: Partial<TrendsFiltersType>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }, [])

  const handleChartInteraction = useCallback((event: ChartEvent) => {
    console.log('Chart interaction:', event)
    // Handle chart interactions (zoom, hover, etc.)
  }, [])

  const handleExport = useCallback((format: 'csv' | 'json' | 'pdf') => {
    console.log('Export requested:', format)
    // Export functionality will be implemented
  }, [])

  const handleRefresh = useCallback(() => {
    refetch()
  }, [refetch])

  const handleConfigureAlerts = useCallback(() => {
    console.log('Configure alerts requested')
    // Alert configuration will be implemented
  }, [])

  const handleRegionClick = useCallback((region: string) => {
    console.log('Region clicked:', region)
    // Handle region selection
  }, [])

  const handleMetricChange = useCallback((metric: string) => {
    console.log('Metric changed:', metric)
    // Handle heatmap metric change
  }, [])

  const handleCommentaryRefresh = useCallback(() => {
    console.log('Commentary refresh requested')
    // Refresh AI commentary
  }, [])

  if (error) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="max-w-md mx-4">
          <div className="bg-white rounded-2xl shadow-sm border border-red-200 p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-8 h-8 text-[#C13515]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-[#484848] mb-2">
              Unable to load market data
            </h2>
            <p className="text-[#767676] mb-6">{error.message}</p>
            <button
              onClick={handleRefresh}
              className="bg-[#FF5A5F] hover:bg-[#E04E53] text-white font-medium px-6 py-3 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <TrendsHeader
          lastUpdated={trendsData?.commentary?.lastUpdated || new Date()}
          isLoading={isLoading}
          onRefresh={handleRefresh}
          onExport={handleExport}
          onConfigureAlerts={handleConfigureAlerts}
        />

        {/* Filters */}
        <TrendsFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          availableAssetTypes={[
            { id: 'residential', name: 'Residential', category: 'property' },
            { id: 'commercial', name: 'Commercial', category: 'property' },
            { id: 'land', name: 'Land', category: 'property' },
          ]}
          availableRegions={[
            { id: 'north-america', name: 'Americas', code: 'NA', continent: 'Americas' },
            { id: 'europe', name: 'Europe', code: 'EU', continent: 'Europe' },
            { id: 'asia', name: 'Asia', code: 'AS', continent: 'Asia' },
          ]}
          presets={[
            { id: 'hot-markets', name: 'Hot Markets', filters: { assetTypes: ['residential'], timePeriod: '7D' } },
            { id: 'institutional', name: 'Institutional', filters: { assetTypes: ['commercial'], timePeriod: '30D' } },
          ]}
        />

        {/* Key Metrics */}
        <MetricsGrid
          metrics={trendsData?.metrics || {
            totalVolume: 0,
            avgPrice: 0,
            avgHoldingDuration: 0,
            volumeChange: 0,
            priceChange: 0,
            durationChange: 0,
          }}
          loading={isLoading}
          timeframe={filters.timePeriod}
        />

        {/* Charts Grid */}
        <ChartsGrid
          volumeData={trendsData?.chartData || []}
          priceData={trendsData?.chartData || []}
          yieldData={[]}
          onChartInteraction={handleChartInteraction}
          selectedTimeRange={selectedTimeRange}
        />

        {/* Geographic Heatmap */}
        <GeoHeatmap
          data={trendsData?.heatmapData || []}
          selectedMetric="volume"
          onRegionSelect={handleRegionClick}
          onMetricChange={handleMetricChange}
        />

        {/* AI Forecasts */}
        <AIForecasts
          forecasts={trendsData?.forecasts || {
            priceForecasts: [],
            yieldForecasts: [],
            sentimentIndex: { value: 0, trend: 'stable', confidence: 0, factors: [] },
          }}
          commentary={trendsData?.commentary || {
            content: '',
            sources: [],
            confidence: 0,
            lastUpdated: new Date(),
            validatedSchema: false,
          }}
          confidenceLevel={75}
          onCommentaryRefresh={handleCommentaryRefresh}
        />

        {/* Actions Bar */}
        <ActionsBar
          onExport={handleExport}
          onSetAlert={handleConfigureAlerts}
          onShare={() => console.log('Share requested')}
          onAPIAccess={() => console.log('API access requested')}
        />
      </div>
    </div>
  )
}