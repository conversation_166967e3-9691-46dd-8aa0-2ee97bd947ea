'use client'

import React from 'react'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ErrorBoundaryProps {
  children: React.ReactNode
}

export class TrendsErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('PublishAI Trends page error:', error, errorInfo)
    // Send to monitoring service in production
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center p-4">
          <div className="max-w-lg w-full">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 text-center">
              {/* Error Icon */}
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center">
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
              </div>
              
              {/* Error Message */}
              <h2 className="text-2xl font-semibold text-[#484848] mb-3">
                Oops! Something went wrong
              </h2>
              
              <p className="text-[#767676] mb-6 leading-relaxed">
                {this.state.error?.message || 
                 'We encountered an unexpected error while loading the PublishAI Market Trends. Our team has been notified and is working on a fix.'}
              </p>
              
              {/* Action Buttons */}
              <div className="space-y-3">
                <button 
                  onClick={() => {
                    this.setState({ hasError: false })
                    window.location.reload()
                  }}
                  className="w-full flex items-center justify-center px-6 py-3 bg-[#FF5A5F] hover:bg-[#E04E53] text-white font-medium rounded-xl shadow-sm transition-all duration-200 hover:shadow-md"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </button>
                
                <button 
                  onClick={() => window.location.href = '/dashboard'}
                  className="w-full flex items-center justify-center px-6 py-3 text-[#484848] bg-gray-100 hover:bg-gray-200 font-medium rounded-xl transition-all duration-200"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </button>
              </div>

              {/* Support Info */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <p className="text-sm text-[#B0B0B0] mb-2">
                  Need help? Contact our support team
                </p>
                <div className="flex items-center justify-center space-x-4 text-xs text-[#767676]">
                  <span><EMAIL></span>
                  <span>•</span>
                  <span>24/7 Support</span>
                </div>
              </div>
              
              {/* Development Error Details */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-6 text-left">
                  <summary className="text-sm text-[#767676] cursor-pointer hover:text-[#484848] transition-colors duration-200 flex items-center">
                    <Bug className="w-4 h-4 mr-2" />
                    Error Details (Development)
                  </summary>
                  <div className="mt-3 p-4 bg-gray-100 rounded-lg">
                    <pre className="text-xs text-[#484848] overflow-auto whitespace-pre-wrap">
                      {this.state.error.stack}
                    </pre>
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}