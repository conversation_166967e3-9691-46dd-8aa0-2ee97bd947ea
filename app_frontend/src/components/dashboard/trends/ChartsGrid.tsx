'use client'

import React from 'react'
import { TrendsChartsProps } from '@/types/trends'
import { Line<PERSON>hart, BarChart3, TrendingUp, Zap } from 'lucide-react'

export const ChartsGrid: React.FC<TrendsChartsProps> = ({
  volumeData,
  priceData,
  yieldData,
  onChartInteraction,
  selectedTimeRange,
}) => {
  return (
    <div className="mb-8">
      {/* Section Header */}
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <LineChart className="w-5 h-5 text-[#FF5A5F] mr-3" />
          <h2 className="text-xl font-semibold text-[#484848]">Market Visualizations</h2>
        </div>
        <p className="text-sm text-[#767676]">
          Interactive charts showing trading patterns, price movements, and yield analytics
        </p>
      </div>
      
      {/* Charts Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6">
        {/* Volume Trends Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-[#484848]">Trading Volume</h3>
            <div className="flex items-center text-xs text-[#767676]">
              <div className="w-2 h-2 bg-[#FF5A5F] rounded-full mr-2"></div>
              Real-time data
            </div>
          </div>
          <div className="h-64 bg-gradient-to-br from-gray-50 to-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-200">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-[#FF5A5F] mx-auto mb-3" />
              <p className="text-[#767676] font-medium">Volume Chart</p>
              <p className="text-xs text-[#B0B0B0] mt-1">Recharts implementation coming soon</p>
            </div>
          </div>
          {/* Chart Legend */}
          <div className="flex items-center justify-center mt-4 space-x-4">
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-[#FF5A5F] rounded mr-2"></div>
              <span className="text-[#767676]">Volume</span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-[#00A699] rounded mr-2"></div>
              <span className="text-[#767676]">Trend</span>
            </div>
          </div>
        </div>

        {/* Price Index Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-[#484848]">Price Index</h3>
            <div className="flex space-x-2">
              <button className="px-3 py-1 text-xs bg-[#FF5A5F] text-white rounded-full">All</button>
              <button className="px-3 py-1 text-xs bg-gray-100 text-[#767676] rounded-full hover:bg-gray-200 transition-colors duration-200">Residential</button>
              <button className="px-3 py-1 text-xs bg-gray-100 text-[#767676] rounded-full hover:bg-gray-200 transition-colors duration-200">Commercial</button>
            </div>
          </div>
          <div className="h-64 bg-gradient-to-br from-gray-50 to-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-200">
            <div className="text-center">
              <TrendingUp className="w-12 h-12 text-[#FF5A5F] mx-auto mb-3" />
              <p className="text-[#767676] font-medium">Price Trends</p>
              <p className="text-xs text-[#B0B0B0] mt-1">Multi-asset line chart with filters</p>
            </div>
          </div>
        </div>
      </div>

      {/* Yield Curve - Full Width */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-[#484848] mb-1">Yield Curve Analysis</h3>
            <p className="text-sm text-[#767676]">Historical vs. forecasted yields with confidence intervals</p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-[#FF5A5F] rounded mr-2"></div>
              <span className="text-[#767676]">Historical</span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-[#FF8E53] rounded mr-2"></div>
              <span className="text-[#767676]">Forecast</span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-1 bg-[#FFB5B7] rounded mr-2"></div>
              <span className="text-[#767676]">Confidence Band</span>
            </div>
          </div>
        </div>
        <div className="h-80 bg-gradient-to-br from-gray-50 to-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-200">
          <div className="text-center">
            <Zap className="w-16 h-16 text-[#FF5A5F] mx-auto mb-4" />
            <p className="text-[#767676] font-medium text-lg">Advanced Yield Curve</p>
            <p className="text-sm text-[#B0B0B0] mt-2">Area chart with confidence intervals and interactive tooltips</p>
            <div className="mt-4 flex items-center justify-center space-x-6 text-xs text-[#767676]">
              <span>• Duration: 1M - 30Y</span>
              <span>• Confidence: 95%</span>
              <span>• Updated: Real-time</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}