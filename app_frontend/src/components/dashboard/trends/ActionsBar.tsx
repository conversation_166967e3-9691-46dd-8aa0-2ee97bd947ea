'use client'

import React from 'react'
import { Download, Bell, Share2, Code, FileText, Database, Webhook } from 'lucide-react'

interface ActionsBarProps {
  onExport: (format: 'csv' | 'json' | 'pdf') => void
  onSetAlert: () => void
  onShare: () => void
  onAPIAccess: () => void
}

export const ActionsBar: React.FC<ActionsBarProps> = ({
  onExport,
  onSetAlert,
  onShare,
  onAPIAccess,
}) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        {/* Section Info */}
        <div className="mb-6 lg:mb-0">
          <h3 className="text-lg font-medium text-[#484848] mb-1">Export & Integration</h3>
          <p className="text-sm text-[#767676]">
            Download reports, configure alerts, and integrate with your systems
          </p>
        </div>
        
        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          {/* Alert Setup */}
          <button
            onClick={onSetAlert}
            className="flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 hover:shadow-sm"
          >
            <Bell className="w-4 h-4 mr-2" />
            Set Alert
          </button>
          
          {/* Export Options */}
          <div className="flex items-center">
            <button
              onClick={() => onExport('csv')}
              className="flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 rounded-l-xl transition-all duration-200 border-r border-gray-200"
            >
              <Download className="w-4 h-4 mr-2" />
              CSV
            </button>
            <button
              onClick={() => onExport('json')}
              className="flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 transition-all duration-200 border-r border-gray-200"
            >
              <Database className="w-4 h-4 mr-2" />
              JSON
            </button>
            <button
              onClick={() => onExport('pdf')}
              className="flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 rounded-r-xl transition-all duration-200"
            >
              <FileText className="w-4 h-4 mr-2" />
              PDF
            </button>
          </div>
          
          {/* Share */}
          <button
            onClick={onShare}
            className="flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 hover:shadow-sm"
          >
            <Share2 className="w-4 h-4 mr-2" />
            Share
          </button>
          
          {/* API Access */}
          <button
            onClick={onAPIAccess}
            className="flex items-center px-6 py-3 text-sm font-medium text-white bg-[#FF5A5F] hover:bg-[#E04E53] rounded-xl shadow-sm transition-all duration-200 hover:shadow-md"
          >
            <Code className="w-4 h-4 mr-2" />
            API Access
          </button>
        </div>
      </div>

      {/* Quick Integration Options */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Webhook Integration */}
          <div className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
            <div className="w-10 h-10 bg-[#FFB5B7] rounded-lg flex items-center justify-center mr-3">
              <Webhook className="w-5 h-5 text-[#FF5A5F]" />
            </div>
            <div>
              <div className="text-sm font-medium text-[#484848]">Webhook Alerts</div>
              <div className="text-xs text-[#767676]">Real-time notifications</div>
            </div>
          </div>

          {/* REST API */}
          <div className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              <Code className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <div className="text-sm font-medium text-[#484848]">REST API</div>
              <div className="text-xs text-[#767676]">Programmatic access</div>
            </div>
          </div>

          {/* Scheduled Reports */}
          <div className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              <FileText className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <div className="text-sm font-medium text-[#484848]">Scheduled Reports</div>
              <div className="text-xs text-[#767676]">Automated delivery</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}