'use client'

import React from 'react'

interface LoadingSkeletonProps {
  type: 'page' | 'metrics' | 'chart' | 'heatmap'
}

const MetricsSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    {[1, 2, 3].map(i => (
      <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-gray-200 rounded w-20"></div>
            <div className="w-10 h-10 bg-gray-200 rounded-xl"></div>
          </div>
          {/* Value */}
          <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-32 mb-3"></div>
          {/* Change indicator */}
          <div className="flex items-center">
            <div className="h-6 bg-gray-200 rounded-full w-16"></div>
            <div className="h-3 bg-gray-200 rounded w-24 ml-2"></div>
          </div>
          {/* Sparkline */}
          <div className="mt-4 h-6 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-full"></div>
        </div>
      </div>
    ))}
  </div>
)

const ChartSkeleton: React.FC = () => (
  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div className="animate-pulse">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="h-5 bg-gray-200 rounded w-32"></div>
        <div className="flex space-x-2">
          <div className="h-6 bg-gray-200 rounded-full w-12"></div>
          <div className="h-6 bg-gray-200 rounded-full w-16"></div>
          <div className="h-6 bg-gray-200 rounded-full w-20"></div>
        </div>
      </div>
      {/* Chart area */}
      <div className="h-64 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4"></div>
      {/* Legend */}
      <div className="flex items-center justify-center space-x-4">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-gray-200 rounded mr-2"></div>
          <div className="h-3 bg-gray-200 rounded w-12"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-gray-200 rounded mr-2"></div>
          <div className="h-3 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    </div>
  </div>
)

const HeatmapSkeleton: React.FC = () => (
  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
    <div className="animate-pulse">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
        <div className="mb-4 lg:mb-0">
          <div className="h-5 bg-gray-200 rounded w-48 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-64"></div>
        </div>
        <div className="flex space-x-2">
          <div className="h-8 bg-gray-200 rounded-lg w-20"></div>
          <div className="h-8 bg-gray-200 rounded-lg w-24"></div>
          <div className="h-8 bg-gray-200 rounded-lg w-20"></div>
        </div>
      </div>
      {/* Map area */}
      <div className="h-96 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-6"></div>
      {/* Legend and stats */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div className="flex items-center space-x-4 mb-4 lg:mb-0">
          <div className="h-4 bg-gray-200 rounded w-16"></div>
          <div className="flex space-x-1">
            {[1,2,3,4,5].map(i => (
              <div key={i} className="w-4 h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-4 bg-gray-200 rounded w-12"></div>
        </div>
        <div className="flex items-center space-x-6">
          <div className="h-4 bg-gray-200 rounded w-24"></div>
          <div className="h-4 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    </div>
  </div>
)

const PageSkeleton: React.FC = () => (
  <div className="min-h-screen bg-[#F7F7F7]">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header Skeleton */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-6">
        <div className="animate-pulse">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center mb-3">
                <div className="w-1 h-8 bg-gray-200 rounded-full mr-4"></div>
                <div className="h-8 bg-gray-200 rounded w-64"></div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-48"></div>
            </div>
            <div className="flex space-x-3">
              {[1,2,3,4].map(i => (
                <div key={i} className="h-10 bg-gray-200 rounded-xl w-24"></div>
              ))}
            </div>
          </div>
          {/* Quick stats */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[1,2,3,4].map(i => (
                <div key={i} className="text-center">
                  <div className="h-6 bg-gray-200 rounded w-12 mx-auto mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-20 mx-auto"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Filters Skeleton */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="animate-pulse">
          <div className="h-5 bg-gray-200 rounded w-32 mb-6"></div>
          <div className="space-y-6">
            <div>
              <div className="h-4 bg-gray-200 rounded w-24 mb-3"></div>
              <div className="flex flex-wrap gap-2">
                {[1,2,3].map(i => (
                  <div key={i} className="h-8 bg-gray-200 rounded-full w-24"></div>
                ))}
              </div>
            </div>
            <div>
              <div className="h-4 bg-gray-200 rounded w-20 mb-3"></div>
              <div className="flex flex-wrap gap-2">
                {[1,2,3].map(i => (
                  <div key={i} className="h-8 bg-gray-200 rounded-full w-20"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Metrics Skeleton */}
      <MetricsSkeleton />

      {/* Charts Skeleton */}
      <div className="mb-8">
        <div className="animate-pulse mb-6">
          <div className="h-5 bg-gray-200 rounded w-40 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-80"></div>
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6">
          <ChartSkeleton />
          <ChartSkeleton />
        </div>
        <ChartSkeleton />
      </div>

      {/* Heatmap Skeleton */}
      <HeatmapSkeleton />

      {/* Forecasts Skeleton */}
      <div className="mb-8">
        <div className="animate-pulse mb-6">
          <div className="h-5 bg-gray-200 rounded w-48 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-96"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {[1,2,3].map(i => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="animate-pulse">
                <div className="flex items-center justify-between mb-4">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                </div>
                <div className="text-center">
                  <div className="h-10 bg-gray-200 rounded w-16 mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-32 mx-auto mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-24 mx-auto"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="animate-pulse">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-200 rounded-lg mr-3"></div>
                <div>
                  <div className="h-5 bg-gray-200 rounded w-40 mb-1"></div>
                  <div className="h-4 bg-gray-200 rounded w-56"></div>
                </div>
              </div>
              <div className="h-8 bg-gray-200 rounded-lg w-20"></div>
            </div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Actions Skeleton */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-6 lg:mb-0">
              <div className="h-5 bg-gray-200 rounded w-40 mb-1"></div>
              <div className="h-4 bg-gray-200 rounded w-64"></div>
            </div>
            <div className="flex space-x-3">
              {[1,2,3,4,5].map(i => (
                <div key={i} className="h-10 bg-gray-200 rounded-xl w-20"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ type }) => {
  switch (type) {
    case 'metrics':
      return <MetricsSkeleton />
    case 'chart':
      return <ChartSkeleton />
    case 'heatmap':
      return <HeatmapSkeleton />
    case 'page':
    default:
      return <PageSkeleton />
  }
}