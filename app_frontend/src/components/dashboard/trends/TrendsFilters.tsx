'use client'

import React from 'react'
import { <PERSON>, Sparkles } from 'lucide-react'
import { TrendsFiltersProps, TimePeriod } from '@/types/trends'

const TIME_PERIODS: { value: TimePeriod; label: string }[] = [
  { value: '1D', label: '24h' },
  { value: '7D', label: '7d' },
  { value: '30D', label: '30d' },
  { value: '3M', label: '3m' },
  { value: '1Y', label: '1y' },
  { value: 'custom', label: 'Custom' },
]

export const TrendsFilters: React.FC<TrendsFiltersProps> = ({
  filters,
  onFiltersChange,
  availableAssetTypes,
  availableRegions,
  presets,
}) => {
  const handleAssetTypeToggle = (assetTypeId: string) => {
    const newAssetTypes = filters.assetTypes.includes(assetTypeId)
      ? filters.assetTypes.filter(id => id !== assetTypeId)
      : [...filters.assetTypes, assetTypeId]
    
    onFiltersChange({ assetTypes: newAssetTypes })
  }

  const handleRegionToggle = (regionId: string) => {
    const newRegions = filters.regions.includes(regionId)
      ? filters.regions.filter(id => id !== regionId)
      : [...filters.regions, regionId]
    
    onFiltersChange({ regions: newRegions })
  }

  const handleTimePeriodChange = (timePeriod: TimePeriod) => {
    onFiltersChange({ timePeriod })
  }

  const handlePresetApply = (presetFilters: Partial<typeof filters>) => {
    onFiltersChange(presetFilters)
  }

  const clearAllFilters = () => {
    onFiltersChange({
      assetTypes: [],
      regions: [],
      timePeriod: '30D',
      customDateRange: undefined,
    })
  }

  const hasActiveFilters = filters.assetTypes.length > 0 || filters.regions.length > 0

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-[#484848]">Market Filters</h3>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-[#767676] hover:text-[#484848] transition-colors duration-200"
          >
            Clear all
          </button>
        )}
      </div>

      {/* Filter Categories */}
      <div className="space-y-6">
        {/* Asset Types */}
        <div>
          <label className="text-sm font-medium text-[#484848] mb-3 block">
            Asset Types
          </label>
          <div className="flex flex-wrap gap-2">
            {availableAssetTypes.map((assetType) => {
              const isActive = filters.assetTypes.includes(assetType.id)
              return (
                <button
                  key={assetType.id}
                  onClick={() => handleAssetTypeToggle(assetType.id)}
                  className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    isActive
                      ? 'bg-[#FF5A5F] text-white shadow-sm hover:bg-[#E04E53]'
                      : 'bg-gray-100 text-[#767676] hover:bg-gray-200 hover:text-[#484848]'
                  }`}
                >
                  {assetType.name}
                  {isActive && (
                    <X className="w-3 h-3 ml-2" />
                  )}
                </button>
              )
            })}
          </div>
        </div>

        {/* Regions */}
        <div>
          <label className="text-sm font-medium text-[#484848] mb-3 block">
            Regions
          </label>
          <div className="flex flex-wrap gap-2">
            {availableRegions.map((region) => {
              const isActive = filters.regions.includes(region.id)
              return (
                <button
                  key={region.id}
                  onClick={() => handleRegionToggle(region.id)}
                  className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    isActive
                      ? 'bg-[#FF5A5F] text-white shadow-sm hover:bg-[#E04E53]'
                      : 'bg-gray-100 text-[#767676] hover:bg-gray-200 hover:text-[#484848]'
                  }`}
                >
                  {region.name}
                  {isActive && (
                    <X className="w-3 h-3 ml-2" />
                  )}
                </button>
              )
            })}
          </div>
        </div>

        {/* Time Period & Quick Presets */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Time Period */}
          <div>
            <label className="text-sm font-medium text-[#484848] mb-3 block">
              Time Period
            </label>
            <div className="flex flex-wrap gap-2">
              {TIME_PERIODS.map((period) => {
                const isActive = filters.timePeriod === period.value
                return (
                  <button
                    key={period.value}
                    onClick={() => handleTimePeriodChange(period.value)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-[#FF5A5F] text-white shadow-sm'
                        : 'bg-gray-100 text-[#767676] hover:bg-gray-200 hover:text-[#484848]'
                    }`}
                  >
                    {period.label}
                  </button>
                )
              })}
            </div>
          </div>

          {/* Quick Presets */}
          <div>
            <label className="text-sm font-medium text-[#484848] mb-3 flex items-center">
              <Sparkles className="w-4 h-4 mr-2 text-[#FF5A5F]" />
              Quick Filters
            </label>
            <div className="flex flex-wrap gap-2">
              {presets.map((preset) => (
                <button
                  key={preset.id}
                  onClick={() => handlePresetApply(preset.filters)}
                  className="px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-[#FF5A5F] to-[#FF8E53] text-white hover:from-[#E04E53] hover:to-[#E07B4A] transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  {preset.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-4 border-t border-gray-200">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-[#484848]">Active:</span>
              <div className="flex flex-wrap gap-2">
                {filters.assetTypes.map((assetTypeId) => {
                  const assetType = availableAssetTypes.find(at => at.id === assetTypeId)
                  return assetType ? (
                    <span
                      key={assetTypeId}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#FFB5B7] text-[#484848]"
                    >
                      {assetType.name}
                    </span>
                  ) : null
                })}
                {filters.regions.map((regionId) => {
                  const region = availableRegions.find(r => r.id === regionId)
                  return region ? (
                    <span
                      key={regionId}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#FFB5B7] text-[#484848]"
                    >
                      {region.name}
                    </span>
                  ) : null
                })}
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#FFB5B7] text-[#484848]">
                  {TIME_PERIODS.find(p => p.value === filters.timePeriod)?.label}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}