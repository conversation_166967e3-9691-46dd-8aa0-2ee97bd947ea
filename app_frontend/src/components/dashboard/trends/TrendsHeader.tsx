'use client'

import React from 'react'
import { RefreshCw, Download, Bell, Settings } from 'lucide-react'
import { TrendsHeaderProps } from '@/types/trends'
import { formatDistanceToNow } from 'date-fns'

export const TrendsHeader: React.FC<TrendsHeaderProps> = ({
  lastUpdated,
  isLoading,
  onRefresh,
  onExport,
  onConfigureAlerts,
}) => {
  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        {/* Title Section */}
        <div className="mb-6 lg:mb-0">
          <div className="flex items-center mb-3">
            <div className="w-1 h-8 bg-[#FF5A5F] rounded-full mr-4"></div>
            <h1 className="text-3xl font-semibold text-[#484848] tracking-tight">
              PublishAI Market Trends
            </h1>
          </div>
          <p className="text-sm text-[#767676] flex items-center">
            <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
            Last updated {formatDistanceToNow(lastUpdated, { addSuffix: true })}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className="flex items-center px-4 py-3 text-sm font-medium text-[#767676] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw 
              className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} 
            />
            Refresh
          </button>

          <button
            onClick={() => onExport('csv')}
            className="flex items-center px-4 py-3 text-sm font-medium text-[#767676] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>

          <button
            onClick={onConfigureAlerts}
            className="flex items-center px-4 py-3 text-sm font-medium text-[#767676] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200"
          >
            <Bell className="w-4 h-4 mr-2" />
            Alerts
          </button>

          <button className="flex items-center px-6 py-3 text-sm font-medium text-white bg-[#FF5A5F] hover:bg-[#E04E53] rounded-xl shadow-sm transition-all duration-200 hover:shadow-md">
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </button>
        </div>
      </div>

      {/* Quick Stats Bar */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-[#484848]">24/7</div>
            <div className="text-xs text-[#767676]">Market Monitoring</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-[#484848]">15+</div>
            <div className="text-xs text-[#767676]">Global Markets</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-[#484848]">AI</div>
            <div className="text-xs text-[#767676]">Powered Insights</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-[#484848]">Real-time</div>
            <div className="text-xs text-[#767676]">Data Updates</div>
          </div>
        </div>
      </div>
    </div>
  )
}