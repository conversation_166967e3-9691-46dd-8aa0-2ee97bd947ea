'use client'

import React from 'react'
import { MapPin, Globe, Filter } from 'lucide-react'

interface GeoHeatmapProps {
  data: any[]
  selectedMetric: 'volume' | 'price' | 'sentiment'
  onRegionSelect: (region: string) => void
  onMetricChange: (metric: string) => void
}

export const GeoHeatmap: React.FC<GeoHeatmapProps> = ({
  data,
  selectedMetric,
  onRegionSelect,
  onMetricChange,
}) => {
  const metrics = [
    { value: 'volume', label: 'Volume', icon: '📊', color: 'bg-[#FF5A5F]' },
    { value: 'price', label: 'Price Index', icon: '💰', color: 'bg-[#00A699]' },
    { value: 'sentiment', label: 'Sentiment', icon: '🎯', color: 'bg-[#FC642D]' },
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
        <div className="mb-4 lg:mb-0">
          <div className="flex items-center mb-2">
            <Globe className="w-5 h-5 text-[#FF5A5F] mr-3" />
            <h2 className="text-xl font-semibold text-[#484848]">Geographic Market Heatmap</h2>
          </div>
          <p className="text-sm text-[#767676]">
            Interactive world map showing regional activity and performance data
          </p>
        </div>
        
        {/* Metric Toggle Buttons */}
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-[#767676] mr-2" />
          <div className="flex bg-gray-100 rounded-lg p-1">
            {metrics.map((metric) => (
              <button
                key={metric.value}
                onClick={() => onMetricChange(metric.value)}
                className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  selectedMetric === metric.value
                    ? 'bg-white text-[#484848] shadow-sm'
                    : 'text-[#767676] hover:text-[#484848]'
                }`}
              >
                <span className="mr-2">{metric.icon}</span>
                {metric.label}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Map Container */}
      <div className="relative h-96 bg-gradient-to-br from-gray-50 to-white rounded-lg border-2 border-dashed border-gray-200 overflow-hidden">
        {/* Map Placeholder */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <MapPin className="w-16 h-16 text-[#FF5A5F] mx-auto mb-4" />
            <h3 className="text-lg font-medium text-[#484848] mb-2">Interactive World Map</h3>
            <p className="text-sm text-[#767676] mb-4 max-w-md">
              Real-time geographic visualization showing {selectedMetric} data across global markets
            </p>
            <div className="flex items-center justify-center space-x-6 text-xs text-[#767676]">
              <span>• 15+ Countries</span>
              <span>• Real-time Updates</span>
              <span>• Click to Drill Down</span>
            </div>
          </div>
        </div>

        {/* Sample Data Points */}
        <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-[#FF5A5F] rounded-full animate-pulse"></div>
        <div className="absolute top-1/3 right-1/3 w-4 h-4 bg-[#FF8E53] rounded-full animate-pulse delay-300"></div>
        <div className="absolute bottom-1/3 left-1/2 w-2 h-2 bg-[#FFB5B7] rounded-full animate-pulse delay-700"></div>
        <div className="absolute top-1/2 right-1/4 w-5 h-5 bg-[#E04E53] rounded-full animate-pulse delay-1000"></div>
      </div>
      
      {/* Legend and Stats */}
      <div className="mt-6 flex flex-col lg:flex-row lg:items-center lg:justify-between">
        {/* Color Legend */}
        <div className="flex items-center space-x-4 mb-4 lg:mb-0">
          <span className="text-sm font-medium text-[#484848]">Intensity:</span>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-[#767676]">Low</span>
            <div className="flex space-x-1">
              <div className="w-4 h-4 rounded bg-[#FFB5B7]"></div>
              <div className="w-4 h-4 rounded bg-[#FF8E53]"></div>
              <div className="w-4 h-4 rounded bg-[#FF5A5F]"></div>
              <div className="w-4 h-4 rounded bg-[#E04E53]"></div>
              <div className="w-4 h-4 rounded bg-[#C13515]"></div>
            </div>
            <span className="text-xs text-[#767676]">High</span>
          </div>
        </div>
        
        {/* Summary Stats */}
        <div className="flex items-center space-x-6 text-sm">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            <span className="text-[#767676]">{data.length} active regions</span>
          </div>
          <div className="flex items-center">
            <div className="w-2 h-2 bg-[#FF5A5F] rounded-full mr-2"></div>
            <span className="text-[#767676]">Live data</span>
          </div>
        </div>
      </div>

      {/* Regional Highlights */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-sm font-medium text-[#484848] mb-3">Top Performing Regions</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {['North America', 'Europe', 'Asia Pacific'].map((region, index) => (
            <div 
              key={region}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
              onClick={() => onRegionSelect(region.toLowerCase().replace(' ', '-'))}
            >
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  index === 0 ? 'bg-[#FF5A5F]' : index === 1 ? 'bg-[#FF8E53]' : 'bg-[#FFB5B7]'
                }`}></div>
                <span className="text-sm font-medium text-[#484848]">{region}</span>
              </div>
              <span className="text-sm text-[#767676]">
                {index === 0 ? '+15.2%' : index === 1 ? '+8.7%' : '+5.3%'}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}