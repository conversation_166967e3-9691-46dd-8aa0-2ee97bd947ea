'use client'

import React from 'react'
import { RefreshCw, TrendingUp, TrendingDown, Minus, Sparkles, Brain, CheckCircle, Clock } from 'lucide-react'
import { TrendsForecastsProps } from '@/types/trends'

const SentimentGauge: React.FC<{ 
  value: number; 
  trend: 'up' | 'down' | 'stable';
  confidence: number;
}> = ({ value, trend, confidence }) => {
  const normalizedValue = Math.max(0, Math.min(100, (value + 100) / 2)) // Convert -100,100 to 0,100
  const rotation = (normalizedValue * 180) / 100 - 90 // Convert to degrees (-90 to 90)
  
  const getSentimentColor = (val: number) => {
    if (val > 20) return '#00A699' // Airbnb green
    if (val < -20) return '#C13515' // Airbnb red
    return '#FC642D' // Airbnb orange
  }

  const getSentimentLabel = (val: number) => {
    if (val > 40) return 'Very Bullish'
    if (val > 20) return 'Bullish'
    if (val > -20) return 'Neutral'
    if (val > -40) return 'Bearish'
    return 'Very Bearish'
  }

  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus

  return (
    <div className="relative w-full max-w-xs mx-auto">
      <svg viewBox="0 0 200 120" className="w-full h-32">
        {/* Background arc */}
        <path
          d="M 20 100 A 80 80 0 0 1 180 100"
          fill="none"
          stroke="#E5E7EB"
          strokeWidth="8"
          strokeLinecap="round"
        />
        {/* Colored progress arc */}
        <path
          d="M 20 100 A 80 80 0 0 1 180 100"
          fill="none"
          stroke={getSentimentColor(value)}
          strokeWidth="8"
          strokeLinecap="round"
          strokeDasharray={`${(normalizedValue * 251.2) / 100} 251.2`}
        />
        {/* Needle */}
        <line
          x1="100"
          y1="100"
          x2={100 + 60 * Math.cos((rotation * Math.PI) / 180)}
          y2={100 + 60 * Math.sin((rotation * Math.PI) / 180)}
          stroke="#484848"
          strokeWidth="3"
          strokeLinecap="round"
        />
        <circle cx="100" cy="100" r="6" fill="#484848" />
        <circle cx="100" cy="100" r="3" fill="white" />
      </svg>
      
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 text-center">
        <div className="text-2xl font-bold text-[#484848] mb-1">
          {value > 0 ? '+' : ''}{value}
        </div>
        <div className="flex items-center justify-center text-sm text-[#767676] mb-1">
          <TrendIcon className="w-3 h-3 mr-1" />
          <span>{getSentimentLabel(value)}</span>
        </div>
        <div className="text-xs text-[#B0B0B0]">
          {confidence}% confidence
        </div>
      </div>
    </div>
  )
}

export const AIForecasts: React.FC<TrendsForecastsProps> = ({
  forecasts,
  commentary,
  confidenceLevel,
  onCommentaryRefresh,
}) => {
  return (
    <div className="mb-8">
      {/* Section Header */}
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Brain className="w-5 h-5 text-[#FF5A5F] mr-3" />
          <h2 className="text-xl font-semibold text-[#484848]">AI-Powered Forecasts</h2>
          <div className="ml-3 px-2 py-1 bg-gradient-to-r from-[#FF5A5F] to-[#FF8E53] text-white text-xs font-medium rounded-full">
            BETA
          </div>
        </div>
        <p className="text-sm text-[#767676]">
          Machine learning predictions and market sentiment analysis powered by advanced algorithms
        </p>
      </div>
      
      {/* Forecast Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Price Forecast */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-[#767676]">Price Forecast</h3>
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-green-600" />
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">+15%</div>
            <div className="text-sm text-[#767676] mb-1">±3% confidence interval</div>
            <div className="text-xs text-[#B0B0B0] mb-4">(Next 3 months)</div>
            <div className="bg-green-50 rounded-lg p-3">
              <div className="text-xs text-green-700 font-medium">Strong Upward Trend</div>
              <div className="text-xs text-green-600 mt-1">Based on 15 market factors</div>
            </div>
          </div>
        </div>

        {/* Yield Forecast */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-[#767676]">Yield Forecast</h3>
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-blue-600" />
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">6.2%</div>
            <div className="text-sm text-[#767676] mb-1">±0.5% confidence interval</div>
            <div className="text-xs text-[#B0B0B0] mb-4">(12-month average)</div>
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-xs text-blue-700 font-medium">Stable Growth</div>
              <div className="text-xs text-blue-600 mt-1">Historical pattern analysis</div>
            </div>
          </div>
        </div>

        {/* Sentiment Gauge */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-[#767676]">Market Sentiment</h3>
            <div className="w-8 h-8 bg-[#FFB5B7] rounded-lg flex items-center justify-center">
              <Sparkles className="w-4 h-4 text-[#FF5A5F]" />
            </div>
          </div>
          <SentimentGauge 
            value={forecasts.sentimentIndex.value || 75} 
            trend={forecasts.sentimentIndex.trend || 'up'}
            confidence={forecasts.sentimentIndex.confidence || 82}
          />
        </div>
      </div>

      {/* AI Commentary Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-r from-[#FF5A5F] to-[#FF8E53] rounded-lg flex items-center justify-center mr-3">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-[#484848]">AI Market Commentary</h3>
              <p className="text-sm text-[#767676]">Generated insights from multiple data sources</p>
            </div>
          </div>
          <button
            onClick={onCommentaryRefresh}
            className="flex items-center px-4 py-2 text-sm font-medium text-[#767676] bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
        
        <div className="space-y-4">
          {/* Commentary Content */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="prose prose-sm text-[#484848] leading-relaxed">
              {commentary.content || (
                <>
                  <p className="mb-3">
                    <strong>Market momentum strengthening in urban areas.</strong> Rising institutional 
                    interest driving volume increases across major metropolitan regions. Short-term outlook 
                    remains positive with continued growth expected through Q2.
                  </p>
                  <p className="mb-3">
                    Key drivers include increased liquidity from institutional investors, favorable 
                    regulatory developments, and strong fundamentals in the residential sector. 
                    Geographic analysis shows particular strength in North American and European markets.
                  </p>
                  <p className="mb-0">
                    Risk factors to monitor include potential policy changes and global economic 
                    indicators that could impact investor sentiment in the coming quarters.
                  </p>
                </>
              )}
            </div>
          </div>
          
          {/* Metadata and Validation */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-6 mb-4 lg:mb-0">
              <div className="flex items-center text-sm">
                <div className="w-4 h-4 bg-[#FF5A5F] rounded-full mr-2"></div>
                <span className="text-[#767676]">Confidence: {commentary.confidence || confidenceLevel}%</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-[#767676]">Sources: {commentary.sources?.length || 12}</span>
              </div>
              <div className={`flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                commentary.validatedSchema 
                  ? 'bg-green-100 text-green-700' 
                  : 'bg-yellow-100 text-yellow-700'
              }`}>
                {commentary.validatedSchema ? (
                  <CheckCircle className="w-3 h-3 mr-1" />
                ) : (
                  <Clock className="w-3 h-3 mr-1" />
                )}
                {commentary.validatedSchema ? 'PydanticAI Validated' : 'Validation Pending'}
              </div>
            </div>
            <div className="text-xs text-[#B0B0B0]">
              Last updated: {commentary.lastUpdated?.toLocaleTimeString() || 'Never'}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}