'use client'

import React from 'react'
import { TrendingUp, TrendingDown, DollarSign, Clock, BarChart3, Activity } from 'lucide-react'
import { TrendsMetricsProps } from '@/types/trends'

interface MetricCardProps {
  title: string
  value: string
  change: number
  icon: React.ReactNode
  loading: boolean
  description: string
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  loading,
  description,
}) => {
  const isPositive = change >= 0
  const TrendIcon = isPositive ? TrendingUp : TrendingDown

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="w-10 h-10 bg-gray-200 rounded-xl"></div>
          </div>
          <div className="h-8 bg-gray-200 rounded w-20 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-32 mb-1"></div>
          <div className="h-3 bg-gray-200 rounded w-24"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 cursor-pointer group">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-[#767676] group-hover:text-[#484848] transition-colors duration-200">
          {title}
        </h3>
        <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center group-hover:bg-[#FFB5B7] transition-colors duration-200">
          {icon}
        </div>
      </div>
      
      {/* Main Value */}
      <div className="mb-3">
        <p className="text-2xl font-semibold text-[#484848] leading-tight">{value}</p>
        <p className="text-xs text-[#767676] mt-1">{description}</p>
      </div>
      
      {/* Change Indicator */}
      <div className="flex items-center">
        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isPositive 
            ? 'bg-green-100 text-green-700' 
            : 'bg-red-100 text-red-700'
        }`}>
          <TrendIcon className="w-3 h-3 mr-1" />
          {isPositive ? '+' : ''}{change.toFixed(1)}%
        </div>
        <span className="text-xs text-[#B0B0B0] ml-2">vs previous period</span>
      </div>

      {/* Trend Sparkline Placeholder */}
      <div className="mt-4 h-6 bg-gradient-to-r from-transparent via-[#FFB5B7] to-transparent opacity-20 rounded-full"></div>
    </div>
  )
}

const formatCurrency = (value: number): string => {
  if (value >= 1000000000) {
    return `$${(value / 1000000000).toFixed(1)}B`
  } else if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(1)}K`
  }
  return `$${value.toFixed(0)}`
}

const formatDuration = (months: number): string => {
  if (months >= 12) {
    const years = months / 12
    return `${years.toFixed(1)}y`
  }
  return `${months.toFixed(0)}m`
}

export const MetricsGrid: React.FC<TrendsMetricsProps> = ({
  metrics,
  loading,
  timeframe,
}) => {
  const metricCards = [
    {
      title: 'Trading Volume',
      value: formatCurrency(metrics.totalVolume),
      change: metrics.volumeChange,
      icon: <BarChart3 className="w-5 h-5 text-[#767676]" />,
      description: 'Total token transactions',
    },
    {
      title: 'Average Price Index',
      value: formatCurrency(metrics.avgPrice),
      change: metrics.priceChange,
      icon: <DollarSign className="w-5 h-5 text-[#767676]" />,
      description: 'Weighted average across assets',
    },
    {
      title: 'Holding Duration',
      value: formatDuration(metrics.avgHoldingDuration),
      change: metrics.durationChange,
      icon: <Clock className="w-5 h-5 text-[#767676]" />,
      description: 'Average investment period',
    },
  ]

  return (
    <div className="mb-8">
      {/* Section Header */}
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Activity className="w-5 h-5 text-[#FF5A5F] mr-3" />
          <h2 className="text-xl font-semibold text-[#484848]">Key Performance Metrics</h2>
        </div>
        <p className="text-sm text-[#767676]">
          Real-time market performance indicators for the {timeframe.toLowerCase()} period
        </p>
      </div>
      
      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {metricCards.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            change={metric.change}
            icon={metric.icon}
            loading={loading}
            description={metric.description}
          />
        ))}
      </div>

      {/* Additional Insights Bar */}
      <div className="mt-6 bg-gradient-to-r from-[#FF5A5F] to-[#FF8E53] rounded-xl p-4">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
              <TrendingUp className="w-4 h-4" />
            </div>
            <div>
              <div className="text-sm font-medium">Market Sentiment</div>
              <div className="text-xs opacity-90">Overall market confidence is strong</div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold">Bullish</div>
            <div className="text-xs opacity-90">75% confidence</div>
          </div>
        </div>
      </div>
    </div>
  )
}