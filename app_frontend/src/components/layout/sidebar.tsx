"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useUser } from "@/hooks/api/use-auth";
import {
  LayoutDashboard,
  BookOpen,
  BarChart3,
  TrendingUp,
  Upload,
  Settings,
  HelpCircle,
  X,
  PlusCircle,
  FileText,
  DollarSign,
} from "lucide-react";

interface SidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
}

const sidebarItems = [
  {
    title: "Main",
    items: [
      { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
      { name: "My Books", href: "/dashboard/books", icon: BookOpen },
      { name: "Analytics", href: "/dashboard/analytics", icon: BarChart3 },
      { name: "Trends", href: "/dashboard/trends", icon: TrendingUp },
    ],
  },
  {
    title: "Publishing",
    items: [
      { name: "New Book", href: "/dashboard/books/new", icon: PlusCircle },
      { name: "Manuscripts", href: "/dashboard/manuscripts", icon: FileText },
      { name: "Publications", href: "/dashboard/publications", icon: Upload },
      { name: "Revenue", href: "/dashboard/revenue", icon: DollarSign },
    ],
  },
  {
    title: "Support",
    items: [
      { name: "Settings", href: "/dashboard/settings", icon: Settings },
      { name: "Help & Docs", href: "/dashboard/help", icon: HelpCircle },
    ],
  },
];

export function Sidebar({ isOpen = true, onClose }: SidebarProps) {
  const pathname = usePathname();
  const { data: user } = useUser();

  // Get user's subscription tier with fallback to 'free'
  const subscriptionTier = user?.subscription_tier || "free";

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-surface transition-transform duration-300 md:relative md:translate-x-0",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex h-full flex-col">
          {/* Mobile header */}
          <div className="flex h-16 items-center justify-between border-b px-4 md:hidden">
            <span className="font-display text-lg font-semibold">Menu</span>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-5 w-5" />
              <span className="sr-only">Close sidebar</span>
            </Button>
          </div>

          {/* Navigation */}
          <ScrollArea className="flex-1 px-3 py-4">
            <nav className="space-y-6">
              {sidebarItems.map((section) => (
                <div key={section.title}>
                  <h3 className="mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                    {section.title}
                  </h3>
                  <div className="space-y-1">
                    {section.items.map((item) => {
                      const isActive = pathname === item.href;
                      const Icon = item.icon;
                      return (
                        <Link
                          key={item.name}
                          href={item.href}
                          className={cn(
                            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                            isActive
                              ? "bg-primary/10 text-primary"
                              : "text-muted-foreground hover:bg-muted hover:text-foreground"
                          )}
                        >
                          <Icon className="h-5 w-5" />
                          {item.name}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              ))}
            </nav>
          </ScrollArea>

          {/* Footer - Upgrade prompt */}
          {subscriptionTier !== "enterprise" && (
            <div className="border-t p-4">
              <div className="rounded-lg bg-primary/10 p-3">
                {subscriptionTier === "free" ? (
                  <>
                    <h4 className="text-sm font-semibold">Upgrade to Pro</h4>
                    <p className="mt-1 text-xs text-muted-foreground">
                      Unlock advanced features and analytics
                    </p>
                    <Button size="sm" className="mt-3 w-full">
                      Upgrade Now
                    </Button>
                  </>
                ) : subscriptionTier === "pro" ? (
                  <>
                    <h4 className="text-sm font-semibold">
                      Upgrade to Enterprise
                    </h4>
                    <p className="mt-1 text-xs text-muted-foreground">
                      Get premium support and enterprise features
                    </p>
                    <Button size="sm" className="mt-3 w-full">
                      Go Enterprise
                    </Button>
                  </>
                ) : null}
              </div>
            </div>
          )}
        </div>
      </aside>
    </>
  );
}