import { ReactNode } from "react"
import { Breadcrumb } from "./breadcrumb"
import { Heading, Text } from "@/components/ui/typography"
import { But<PERSON> } from "@/components/ui/button"
import { useLayout } from "@/contexts/layout-context"
import { cn } from "@/lib/utils"
import { Menu } from "lucide-react"

interface PageHeaderProps {
  title: string
  description?: string
  breadcrumb?: boolean
  actions?: ReactNode
  className?: string
}

export function PageHeader({
  title,
  description,
  breadcrumb = true,
  actions,
  className,
}: PageHeaderProps) {
  const { toggleSidebar } = useLayout()
  
  return (
    <div className={cn("space-y-4 pb-6", className)}>
      {breadcrumb && <Breadcrumb />}
      
      <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
        <div className="flex items-center gap-3">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={toggleSidebar}
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle menu</span>
          </Button>
          
          <div className="space-y-1">
            <Heading as="h1" variant="h3">
              {title}
            </Heading>
            {description && (
              <Text variant="muted" className="max-w-2xl">
                {description}
              </Text>
            )}
          </div>
        </div>
        
        {actions && (
          <div className="flex items-center gap-2 flex-shrink-0">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}