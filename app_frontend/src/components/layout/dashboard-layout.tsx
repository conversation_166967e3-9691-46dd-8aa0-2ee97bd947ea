"use client"

import { Sidebar } from "./sidebar"
import { Footer } from "./footer"
import { UserHeader } from "./user-header"
import { ErrorBoundary } from "./error-boundary"
import { LayoutProvider, useLayout } from "@/contexts/layout-context"
import { cn } from "@/lib/utils"

interface DashboardLayoutProps {
  children: React.ReactNode
  className?: string
  showFooter?: boolean
}

function DashboardLayoutInner({ 
  children, 
  className,
  showFooter = false
}: DashboardLayoutProps) {
  const { sidebarOpen, setSidebarOpen } = useLayout()

  return (
    <div className="min-h-screen bg-background">
      <div className="flex h-screen">
        <Sidebar 
          isOpen={sidebarOpen} 
          onClose={() => setSidebarOpen(false)} 
        />
        
        <div className="flex flex-1 flex-col">
          <UserHeader onMenuClick={() => setSidebarOpen(true)} />
          
          <main className={cn(
            "flex-1 overflow-y-auto",
            className
          )}>
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </main>
        </div>
      </div>
      
      {showFooter && <Footer />}
    </div>
  )
}

export function DashboardLayout(props: DashboardLayoutProps) {
  return (
    <LayoutProvider>
      <DashboardLayoutInner {...props} />
    </LayoutProvider>
  )
}