# Publish AI Frontend

A modern Next.js 15 application for the Publish AI platform - an AI-powered book generation and publishing system.

## 🚀 Features

- **Next.js 15** with React 19 and Turbopack for lightning-fast development
- **Authentication** - Complete NextAuth integration with OAuth providers
- **Role-Based Access** - Admin, editor, author, and user roles with protected routes
- **Dashboard** - Comprehensive analytics and book management interface
- **UI Components** - 60+ accessible components with Tailwind CSS v4
- **TypeScript** - Full type safety throughout the application
- **Testing** - Jest unit tests and Playwright E2E testing

## 🛠️ Tech Stack

- **Framework**: Next.js 15.3.4
- **React**: 19.1.0
- **Styling**: Tailwind CSS v4
- **Authentication**: NextAuth.js
- **State Management**: React Query (TanStack Query)
- **Testing**: Jest, Playwright
- **Type Safety**: TypeScript
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Git

## 🚀 Quick Start

1. **<PERSON><PERSON> and navigate to the project**
   ```bash
   git clone <repository-url>
   cd app_frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or use the comprehensive installer
   ./install-dependencies.sh
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   ```
   http://localhost:3000
   ```

## 📁 Project Structure

```
app_frontend/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── auth/           # Authentication pages
│   │   ├── dashboard/      # Dashboard and admin pages
│   │   └── api/            # API routes
│   ├── components/         # React components
│   │   ├── ui/            # Reusable UI components
│   │   ├── auth/          # Authentication components
│   │   ├── layout/        # Layout components
│   │   └── books/         # Book-specific components
│   ├── lib/               # Utilities and helpers
│   ├── hooks/             # Custom React hooks
│   ├── hoc/               # Higher-order components
│   ├── config/            # Configuration files
│   └── types/             # TypeScript definitions
├── public/                # Static assets
└── package.json
```

## 🔐 Authentication

The application supports multiple authentication methods:

- **Email/Password** - Traditional credentials
- **OAuth Providers** - Google, Apple, GitHub
- **Role-Based Access** - Different permissions per user type

### User Roles

- **Guest** - Read-only access
- **User** - Basic authenticated user
- **Author** - Can create and manage books
- **Editor** - Can review and edit content
- **Admin** - Full system access

## 🎨 UI Components

Over 60 accessible UI components built with:

- **Radix UI** primitives for accessibility
- **Tailwind CSS v4** for styling
- **Class Variance Authority** for component variants
- **Lucide React** for icons

Key component categories:
- Forms (Input, Select, Checkbox, etc.)
- Layout (Container, Grid, Card, etc.)
- Navigation (Breadcrumb, Tabs, etc.)
- Feedback (Toast, Loading, Error states)
- Data Display (Tables, Charts, Stats)

## 🧪 Testing

### Unit Tests
```bash
npm test                 # Run Jest tests
npm run test:watch      # Watch mode
npm run test:coverage   # Coverage report
```

### E2E Tests
```bash
npm run test:e2e        # Run Playwright tests
npm run test:e2e:ui     # Interactive mode
```

## 🔧 Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript checking
```

### Code Quality

- **ESLint** - Code linting with Next.js config
- **TypeScript** - Type checking
- **Prettier** - Code formatting (via ESLint)

## 🚀 Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

### Environment Variables

Required environment variables:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key

# OAuth Providers (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Backend API
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## 📱 Features Overview

### Dashboard
- Analytics and performance metrics
- Book management interface
- User and role management (admin)
- Real-time data visualization

### Book Management
- Multi-step book creation wizard
- AI-powered content generation
- Cover design tools
- Publishing workflow

### Authentication
- Secure login/register flows
- OAuth provider integration
- Role-based route protection
- Session management

## 🛡️ Security

- **Route Protection** - Server and client-side guards
- **CSRF Protection** - Built-in NextAuth security
- **Content Security Policy** - Configured headers
- **Input Validation** - Zod schema validation
- **Rate Limiting** - API protection middleware

## 🔄 State Management

- **React Query** - Server state management
- **React Hook Form** - Form state handling
- **NextAuth** - Authentication state
- **Local Storage** - Client preferences

## 📚 Documentation

- **Storybook** - Component documentation
- **TypeScript** - Type definitions
- **JSDoc** - Function documentation
- **README** - Setup and usage guides

## 🐛 Troubleshooting

### Common Issues

1. **Module not found errors**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **TypeScript errors**
   ```bash
   npm run type-check
   ```

3. **Build failures**
   ```bash
   npm run lint
   npm run build
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if needed
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🔗 Related

- [Backend Repository](../README.md) - Python FastAPI backend