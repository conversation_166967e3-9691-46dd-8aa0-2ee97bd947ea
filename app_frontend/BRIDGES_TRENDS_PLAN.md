# Bridges Market Trends Page - Airbnb Design Architecture

## 🎨 Design System - Airbnb Style

### Color Palette
```css
/* Primary Airbnb Colors */
--airbnb-coral: #FF5A5F        /* Primary brand color */
--airbnb-deep-coral: #E04E53   /* Hover states */
--airbnb-light-coral: #FFB5B7  /* Subtle accents */

/* Neutral Text Colors */
--airbnb-charcoal: #484848     /* Primary text */
--airbnb-gray: #767676         /* Secondary text */
--airbnb-light-gray: #B0B0B0   /* Disabled text */

/* Background Colors */
--airbnb-white: #FFFFFF        /* Cards, modals */
--airbnb-background: #F7F7F7   /* Page background */
--airbnb-off-white: #FAFAFA    /* Subtle sections */

/* Accent Colors */
--airbnb-green: #00A699        /* Success states */
--airbnb-orange: #FC642D       /* Warning states */
--airbnb-red: #C13515          /* Error states */
```

### Typography & Spacing
```css
/* Airbnb Typography Scale */
h1: text-3xl/4xl font-semibold tracking-tight
h2: text-2xl/3xl font-semibold 
h3: text-xl/2xl font-medium
body: text-base font-normal
small: text-sm text-gray-600

/* Airbnb Spacing Scale */
xs: 0.5rem (8px)
sm: 0.75rem (12px)  
md: 1rem (16px)
lg: 1.5rem (24px)
xl: 2rem (32px)
2xl: 3rem (48px)
```

### Component Design Patterns
- **Rounded Corners**: `rounded-xl` (12px) for cards, `rounded-2xl` (16px) for major sections
- **Shadows**: `shadow-sm` for subtle depth, `shadow-lg` for floating elements
- **Spacing**: Generous padding `p-6`/`p-8`, consistent gaps `gap-6`
- **Borders**: Subtle `border border-gray-200` when needed

## 📋 Section-by-Section Architecture

### 1. Page Header Component
```typescript
// BridgesTrendsHeader.tsx
interface BridgesTrendsHeaderProps {
  lastUpdated: Date
  isLoading: boolean
  onRefresh: () => void
  onExport: (format: 'csv' | 'json' | 'pdf') => void
  onConfigureAlerts: () => void
}
```

**Visual Design:**
- Clean white background with subtle shadow
- Large title with Airbnb coral accent
- Right-aligned action buttons with hover states
- Last updated timestamp in muted gray

**Tailwind Classes:**
```css
bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-6
text-3xl font-semibold text-airbnb-charcoal
text-sm text-airbnb-gray
```

### 2. Interactive Filters Bar
```typescript
// BridgesTrendsFilters.tsx
interface BridgesTrendsFiltersProps {
  filters: TrendsFilters
  onFiltersChange: (filters: Partial<TrendsFilters>) => void
  presets: FilterPreset[]
}
```

**Visual Design:**
- Horizontal scrollable filter pills
- Active filters in coral, inactive in gray outline
- Smooth transitions on state changes
- Clear all option on the right

**Tailwind Classes:**
```css
bg-white rounded-xl shadow-sm p-6 mb-6
bg-airbnb-coral text-white hover:bg-airbnb-deep-coral (active)
border border-gray-300 text-gray-700 hover:border-gray-400 (inactive)
```

### 3. Key Metrics Dashboard
```typescript
// BridgesMetricsGrid.tsx
interface MetricCardProps {
  title: string
  value: string | number
  change: number
  trend: 'up' | 'down' | 'stable'
  icon: ReactNode
  loading: boolean
}
```

**Visual Design:**
- 3-column responsive grid (stacks on mobile)
- Large metric values with trend indicators
- Coral accent for positive trends, subtle red for negative
- Icons in soft gray backgrounds

**Tailwind Classes:**
```css
grid grid-cols-1 md:grid-cols-3 gap-6
bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow
text-2xl font-semibold text-airbnb-charcoal
text-airbnb-green (positive) / text-red-500 (negative)
```

### 4. Interactive Charts Section
```typescript
// BridgesChartsGrid.tsx
interface ChartsGridProps {
  volumeData: ChartDataPoint[]
  priceData: ChartDataPoint[]
  yieldCurveData: YieldPoint[]
  onChartInteraction: (event: ChartEvent) => void
}
```

**Charts to Include:**
- **Volume Trends**: Line chart with area fill
- **Price Index**: Multi-line chart with asset type breakdown
- **Yield Curve**: Area chart with confidence intervals
- **Regional Performance**: Bar chart comparison

**Visual Design:**
- 2x2 grid on desktop, stacked on mobile
- Consistent chart colors using Airbnb palette
- Hover tooltips with white backgrounds and coral accents
- Chart legends with interactive toggle

**Recharts Configuration:**
```typescript
<LineChart>
  <Line stroke="#FF5A5F" strokeWidth={3} dot={{ fill: '#FF5A5F', r: 4 }} />
  <Tooltip contentStyle={{ 
    backgroundColor: '#FFFFFF', 
    border: '1px solid #E5E7EB',
    borderRadius: '12px',
    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
  }} />
</LineChart>
```

### 5. Geographic Heatmap
```typescript
// BridgesGeoHeatmap.tsx
interface GeoHeatmapProps {
  data: GeographicDataPoint[]
  selectedMetric: 'volume' | 'price' | 'sentiment'
  onRegionSelect: (region: string) => void
}
```

**Visual Design:**
- Interactive world map with coral heat gradients
- Metric toggle buttons at top
- Zoom and pan capabilities
- Region details on hover/click

**Color Gradient:**
```css
Low: #FFF1F1 → Medium: #FF5A5F → High: #C13515
```

### 6. AI Forecasts Dashboard
```typescript
// BridgesAIForecasts.tsx
interface AIForecastsProps {
  priceForecasts: PriceForecast[]
  yieldPredictions: YieldForecast[]
  sentimentIndex: SentimentData
  commentary: AICommentary
}
```

**Components:**
- **Price Prediction Cards**: Large numbers with confidence intervals
- **Sentiment Gauge**: Custom SVG with coral gradients
- **Yield Forecasts**: Mini line charts with trend arrows
- **AI Commentary**: Expandable text with validation badges

**Sentiment Gauge Design:**
```typescript
const SentimentGauge = ({ value, confidence }) => (
  <svg viewBox="0 0 200 120" className="w-full h-32">
    {/* Background arc in light gray */}
    <path d="M 20 100 A 80 80 0 0 1 180 100" 
          stroke="#E5E7EB" strokeWidth="8" />
    {/* Progress arc in coral gradient */}
    <path d="M 20 100 A 80 80 0 0 1 180 100"
          stroke="url(#coralGradient)" strokeWidth="8" />
    {/* Needle indicator */}
  </svg>
)
```

### 7. Actions & Export Bar
```typescript
// BridgesActionsBar.tsx
interface ActionsBarProps {
  onExport: (format: ExportFormat) => Promise<void>
  onSetAlert: () => void
  onShareReport: () => void
  onAPIAccess: () => void
}
```

**Visual Design:**
- Fixed bottom bar on mobile, inline on desktop
- Primary export button in coral
- Secondary actions in outlined style
- Loading states for async operations

## 🔄 Data Flow Architecture

### API Endpoints
```
GET  /api/dashboard/trends/metrics       # Key performance metrics
GET  /api/dashboard/trends/charts        # Chart data with filtering
GET  /api/dashboard/trends/geographic    # Geographic heatmap data
GET  /api/dashboard/trends/forecasts     # AI predictions
POST /api/dashboard/trends/commentary    # Generate AI commentary
POST /api/dashboard/trends/export        # Export data
POST /api/dashboard/trends/alerts        # Configure alerts
```

### Data Flow Pattern
```
Supabase DB → Redis Cache (5min) → Next.js API → React Query → Components
     ↓              ↓                    ↓            ↓           ↓
Real-time      Background         REST endpoints   Client      UI Updates
WebSocket      invalidation       with filtering   state       + animations
```

### React Query Setup
```typescript
// hooks/api/useBridgesTrends.ts
export const useBridgesTrends = (filters: TrendsFilters) => {
  return useQuery({
    queryKey: ['bridges-trends', filters],
    queryFn: () => fetchBridgesTrends(filters),
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchInterval: 1000 * 60 * 5, // 5 minutes auto-refresh
  })
}
```

## 📱 Responsive Layout Structure

### Desktop Layout (1200px+)
```
┌─────────────────────────────────────────────────────────────────────┐
│ 🏠 Bridges Market Trends                    [🔄] [📊] [🔔] [⚙️]    │
├─────────────────────────────────────────────────────────────────────┤
│ [Residential ●] [Commercial ○] [Land ○] | [Americas ●] [Europe ●]    │
├───────────────┬───────────────┬───────────────┬─────────────────────┤
│   Volume      │  Price Index  │ Hold Duration │   Sentiment         │
│  $2.4M ↗ 12%  │ 245 ↘ 3%      │  18m ↗ 5%     │    Bullish 75%     │
├───────────────┴───────────────┴───────────────┴─────────────────────┤
│ ┌─────────────────────┐  ┌───────────────────────────────────────┐  │
│ │   Volume Trends     │  │         Geographic Heatmap            │  │
│ │                     │  │                                       │  │
│ │  [Recharts Line]    │  │    [Interactive World Map]           │  │
│ │                     │  │                                       │  │
│ └─────────────────────┘  └───────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐  ┌───────────────────────────────────────┐  │
│ │   Price Index       │  │         Yield Curve                   │  │
│ │                     │  │                                       │  │
│ │ [Multi-line Chart]  │  │  [Area Chart with Confidence Bands]  │  │
│ │                     │  │                                       │  │
│ └─────────────────────┘  └───────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────────────┤
│                           AI FORECASTS                              │
│ ┌──────────────┐ ┌──────────────┐ ┌─────────────────────────────┐   │
│ │Price Forecast│ │Yield Forecast│ │      Sentiment Gauge        │   │
│ │    +15%      │ │    6.2%      │ │                             │   │
│ │   ±3% conf   │ │   ±0.5%      │ │    [Circular Gauge]         │   │
│ │  (Next 3M)   │ │  (12M Avg)   │ │      Bullish 75%            │   │
│ └──────────────┘ └──────────────┘ └─────────────────────────────┘   │
│                                                                     │
│ 📝 AI Commentary: "Market momentum strengthening in urban areas..." │
├─────────────────────────────────────────────────────────────────────┤
│ [🔔 Set Alert] [📤 CSV] [📊 JSON] [📄 PDF] [🔗 API] [📋 Share]     │
└─────────────────────────────────────────────────────────────────────┘
```

### Mobile Layout (< 768px)
- Single column stack
- Collapsible filters
- Horizontal scroll charts
- Bottom action sheet

## 🛠️ Component Development Plan

### Phase 1: Core Structure (Priority: High)
```typescript
// 1. Layout & Navigation
components/dashboard/trends/
├── BridgesTrendsPage.tsx           # Main orchestrator
├── BridgesTrendsHeader.tsx         # Header with actions
├── BridgesTrendsFilters.tsx        # Interactive filters
└── BridgesTrendsLayout.tsx         # Responsive container

// 2. Data Display
├── BridgesMetricsGrid.tsx          # KPI cards
├── BridgesChartsGrid.tsx           # Chart container
└── BridgesGeoHeatmap.tsx           # Geographic visualization
```

### Phase 2: Interactive Features (Priority: High)
```typescript
// 3. Charts & Visualizations  
components/dashboard/trends/charts/
├── BridgesVolumeChart.tsx          # Trading volume trends
├── BridgesPriceChart.tsx           # Price index multi-line
├── BridgesYieldCurve.tsx           # Yield curve with confidence
├── BridgesRegionalBars.tsx         # Regional comparison
└── BridgesChartTooltip.tsx         # Custom tooltip component

// 4. AI & Forecasts
components/dashboard/trends/ai/
├── BridgesAIForecasts.tsx          # Forecast dashboard
├── BridgesSentimentGauge.tsx       # Custom SVG gauge
├── BridgesPricePrediction.tsx      # Price forecast cards
└── BridgesAICommentary.tsx         # Commentary with validation
```

### Phase 3: Advanced Features (Priority: Medium)
```typescript
// 5. Export & Alerts
components/dashboard/trends/actions/
├── BridgesExportModal.tsx          # Export configuration
├── BridgesAlertSetup.tsx           # Alert configuration
├── BridgesShareDialog.tsx          # Share functionality  
└── BridgesAPIAccess.tsx            # API documentation

// 6. Utilities & Hooks
hooks/dashboard/
├── useBridgesTrendsData.ts         # Main data fetching
├── useBridgesExport.ts             # Export functionality
├── useBridgesAlerts.ts             # Alert management
└── useBridgesWebSocket.ts          # Real-time updates
```

## 🎨 Airbnb Tailwind Class Library

### Button Variants
```css
/* Primary Coral Button */
.btn-coral {
  @apply bg-[#FF5A5F] hover:bg-[#E04E53] text-white font-medium px-6 py-3 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md;
}

/* Secondary Outlined Button */
.btn-outline {
  @apply border border-gray-300 hover:border-gray-400 text-[#484848] font-medium px-6 py-3 rounded-xl bg-white hover:bg-gray-50 transition-all duration-200;
}

/* Icon Button */
.btn-icon {
  @apply p-3 rounded-xl bg-gray-100 hover:bg-gray-200 text-[#767676] hover:text-[#484848] transition-all duration-200;
}
```

### Card Components
```css
/* Primary Card */
.card-primary {
  @apply bg-white rounded-2xl shadow-sm border border-gray-200 p-8 hover:shadow-md transition-shadow duration-200;
}

/* Metric Card */
.card-metric {
  @apply bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-all duration-200 cursor-pointer;
}

/* Chart Container */
.chart-container {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
}
```

### Typography
```css
/* Airbnb Headings */
.heading-primary {
  @apply text-3xl font-semibold text-[#484848] tracking-tight;
}

.heading-secondary {
  @apply text-2xl font-semibold text-[#484848];
}

.heading-tertiary {
  @apply text-xl font-medium text-[#484848];
}

/* Body Text */
.text-primary {
  @apply text-base text-[#484848] leading-relaxed;
}

.text-secondary {
  @apply text-sm text-[#767676];
}

.text-muted {
  @apply text-sm text-[#B0B0B0];
}
```

## 📊 Export & API Implementation

### Export Service
```typescript
// lib/dashboard/export/BridgesExportService.ts
export class BridgesExportService {
  static async exportCSV(data: TrendsData, filters: TrendsFilters): Promise<Blob> {
    const csvData = [
      ['Date', 'Volume', 'Price Index', 'Region', 'Asset Type', 'Sentiment'],
      ...data.chartData.map(point => [
        point.timestamp,
        point.volume,
        point.price,
        point.region,
        point.assetType,
        point.sentiment
      ])
    ]
    
    const csv = csvData.map(row => row.join(',')).join('\n')
    return new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  }

  static async exportPDF(elementRef: RefObject<HTMLElement>): Promise<Blob> {
    const element = elementRef.current
    if (!element) throw new Error('Chart element not found')

    const canvas = await html2canvas(element, {
      scale: 2,
      backgroundColor: '#F7F7F7',
      useCORS: true
    })

    const pdf = new jsPDF('landscape', 'mm', 'a4')
    const imgData = canvas.toDataURL('image/png')
    
    // Add Airbnb-style header
    pdf.setFillColor(255, 90, 95) // Coral header
    pdf.rect(0, 0, 297, 20, 'F')
    pdf.setTextColor(255, 255, 255)
    pdf.setFontSize(16)
    pdf.text('Bridges Market Trends Report', 15, 12)
    
    const imgWidth = 267
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    pdf.addImage(imgData, 'PNG', 15, 25, imgWidth, imgHeight)
    
    return pdf.output('blob')
  }
}
```

### Webhook Alert System
```typescript
// API endpoint: /api/dashboard/trends/webhooks/[userId]/route.ts
export async function POST(req: Request, { params }: { params: { userId: string } }) {
  const { alertType, currentValue, threshold, metadata } = await req.json()
  
  // Validate webhook signature
  const signature = req.headers.get('x-bridges-signature')
  if (!validateWebhookSignature(signature, req.body)) {
    return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
  }

  // Send notification with Airbnb-style formatting
  await sendNotification({
    userId: params.userId,
    title: getAlertTitle(alertType),
    message: formatAlertMessage(alertType, currentValue, threshold),
    style: 'airbnb', // Use coral branding
    data: { alertType, currentValue, threshold, metadata }
  })

  return NextResponse.json({ success: true })
}
```

## ⚠️ Edge Cases & Error Handling

### Loading States
```typescript
// Loading with Airbnb skeleton
const BridgesLoadingSkeleton = () => (
  <div className="animate-pulse space-y-6">
    <div className="bg-gray-200 h-8 rounded-xl w-64"></div>
    <div className="grid grid-cols-3 gap-6">
      {[1,2,3].map(i => (
        <div key={i} className="bg-gray-200 h-32 rounded-xl"></div>
      ))}
    </div>
  </div>
)
```

### Error Boundaries
```typescript
// Airbnb-style error component
const BridgesErrorState = ({ error, retry }: ErrorStateProps) => (
  <div className="bg-white rounded-2xl shadow-sm border border-red-200 p-8 text-center">
    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
      <AlertTriangle className="w-8 h-8 text-[#C13515]" />
    </div>
    <h3 className="text-xl font-semibold text-[#484848] mb-2">
      Unable to load market data
    </h3>
    <p className="text-[#767676] mb-6">{error.message}</p>
    <button onClick={retry} className="btn-coral">
      Try Again
    </button>
  </div>
)
```

### No Data States
```typescript
// Empty state with Airbnb illustration style
const BridgesEmptyState = ({ type }: { type: string }) => (
  <div className="bg-white rounded-xl p-12 text-center">
    <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
      <BarChart className="w-12 h-12 text-[#767676]" />
    </div>
    <h3 className="text-lg font-medium text-[#484848] mb-2">
      No {type} data available
    </h3>
    <p className="text-[#767676] max-w-sm mx-auto">
      Try adjusting your filters or check back later for updated market information.
    </p>
  </div>
)
```

## 🚀 Implementation Roadmap

### Week 1: Foundation
- [ ] Set up Airbnb color system in Tailwind config
- [ ] Create base layout components with responsive design
- [ ] Implement core data fetching hooks
- [ ] Build metrics grid with loading states

### Week 2: Visualizations  
- [ ] Integrate Recharts with Airbnb styling
- [ ] Build interactive volume and price charts
- [ ] Create sentiment gauge with SVG animations
- [ ] Implement geographic heatmap

### Week 3: Advanced Features
- [ ] Add AI commentary with PydanticAI validation
- [ ] Build export functionality (CSV, JSON, PDF)
- [ ] Create alert configuration system
- [ ] Implement webhook endpoints

### Week 4: Polish & Optimization
- [ ] Add comprehensive error handling
- [ ] Implement real-time WebSocket updates
- [ ] Performance optimization and caching
- [ ] Mobile responsiveness testing

This architecture provides a production-ready, Airbnb-styled trends dashboard with comprehensive functionality and excellent user experience. The modular design ensures maintainability and scalability for future enhancements.