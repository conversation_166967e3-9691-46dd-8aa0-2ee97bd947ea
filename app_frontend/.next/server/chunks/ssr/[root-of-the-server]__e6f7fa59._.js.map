{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-white hover:bg-primary-600 active:bg-primary-700 shadow-sm hover:shadow-md\",\n        secondary: \"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300 border border-secondary-300\",\n        outline: \"border border-border bg-background hover:bg-surface-200 hover:border-secondary-400 text-foreground\",\n        ghost: \"hover:bg-surface-200 hover:text-accent text-secondary-700\",\n        link: \"text-primary hover:text-primary-600 underline-offset-4 hover:underline p-0 h-auto\",\n        destructive: \"bg-error text-white hover:bg-red-600 active:bg-red-700 shadow-sm\",\n        success: \"bg-success text-white hover:bg-green-600 active:bg-green-700 shadow-sm\",\n      },\n      size: {\n        sm: \"h-8 px-3 py-1.5 text-xs rounded-md\",\n        default: \"h-10 px-4 py-2 text-sm\",\n        lg: \"h-12 px-6 py-3 text-base rounded-xl\",\n        xl: \"h-14 px-8 py-4 text-lg rounded-xl\",\n        icon: \"h-10 w-10 p-0\",\n        \"icon-sm\": \"h-8 w-8 p-0\",\n        \"icon-lg\": \"h-12 w-12 p-0\",\n      },\n      loading: {\n        true: \"cursor-not-allowed\",\n        false: \"\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      loading: false,\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  loadingText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant, \n    size, \n    loading = false,\n    loadingText,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled,\n    asChild = false, \n    ...props \n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    const isDisabled = disabled || loading\n    \n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, loading, className }))}\n        disabled={isDisabled}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            aria-hidden=\"true\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {!loading && leftIcon && <span className=\"mr-1\">{leftIcon}</span>}\n        {loading ? loadingText || children : children}\n        {!loading && rightIcon && <span className=\"ml-1\">{rightIcon}</span>}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,qYACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAaF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,WAAW,EACX,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,aAAa,YAAY;IAE/B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAS;QAAU;QACjE,UAAU;QACV,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,eAAY;;kCAEZ,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,WAAW,0BAAY,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD,UAAU,eAAe,WAAW;YACpC,CAAC,WAAW,2BAAa,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/container.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nexport interface ContainerProps extends HTMLAttributes<HTMLDivElement> {\n  maxWidth?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"full\"\n}\n\nconst Container = forwardRef<HTMLDivElement, ContainerProps>(\n  ({ className, maxWidth = \"2xl\", children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"mx-auto w-full px-4 sm:px-6 lg:px-8\",\n          {\n            \"max-w-screen-sm\": maxWidth === \"sm\",\n            \"max-w-screen-md\": maxWidth === \"md\",\n            \"max-w-screen-lg\": maxWidth === \"lg\",\n            \"max-w-screen-xl\": maxWidth === \"xl\",\n            \"max-w-screen-2xl\": maxWidth === \"2xl\",\n            \"max-w-full\": maxWidth === \"full\",\n          },\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\nContainer.displayName = \"Container\"\n\nexport { Container }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpD,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uCACA;YACE,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,oBAAoB,aAAa;YACjC,cAAc,aAAa;QAC7B,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAEF,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/auth/authHelpers.ts"], "sourcesContent": ["import { getServerSession } from \"next-auth/next\"\nimport { Session } from \"next-auth\"\nimport type { AuthError, AuthErrorType, AuthResponse } from \"@/types/auth\"\n\n// API base URL\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\"\n\n/**\n * Get the current session from NextAuth\n */\nexport async function getCurrentSession(): Promise<Session | null> {\n  try {\n    const session = await getServerSession()\n    return session\n  } catch (error) {\n    console.error(\"Error getting session:\", error)\n    return null\n  }\n}\n\n/**\n * Get auth headers for API requests\n */\nexport function getAuthHeaders(accessToken?: string): HeadersInit {\n  const headers: HeadersInit = {\n    \"Content-Type\": \"application/json\",\n  }\n\n  if (accessToken) {\n    headers[\"Authorization\"] = `Bearer ${accessToken}`\n  }\n\n  return headers\n}\n\n\n/**\n * Standardize authentication errors\n */\nexport function standardizeAuthError(error: any): AuthError {\n  if (error?.response?.status === 401) {\n    return {\n      type: AuthErrorType.CredentialsSignIn,\n      message: \"Invalid email or password\",\n      code: \"INVALID_CREDENTIALS\",\n    }\n  }\n\n  if (error?.response?.status === 404) {\n    return {\n      type: AuthErrorType.CredentialsSignIn,\n      message: \"User not found\",\n      code: \"USER_NOT_FOUND\",\n    }\n  }\n\n  if (error?.response?.status === 429) {\n    return {\n      type: AuthErrorType.Default,\n      message: \"Too many attempts. Please try again later\",\n      code: \"RATE_LIMIT_EXCEEDED\",\n    }\n  }\n\n  if (error?.message?.includes(\"OAuth\")) {\n    return {\n      type: AuthErrorType.OAuthSignIn,\n      message: error.message,\n      code: \"OAUTH_ERROR\",\n    }\n  }\n\n  return {\n    type: AuthErrorType.Default,\n    message: error?.message || \"An unexpected error occurred\",\n    code: \"UNKNOWN_ERROR\",\n  }\n}\n\n/**\n * Validate session and check if it's expired\n */\nexport function isSessionValid(session: Session | null): boolean {\n  if (!session) return false\n  \n  // Check if session has required fields\n  if (!session.user?.id || !session.user?.email) return false\n  \n  // If there's an error in the session, it's invalid\n  if (session.error) return false\n  \n  return true\n}\n\n/**\n * Get user-friendly error messages\n */\nexport function getAuthErrorMessage(errorType: AuthErrorType): string {\n  switch (errorType) {\n    case AuthErrorType.CredentialsSignIn:\n      return \"Invalid email or password. Please check your credentials and try again.\"\n    case AuthErrorType.OAuthSignIn:\n      return \"Failed to sign in with the selected provider. Please try again.\"\n    case AuthErrorType.OAuthCallback:\n      return \"Failed to complete OAuth sign in. Please try again.\"\n    case AuthErrorType.OAuthCreateAccount:\n      return \"Failed to create account with the selected provider.\"\n    case AuthErrorType.OAuthAccountNotLinked:\n      return \"This account is already linked to another provider.\"\n    case AuthErrorType.SessionRequired:\n      return \"You must be signed in to access this page.\"\n    default:\n      return \"An unexpected error occurred. Please try again.\"\n  }\n}\n\n/**\n * Format user data for display\n */\nexport function formatUserData(user: any) {\n  return {\n    id: user.id,\n    email: user.email,\n    name: user.name || \"User\",\n    image: user.image || user.avatar_url || null,\n    initials: getInitials(user.name || user.email),\n  }\n}\n\n/**\n * Get initials from name or email\n */\nfunction getInitials(nameOrEmail: string): string {\n  if (!nameOrEmail) return \"U\"\n  \n  // If it's an email, use the first letter\n  if (nameOrEmail.includes(\"@\")) {\n    return nameOrEmail[0].toUpperCase()\n  }\n  \n  // If it's a name, get first letters of first and last name\n  const parts = nameOrEmail.split(\" \")\n  if (parts.length >= 2) {\n    return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase()\n  }\n  \n  return nameOrEmail[0].toUpperCase()\n}\n\n/**\n * Check if error is due to network issues\n */\nexport function isNetworkError(error: any): boolean {\n  return (\n    error?.code === \"ECONNREFUSED\" ||\n    error?.code === \"ENOTFOUND\" ||\n    error?.message?.includes(\"fetch failed\") ||\n    error?.message?.includes(\"Network request failed\")\n  )\n}\n\n/**\n * Get redirect URL after authentication\n */\nexport function getAuthRedirectUrl(callbackUrl?: string | null): string {\n  // Default redirect to dashboard\n  const defaultRedirect = \"/dashboard\"\n  \n  if (!callbackUrl) return defaultRedirect\n  \n  // Ensure the callback URL is safe (same origin)\n  try {\n    const url = new URL(callbackUrl, window.location.origin)\n    if (url.origin === window.location.origin) {\n      return url.pathname + url.search + url.hash\n    }\n  } catch {\n    // Invalid URL\n  }\n  \n  return defaultRedirect\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAIA,eAAe;AACf,MAAM,eAAe,6DAAmC;AAKjD,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAKO,SAAS,eAAe,WAAoB;IACjD,MAAM,UAAuB;QAC3B,gBAAgB;IAClB;IAEA,IAAI,aAAa;QACf,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa;IACpD;IAEA,OAAO;AACT;AAMO,SAAS,qBAAqB,KAAU;IAC7C,IAAI,OAAO,UAAU,WAAW,KAAK;QACnC,OAAO;YACL,MAAM,cAAc,iBAAiB;YACrC,SAAS;YACT,MAAM;QACR;IACF;IAEA,IAAI,OAAO,UAAU,WAAW,KAAK;QACnC,OAAO;YACL,MAAM,cAAc,iBAAiB;YACrC,SAAS;YACT,MAAM;QACR;IACF;IAEA,IAAI,OAAO,UAAU,WAAW,KAAK;QACnC,OAAO;YACL,MAAM,cAAc,OAAO;YAC3B,SAAS;YACT,MAAM;QACR;IACF;IAEA,IAAI,OAAO,SAAS,SAAS,UAAU;QACrC,OAAO;YACL,MAAM,cAAc,WAAW;YAC/B,SAAS,MAAM,OAAO;YACtB,MAAM;QACR;IACF;IAEA,OAAO;QACL,MAAM,cAAc,OAAO;QAC3B,SAAS,OAAO,WAAW;QAC3B,MAAM;IACR;AACF;AAKO,SAAS,eAAe,OAAuB;IACpD,IAAI,CAAC,SAAS,OAAO;IAErB,uCAAuC;IACvC,IAAI,CAAC,QAAQ,IAAI,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE,OAAO,OAAO;IAEtD,mDAAmD;IACnD,IAAI,QAAQ,KAAK,EAAE,OAAO;IAE1B,OAAO;AACT;AAKO,SAAS,oBAAoB,SAAwB;IAC1D,OAAQ;QACN,KAAK,cAAc,iBAAiB;YAClC,OAAO;QACT,KAAK,cAAc,WAAW;YAC5B,OAAO;QACT,KAAK,cAAc,aAAa;YAC9B,OAAO;QACT,KAAK,cAAc,kBAAkB;YACnC,OAAO;QACT,KAAK,cAAc,qBAAqB;YACtC,OAAO;QACT,KAAK,cAAc,eAAe;YAChC,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,eAAe,IAAS;IACtC,OAAO;QACL,IAAI,KAAK,EAAE;QACX,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI,IAAI;QACnB,OAAO,KAAK,KAAK,IAAI,KAAK,UAAU,IAAI;QACxC,UAAU,YAAY,KAAK,IAAI,IAAI,KAAK,KAAK;IAC/C;AACF;AAEA;;CAEC,GACD,SAAS,YAAY,WAAmB;IACtC,IAAI,CAAC,aAAa,OAAO;IAEzB,yCAAyC;IACzC,IAAI,YAAY,QAAQ,CAAC,MAAM;QAC7B,OAAO,WAAW,CAAC,EAAE,CAAC,WAAW;IACnC;IAEA,2DAA2D;IAC3D,MAAM,QAAQ,YAAY,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,GAAG;QACrB,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,WAAW;IAClE;IAEA,OAAO,WAAW,CAAC,EAAE,CAAC,WAAW;AACnC;AAKO,SAAS,eAAe,KAAU;IACvC,OACE,OAAO,SAAS,kBAChB,OAAO,SAAS,eAChB,OAAO,SAAS,SAAS,mBACzB,OAAO,SAAS,SAAS;AAE7B;AAKO,SAAS,mBAAmB,WAA2B;IAC5D,gCAAgC;IAChC,MAAM,kBAAkB;IAExB,IAAI,CAAC,aAAa,OAAO;IAEzB,gDAAgD;IAChD,IAAI;QACF,MAAM,MAAM,IAAI,IAAI,aAAa,OAAO,QAAQ,CAAC,MAAM;QACvD,IAAI,IAAI,MAAM,KAAK,OAAO,QAAQ,CAAC,MAAM,EAAE;YACzC,OAAO,IAAI,QAAQ,GAAG,IAAI,MAAM,GAAG,IAAI,IAAI;QAC7C;IACF,EAAE,OAAM;IACN,cAAc;IAChB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { useSession, signOut } from \"next-auth/react\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Container } from \"@/components/ui/container\"\nimport { Menu, Bell, User, LogOut, Settings, BookOpen } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { formatUserData } from \"@/lib/auth/authHelpers\"\n\ninterface HeaderProps {\n  onMenuClick?: () => void\n}\n\nexport function Header({ onMenuClick }: HeaderProps) {\n  const pathname = usePathname()\n  const { data: session, status } = useSession()\n\n  const user = session?.user ? formatUserData(session.user) : null\n\n  const navigation = [\n    { name: \"Dashboard\", href: \"/dashboard\" },\n    { name: \"Books\", href: \"/dashboard/books\" },\n    { name: \"Analytics\", href: \"/dashboard/analytics\" },\n    { name: \"Trends\", href: \"/dashboard/trends\" },\n  ]\n\n  const handleLogout = async () => {\n    try {\n      await signOut({ \n        callbackUrl: \"/auth/login\",\n        redirect: true \n      })\n    } catch (error) {\n      console.error(\"Logout error:\", error)\n    }\n  }\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <Container maxWidth=\"full\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Left section */}\n          <div className=\"flex items-center gap-4\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"md:hidden\"\n              onClick={onMenuClick}\n            >\n              <Menu className=\"h-5 w-5\" />\n              <span className=\"sr-only\">Toggle menu</span>\n            </Button>\n\n            <Link href=\"/\" className=\"flex items-center gap-2\">\n              <BookOpen className=\"h-6 w-6 text-primary\" />\n              <span className=\"font-display text-xl font-semibold hidden sm:inline\">\n                Publish AI\n              </span>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center gap-1 ml-6\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    \"px-3 py-2 text-sm font-medium rounded-lg transition-colors\",\n                    pathname === item.href\n                      ? \"bg-primary/10 text-primary\"\n                      : \"text-muted-foreground hover:text-foreground hover:bg-muted\"\n                  )}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n\n          {/* Right section */}\n          <div className=\"flex items-center gap-2\">\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute top-0 right-0 h-2 w-2 rounded-full bg-primary\" />\n              <span className=\"sr-only\">Notifications</span>\n            </Button>\n\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                  <Avatar className=\"h-8 w-8\">\n                    <AvatarImage src={user?.image || undefined} alt={user?.name} />\n                    <AvatarFallback>\n                      {user?.initials || \"U\"}\n                    </AvatarFallback>\n                  </Avatar>\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                <DropdownMenuLabel className=\"font-normal\">\n                  <div className=\"flex flex-col space-y-1\">\n                    <p className=\"text-sm font-medium leading-none\">{user?.name || \"Guest\"}</p>\n                    <p className=\"text-xs leading-none text-muted-foreground\">\n                      {user?.email || \"<EMAIL>\"}\n                    </p>\n                  </div>\n                </DropdownMenuLabel>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem asChild>\n                  <Link href=\"/dashboard/profile\" className=\"cursor-pointer\">\n                    <User className=\"mr-2 h-4 w-4\" />\n                    <span>Profile</span>\n                  </Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem asChild>\n                  <Link href=\"/dashboard/settings\" className=\"cursor-pointer\">\n                    <Settings className=\"mr-2 h-4 w-4\" />\n                    <span>Settings</span>\n                  </Link>\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem \n                  className=\"cursor-pointer text-destructive\"\n                  onClick={handleLogout}\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  <span>Log out</span>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </Container>\n    </header>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAlBA;;;;;;;;;;;;AAwBO,SAAS,OAAO,EAAE,WAAW,EAAe;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAE3C,MAAM,OAAO,SAAS,OAAO,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,IAAI;IAE5D,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAmB;QAC1C;YAAE,MAAM;YAAa,MAAM;QAAuB;QAClD;YAAE,MAAM;YAAU,MAAM;QAAoB;KAC7C;IAED,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;gBACZ,aAAa;gBACb,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC,qIAAA,CAAA,YAAS;YAAC,UAAS;sBAClB,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAsD;;;;;;;;;;;;0CAMxE,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,aAAa,KAAK,IAAI,GAClB,+BACA;kDAGL,KAAK,IAAI;uCATL,KAAK,IAAI;;;;;;;;;;;;;;;;kCAgBtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,WAAU;;kDAC5C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDAAC,KAAK,MAAM,SAAS;wDAAW,KAAK,MAAM;;;;;;kEACvD,8OAAC,kIAAA,CAAA,iBAAc;kEACZ,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;kDAK3B,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,8OAAC,4IAAA,CAAA,oBAAiB;gDAAC,WAAU;0DAC3B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC,MAAM,QAAQ;;;;;;sEAC/D,8OAAC;4DAAE,WAAU;sEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;0DAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0DACtB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;;sEACxC,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAsB,WAAU;;sEACzC,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0DACtB,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;;kEAET,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport {\n  LayoutDashboard,\n  BookOpen,\n  BarChart3,\n  TrendingUp,\n  Upload,\n  Settings,\n  HelpCircle,\n  X,\n  PlusCircle,\n  FileText,\n  DollarSign,\n} from \"lucide-react\"\n\ninterface SidebarProps {\n  isOpen?: boolean\n  onClose?: () => void\n}\n\nconst sidebarItems = [\n  {\n    title: \"Main\",\n    items: [\n      { name: \"Dashboard\", href: \"/dashboard\", icon: LayoutDashboard },\n      { name: \"My Books\", href: \"/dashboard/books\", icon: BookOpen },\n      { name: \"Analytics\", href: \"/dashboard/analytics\", icon: BarChart3 },\n      { name: \"Trends\", href: \"/dashboard/trends\", icon: TrendingUp },\n    ],\n  },\n  {\n    title: \"Publishing\",\n    items: [\n      { name: \"New Book\", href: \"/dashboard/books/new\", icon: PlusCircle },\n      { name: \"Manuscripts\", href: \"/dashboard/manuscripts\", icon: FileText },\n      { name: \"Publications\", href: \"/dashboard/publications\", icon: Upload },\n      { name: \"Revenue\", href: \"/dashboard/revenue\", icon: DollarSign },\n    ],\n  },\n  {\n    title: \"Support\",\n    items: [\n      { name: \"Settings\", href: \"/dashboard/settings\", icon: Settings },\n      { name: \"Help & Docs\", href: \"/dashboard/help\", icon: HelpCircle },\n    ],\n  },\n]\n\nexport function Sidebar({ isOpen = true, onClose }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile backdrop */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black/50 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <aside\n        className={cn(\n          \"fixed inset-y-0 left-0 z-50 w-64 bg-surface transition-transform duration-300 md:relative md:translate-x-0\",\n          isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Mobile header */}\n          <div className=\"flex h-16 items-center justify-between border-b px-4 md:hidden\">\n            <span className=\"font-display text-lg font-semibold\">Menu</span>\n            <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n              <X className=\"h-5 w-5\" />\n              <span className=\"sr-only\">Close sidebar</span>\n            </Button>\n          </div>\n\n          {/* Navigation */}\n          <ScrollArea className=\"flex-1 px-3 py-4\">\n            <nav className=\"space-y-6\">\n              {sidebarItems.map((section) => (\n                <div key={section.title}>\n                  <h3 className=\"mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-muted-foreground\">\n                    {section.title}\n                  </h3>\n                  <div className=\"space-y-1\">\n                    {section.items.map((item) => {\n                      const isActive = pathname === item.href\n                      const Icon = item.icon\n                      return (\n                        <Link\n                          key={item.name}\n                          href={item.href}\n                          className={cn(\n                            \"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors\",\n                            isActive\n                              ? \"bg-primary/10 text-primary\"\n                              : \"text-muted-foreground hover:bg-muted hover:text-foreground\"\n                          )}\n                        >\n                          <Icon className=\"h-5 w-5\" />\n                          {item.name}\n                        </Link>\n                      )\n                    })}\n                  </div>\n                </div>\n              ))}\n            </nav>\n          </ScrollArea>\n\n          {/* Footer */}\n          <div className=\"border-t p-4\">\n            <div className=\"rounded-lg bg-primary/10 p-3\">\n              <h4 className=\"text-sm font-semibold\">Upgrade to Pro</h4>\n              <p className=\"mt-1 text-xs text-muted-foreground\">\n                Unlock advanced features and analytics\n              </p>\n              <Button size=\"sm\" className=\"mt-3 w-full\">\n                Upgrade Now\n              </Button>\n            </div>\n          </div>\n        </div>\n      </aside>\n    </>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AA0BA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;gBAAc,MAAM,4NAAA,CAAA,kBAAe;YAAC;YAC/D;gBAAE,MAAM;gBAAY,MAAM;gBAAoB,MAAM,8MAAA,CAAA,WAAQ;YAAC;YAC7D;gBAAE,MAAM;gBAAa,MAAM;gBAAwB,MAAM,kNAAA,CAAA,YAAS;YAAC;YACnE;gBAAE,MAAM;gBAAU,MAAM;gBAAqB,MAAM,kNAAA,CAAA,aAAU;YAAC;SAC/D;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAwB,MAAM,kNAAA,CAAA,aAAU;YAAC;YACnE;gBAAE,MAAM;gBAAe,MAAM;gBAA0B,MAAM,8MAAA,CAAA,WAAQ;YAAC;YACtE;gBAAE,MAAM;gBAAgB,MAAM;gBAA2B,MAAM,sMAAA,CAAA,SAAM;YAAC;YACtE;gBAAE,MAAM;gBAAW,MAAM;gBAAsB,MAAM,kNAAA,CAAA,aAAU;YAAC;SACjE;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAuB,MAAM,0MAAA,CAAA,WAAQ;YAAC;YAChE;gBAAE,MAAM;gBAAe,MAAM;gBAAmB,MAAM,8NAAA,CAAA,aAAU;YAAC;SAClE;IACH;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,IAAI,EAAE,OAAO,EAAgB;IAC9D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;YAEG,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8GACA,SAAS,kBAAkB;0BAG7B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS;;sDAC3C,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAK9B,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;oDAClB,MAAM,WAAW,aAAa,KAAK,IAAI;oDACvC,MAAM,OAAO,KAAK,IAAI;oDACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,+BACA;;0EAGN,8OAAC;gEAAK,WAAU;;;;;;4DACf,KAAK,IAAI;;uDAVL,KAAK,IAAI;;;;;gDAapB;;;;;;;uCAvBM,QAAQ,KAAK;;;;;;;;;;;;;;;sCA+B7B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}, {"offset": {"line": 1427, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1458, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Container } from \"@/components/ui/container\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { BookOpen, Github, Twitter, Linkedin } from \"lucide-react\"\n\nconst footerLinks = {\n  product: [\n    { name: \"Features\", href: \"#features\" },\n    { name: \"Pricing\", href: \"#pricing\" },\n    { name: \"API\", href: \"/docs/api\" },\n    { name: \"Integrations\", href: \"#integrations\" },\n  ],\n  company: [\n    { name: \"About\", href: \"/about\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Careers\", href: \"/careers\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  resources: [\n    { name: \"Documentation\", href: \"/docs\" },\n    { name: \"Guides\", href: \"/guides\" },\n    { name: \"Support\", href: \"/support\" },\n    { name: \"Status\", href: \"/status\" },\n  ],\n  legal: [\n    { name: \"Privacy\", href: \"/privacy\" },\n    { name: \"Terms\", href: \"/terms\" },\n    { name: \"Cookie Policy\", href: \"/cookies\" },\n    { name: \"License\", href: \"/license\" },\n  ],\n}\n\nconst socialLinks = [\n  { name: \"GitHub\", icon: Github, href: \"https://github.com\" },\n  { name: \"Twitter\", icon: Twitter, href: \"https://twitter.com\" },\n  { name: \"LinkedIn\", icon: Linkedin, href: \"https://linkedin.com\" },\n]\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-surface border-t\">\n      <Container>\n        <div className=\"py-12 md:py-16\">\n          {/* Main footer content */}\n          <div className=\"grid grid-cols-2 gap-8 md:grid-cols-5\">\n            {/* Brand section */}\n            <div className=\"col-span-2 md:col-span-1\">\n              <Link href=\"/\" className=\"flex items-center gap-2 mb-4\">\n                <BookOpen className=\"h-6 w-6 text-primary\" />\n                <span className=\"font-display text-lg font-semibold\">\n                  Publish AI\n                </span>\n              </Link>\n              <p className=\"text-sm text-muted-foreground\">\n                AI-powered book publishing platform for modern authors.\n              </p>\n              {/* Social links */}\n              <div className=\"flex gap-4 mt-6\">\n                {socialLinks.map((link) => {\n                  const Icon = link.icon\n                  return (\n                    <a\n                      key={link.name}\n                      href={link.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      <Icon className=\"h-5 w-5\" />\n                      <span className=\"sr-only\">{link.name}</span>\n                    </a>\n                  )\n                })}\n              </div>\n            </div>\n\n            {/* Links sections */}\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          <Separator className=\"my-8\" />\n\n          {/* Bottom section */}\n          <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\n            <p className=\"text-sm text-muted-foreground\">\n              © {new Date().getFullYear()} Publish AI. All rights reserved.\n            </p>\n            <p className=\"text-sm text-muted-foreground\">\n              Made with ❤️ by the Publish AI team\n            </p>\n          </div>\n        </div>\n      </Container>\n    </footer>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAO,MAAM;QAAY;QACjC;YAAE,MAAM;YAAgB,MAAM;QAAgB;KAC/C;IACD,SAAS;QACP;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,WAAW;QACT;YAAE,MAAM;YAAiB,MAAM;QAAQ;QACvC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAU,MAAM;QAAU;KACnC;IACD,OAAO;QACL;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAU,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;IAAqB;IAC3D;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;IAAsB;IAC9D;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;IAAuB;CAClE;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC,qIAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAIvD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,OAAO,KAAK,IAAI;4CACtB,qBACE,8OAAC;gDAEC,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAW,KAAK,IAAI;;;;;;;+CAP/B,KAAK,IAAI;;;;;wCAUpB;;;;;;;;;;;;0CAKJ,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAa1B,8OAAC,qIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCAGrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAgC;oCACxC,IAAI,OAAO,WAAW;oCAAG;;;;;;;0CAE9B,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD", "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border shadow-sm hover:shadow-md\",\n        elevated: \"border-border shadow-md hover:shadow-lg\",\n        outlined: \"border-border shadow-none hover:shadow-sm\",\n        ghost: \"border-transparent shadow-none hover:bg-surface-100\",\n        success: \"border-success bg-green-50 shadow-sm\",\n        warning: \"border-warning bg-yellow-50 shadow-sm\",\n        error: \"border-error bg-red-50 shadow-sm\",\n        info: \"border-info bg-blue-50 shadow-sm\",\n      },\n      size: {\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n      hoverable: {\n        true: \"cursor-pointer hover:scale-[1.02] active:scale-[0.98]\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      hoverable: false,\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, size, hoverable, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, size, hoverable }), className)}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-2\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-tight tracking-tight text-accent\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground leading-relaxed\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"pt-4\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center pt-4\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,8EACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;YACP,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,WAAW;YACT,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,WAAW;IACb;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAM;QAAU,IAAI;QACzD,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAE5D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/error-boundary.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport { <PERSON>ert<PERSON>riangle } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from \"@/components/ui/card\"\n\ninterface Props {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; reset: () => void }>\n}\n\ninterface State {\n  hasError: boolean\n  error: Error | null\n}\n\nexport class ErrorBoundary extends React.Component<Props, State> {\n  constructor(props: Props) {\n    super(props)\n    this.state = { hasError: false, error: null }\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error(\"ErrorBoundary caught an error:\", error, errorInfo)\n    \n    // Log to monitoring service in production\n    if (process.env.NODE_ENV === 'production') {\n      this.logError(error, errorInfo)\n    }\n  }\n\n  private logError = async (error: Error, errorInfo: React.ErrorInfo) => {\n    try {\n      const errorReport = {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        errorId: crypto.randomUUID(),\n        timestamp: new Date().toISOString(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n      }\n      \n      // Send to backend error reporting endpoint\n      await fetch('/api/errors/report', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(errorReport),\n      })\n    } catch (reportingError) {\n      console.error('Failed to report error:', reportingError)\n    }\n  }\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: null })\n  }\n\n  render() {\n    if (this.state.hasError && this.state.error) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return (\n          <FallbackComponent\n            error={this.state.error}\n            reset={this.handleReset}\n          />\n        )\n      }\n\n      return <DefaultErrorFallback error={this.state.error} reset={this.handleReset} />\n    }\n\n    return this.props.children\n  }\n}\n\ninterface ErrorFallbackProps {\n  error: Error\n  reset: () => void\n}\n\nexport function DefaultErrorFallback({ error, reset }: ErrorFallbackProps) {\n  return (\n    <div className=\"flex items-center justify-center min-h-[400px] p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader>\n          <div className=\"flex items-center gap-2\">\n            <AlertTriangle className=\"h-5 w-5 text-destructive\" />\n            <CardTitle>Something went wrong</CardTitle>\n          </div>\n          <CardDescription>\n            An unexpected error occurred. Please try again.\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <details className=\"rounded-lg bg-muted p-3\">\n            <summary className=\"cursor-pointer text-sm font-medium\">\n              Error details\n            </summary>\n            <pre className=\"mt-2 whitespace-pre-wrap text-xs text-muted-foreground\">\n              {error.message}\n            </pre>\n          </details>\n        </CardContent>\n        <CardFooter className=\"flex gap-2\">\n          <Button onClick={reset}>Try again</Button>\n          <Button\n            variant=\"outline\"\n            onClick={() => window.location.href = \"/dashboard\"}\n          >\n            Go to Dashboard\n          </Button>\n        </CardFooter>\n      </Card>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAiBO,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;YAAO,OAAO;QAAK;IAC9C;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,0CAA0C;QAC1C,uCAA2C;;QAE3C;IACF;IAEQ,WAAW,OAAO,OAAc;QACtC,IAAI;YACF,MAAM,cAAc;gBAClB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;gBAClB,gBAAgB,UAAU,cAAc;gBACxC,SAAS,OAAO,UAAU;gBAC1B,WAAW,IAAI,OAAO,WAAW;gBACjC,KAAK,OAAO,QAAQ,CAAC,IAAI;gBACzB,WAAW,UAAU,SAAS;YAChC;YAEA,2CAA2C;YAC3C,MAAM,MAAM,sBAAsB;gBAChC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;QACF,EAAE,OAAO,gBAAgB;YACvB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF,EAAC;IAED,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAK;IAC/C,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBACE,8OAAC;oBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBACvB,OAAO,IAAI,CAAC,WAAW;;;;;;YAG7B;YAEA,qBAAO,8OAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC,WAAW;;;;;;QAC/E;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAOO,SAAS,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAsB;IACvE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAQ,WAAU;0CAAqC;;;;;;0CAGxD,8OAAC;gCAAI,WAAU;0CACZ,MAAM,OAAO;;;;;;;;;;;;;;;;;8BAIpB,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;sCAAO;;;;;;sCACxB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sCACvC;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2195, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Head<PERSON> } from \"./header\"\nimport { Sidebar } from \"./sidebar\"\nimport { Footer } from \"./footer\"\nimport { ErrorBoundary } from \"./error-boundary\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  className?: string\n  showFooter?: boolean\n}\n\nexport function DashboardLayout({ \n  children, \n  className,\n  showFooter = false\n}: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header \n        onMenuClick={() => setSidebarOpen(!sidebarOpen)} \n      />\n      \n      <div className=\"flex h-[calc(100vh-4rem)]\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <main className={cn(\n          \"flex-1 overflow-y-auto\",\n          className\n        )}>\n          <ErrorBoundary>\n            {children}\n          </ErrorBoundary>\n        </main>\n      </div>\n      \n      {showFooter && <Footer />}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAeO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,aAAa,KAAK,EACG;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;gBACL,aAAa,IAAM,eAAe,CAAC;;;;;;0BAGrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uIAAA,CAAA,UAAO;wBACN,QAAQ;wBACR,SAAS,IAAM,eAAe;;;;;;kCAGhC,8OAAC;wBAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,0BACA;kCAEA,cAAA,8OAAC,iJAAA,CAAA,gBAAa;sCACX;;;;;;;;;;;;;;;;;YAKN,4BAAc,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAG5B", "debugId": null}}, {"offset": {"line": 2274, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/breadcrumb.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { ChevronRight, Home } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BreadcrumbItem {\n  label: string\n  href?: string\n}\n\ninterface BreadcrumbProps {\n  items?: BreadcrumbItem[]\n  className?: string\n}\n\nexport function Breadcrumb({ items, className }: BreadcrumbProps) {\n  const pathname = usePathname()\n  \n  // Auto-generate breadcrumbs from pathname if items not provided\n  const breadcrumbItems = items || generateBreadcrumbs(pathname)\n\n  if (breadcrumbItems.length === 0) return null\n\n  return (\n    <nav \n      aria-label=\"Breadcrumb\"\n      className={cn(\"flex items-center space-x-1 text-sm\", className)}\n    >\n      <Link\n        href=\"/dashboard\"\n        className=\"flex items-center text-muted-foreground hover:text-foreground transition-colors\"\n      >\n        <Home className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Home</span>\n      </Link>\n      \n      {breadcrumbItems.map((item, index) => {\n        const isLast = index === breadcrumbItems.length - 1\n        \n        return (\n          <div key={index} className=\"flex items-center\">\n            <ChevronRight className=\"h-4 w-4 text-muted-foreground mx-1\" />\n            {isLast || !item.href ? (\n              <span className=\"font-medium text-foreground\">\n                {item.label}\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"text-muted-foreground hover:text-foreground transition-colors\"\n              >\n                {item.label}\n              </Link>\n            )}\n          </div>\n        )\n      })}\n    </nav>\n  )\n}\n\nfunction generateBreadcrumbs(pathname: string): BreadcrumbItem[] {\n  const segments = pathname.split(\"/\").filter(Boolean)\n  const breadcrumbs: BreadcrumbItem[] = []\n  \n  // Skip the first segment if it's \"dashboard\"\n  const startIndex = segments[0] === \"dashboard\" ? 1 : 0\n  \n  segments.slice(startIndex).forEach((segment, index) => {\n    const href = \"/\" + segments.slice(0, startIndex + index + 1).join(\"/\")\n    const label = segment\n      .split(\"-\")\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(\" \")\n    \n    breadcrumbs.push({ label, href })\n  })\n  \n  return breadcrumbs\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAiBO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,kBAAkB,SAAS,oBAAoB;IAErD,IAAI,gBAAgB,MAAM,KAAK,GAAG,OAAO;IAEzC,qBACE,8OAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;;0BAErD,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAK;gBACL,WAAU;;kCAEV,8OAAC,mMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;YAG3B,gBAAgB,GAAG,CAAC,CAAC,MAAM;gBAC1B,MAAM,SAAS,UAAU,gBAAgB,MAAM,GAAG;gBAElD,qBACE,8OAAC;oBAAgB,WAAU;;sCACzB,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBACvB,UAAU,CAAC,KAAK,IAAI,iBACnB,8OAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;iDAGb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,KAAK;;;;;;;mBAXP;;;;;YAgBd;;;;;;;AAGN;AAEA,SAAS,oBAAoB,QAAgB;IAC3C,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAgC,EAAE;IAExC,6CAA6C;IAC7C,MAAM,aAAa,QAAQ,CAAC,EAAE,KAAK,cAAc,IAAI;IAErD,SAAS,KAAK,CAAC,YAAY,OAAO,CAAC,CAAC,SAAS;QAC3C,MAAM,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,aAAa,QAAQ,GAAG,IAAI,CAAC;QAClE,MAAM,QAAQ,QACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAER,YAAY,IAAI,CAAC;YAAE;YAAO;QAAK;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/typography.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nconst headingVariants = cva(\n  \"font-semibold text-accent tracking-tight\",\n  {\n    variants: {\n      variant: {\n        h1: \"text-4xl md:text-5xl lg:text-6xl\",\n        h2: \"text-3xl md:text-4xl lg:text-5xl\",\n        h3: \"text-2xl md:text-3xl lg:text-4xl\",\n        h4: \"text-xl md:text-2xl lg:text-3xl\",\n        h5: \"text-lg md:text-xl lg:text-2xl\",\n        h6: \"text-base md:text-lg lg:text-xl\",\n      },\n    },\n    defaultVariants: {\n      variant: \"h1\",\n    },\n  }\n)\n\nconst textVariants = cva(\n  \"\",\n  {\n    variants: {\n      variant: {\n        body: \"text-base text-foreground\",\n        lead: \"text-lg text-muted-foreground\",\n        small: \"text-sm text-foreground\",\n        muted: \"text-sm text-muted-foreground\",\n        caption: \"text-xs text-muted-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"body\",\n    },\n  }\n)\n\nexport interface HeadingProps\n  extends HTMLAttributes<HTMLHeadingElement>,\n    VariantProps<typeof headingVariants> {\n  as?: \"h1\" | \"h2\" | \"h3\" | \"h4\" | \"h5\" | \"h6\"\n}\n\nexport const Heading = forwardRef<HTMLHeadingElement, HeadingProps>(\n  ({ className, variant, as, ...props }, ref) => {\n    const Comp = as || variant || \"h1\"\n    return (\n      <Comp\n        className={cn(headingVariants({ variant: variant || as }), className)}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nHeading.displayName = \"Heading\"\n\nexport interface TextProps\n  extends HTMLAttributes<HTMLParagraphElement>,\n    VariantProps<typeof textVariants> {\n  as?: \"p\" | \"span\" | \"div\"\n}\n\nexport const Text = forwardRef<HTMLParagraphElement, TextProps>(\n  ({ className, variant, as: Comp = \"p\", ...props }, ref) => {\n    return (\n      <Comp\n        className={cn(textVariants({ variant }), className)}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nText.displayName = \"Text\""], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,kBAAkB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACxB,4CACA;IACE,UAAU;QACR,SAAS;YACP,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,IACA;IACE,UAAU;QACR,SAAS;YACP,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AASK,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IACrC,MAAM,OAAO,MAAM,WAAW;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;YAAE,SAAS,WAAW;QAAG,IAAI;QAC3D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,QAAQ,WAAW,GAAG;AAQf,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE;IACjD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;QAAQ,IAAI;QACzC,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,KAAK,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2462, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/page-header.tsx"], "sourcesContent": ["import { ReactNode } from \"react\"\nimport { Breadcrumb } from \"./breadcrumb\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { cn } from \"@/lib/utils\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  breadcrumb?: boolean\n  actions?: ReactNode\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  breadcrumb = true,\n  actions,\n  className,\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 pb-6\", className)}>\n      {breadcrumb && <Breadcrumb />}\n      \n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between\">\n        <div className=\"space-y-1\">\n          <Heading as=\"h1\" variant=\"h3\">\n            {title}\n          </Heading>\n          {description && (\n            <Text variant=\"muted\" className=\"max-w-2xl\">\n              {description}\n            </Text>\n          )}\n        </div>\n        \n        {actions && (\n          <div className=\"flex items-center gap-2 flex-shrink-0\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAUO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,aAAa,IAAI,EACjB,OAAO,EACP,SAAS,EACO;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAClC,4BAAc,8OAAC,0IAAA,CAAA,aAAU;;;;;0BAE1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,UAAO;gCAAC,IAAG;gCAAK,SAAQ;0CACtB;;;;;;4BAEF,6BACC,8OAAC,sIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAQ,WAAU;0CAC7B;;;;;;;;;;;;oBAKN,yBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 2539, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { Loader2 } from \"lucide-react\"\n\nexport interface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n}\n\nconst sizeClasses = {\n  sm: \"h-4 w-4\",\n  md: \"h-6 w-6\",\n  lg: \"h-8 w-8\",\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  return (\n    <Loader2\n      className={cn(\n        \"animate-spin text-primary\",\n        sizeClasses[size],\n        className\n      )}\n    />\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,8OAAC,iNAAA,CAAA,UAAO;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/loading.tsx"], "sourcesContent": ["import { LoadingSpinner } from \"@/components/ui/loading-spinner\"\n\ninterface LoadingProps {\n  fullScreen?: boolean\n  message?: string\n}\n\nexport function Loading({ fullScreen = false, message }: LoadingProps) {\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50\">\n        <div className=\"flex flex-col items-center gap-4\">\n          <LoadingSpinner size=\"lg\" />\n          {message && (\n            <p className=\"text-sm text-muted-foreground animate-pulse\">\n              {message}\n            </p>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center justify-center py-12\">\n      <div className=\"flex flex-col items-center gap-4\">\n        <LoadingSpinner size=\"md\" />\n        {message && (\n          <p className=\"text-sm text-muted-foreground animate-pulse\">\n            {message}\n          </p>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,QAAQ,EAAE,aAAa,KAAK,EAAE,OAAO,EAAgB;IACnE,IAAI,YAAY;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,iBAAc;wBAAC,MAAK;;;;;;oBACpB,yBACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;IAMb;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,8IAAA,CAAA,iBAAc;oBAAC,MAAK;;;;;;gBACpB,yBACC,8OAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 2647, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/auth/protected-route.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession } from \"next-auth/react\"\nimport { useRouter } from \"next/navigation\"\nimport { useEffect } from \"react\"\nimport { Loading } from \"@/components/layout/loading\"\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({ \n  children, \n  redirectTo = \"/auth/login\" \n}: ProtectedRouteProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(redirectTo)\n    }\n  }, [status, router, redirectTo])\n\n  if (status === \"loading\") {\n    return <Loading fullScreen message=\"Checking authentication...\" />\n  }\n\n  if (status === \"unauthenticated\") {\n    return null\n  }\n\n  return <>{children}</>\n}\n\nexport function PublicRoute({ \n  children, \n  redirectTo = \"/dashboard\" \n}: ProtectedRouteProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      router.push(redirectTo)\n    }\n  }, [status, router, redirectTo])\n\n  if (status === \"loading\") {\n    return <Loading fullScreen message=\"Checking authentication...\" />\n  }\n\n  if (status === \"authenticated\") {\n    return null\n  }\n\n  return <>{children}</>\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,eAAe,EAC7B,QAAQ,EACR,aAAa,aAAa,EACN;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;QAAQ;KAAW;IAE/B,IAAI,WAAW,WAAW;QACxB,qBAAO,8OAAC,uIAAA,CAAA,UAAO;YAAC,UAAU;YAAC,SAAQ;;;;;;IACrC;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;AAEO,SAAS,YAAY,EAC1B,QAAQ,EACR,aAAa,YAAY,EACL;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,iBAAiB;YAC9B,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;QAAQ;KAAW;IAE/B,IAAI,WAAW,WAAW;QACxB,qBAAO,8OAAC,uIAAA,CAAA,UAAO;YAAC,UAAU;YAAC,SAAQ;;;;;;IACrC;IAEA,IAAI,WAAW,iBAAiB;QAC9B,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 2726, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst inputVariants = cva(\n  \"flex w-full rounded-lg border bg-background px-4 py-3 text-sm font-medium transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border hover:border-secondary-400 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20\",\n        error: \"border-error bg-red-50 focus-visible:border-error focus-visible:ring-2 focus-visible:ring-error/20\",\n        success: \"border-success bg-green-50 focus-visible:border-success focus-visible:ring-2 focus-visible:ring-success/20\",\n        warning: \"border-warning bg-yellow-50 focus-visible:border-warning focus-visible:ring-2 focus-visible:ring-warning/20\",\n      },\n      size: {\n        sm: \"h-8 px-3 py-1.5 text-xs\",\n        default: \"h-10 px-4 py-2.5 text-sm\",\n        lg: \"h-12 px-4 py-3 text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface InputProps\n  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,\n    VariantProps<typeof inputVariants> {\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  error?: string\n  success?: string\n  warning?: string\n  helperText?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ \n    className, \n    variant, \n    size, \n    type, \n    leftIcon, \n    rightIcon, \n    error, \n    success, \n    warning, \n    helperText,\n    ...props \n  }, ref) => {\n    // Determine variant based on validation states\n    const currentVariant = error ? 'error' : success ? 'success' : warning ? 'warning' : variant\n\n    const input = (\n      <input\n        type={type}\n        className={cn(\n          inputVariants({ variant: currentVariant, size }),\n          leftIcon && \"pl-10\",\n          rightIcon && \"pr-10\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n\n    // If no icons, return simple input\n    if (!leftIcon && !rightIcon) {\n      return (\n        <div className=\"w-full\">\n          {input}\n          {(error || success || warning || helperText) && (\n            <div className=\"mt-1.5 text-xs\">\n              {error && <p className=\"text-error flex items-center gap-1\">{error}</p>}\n              {success && <p className=\"text-success flex items-center gap-1\">{success}</p>}\n              {warning && <p className=\"text-warning flex items-center gap-1\">{warning}</p>}\n              {helperText && !error && !success && !warning && (\n                <p className=\"text-muted-foreground\">{helperText}</p>\n              )}\n            </div>\n          )}\n        </div>\n      )\n    }\n\n    // Return input with icon wrapper\n    return (\n      <div className=\"w-full\">\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground\">\n              {leftIcon}\n            </div>\n          )}\n          {input}\n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        {(error || success || warning || helperText) && (\n          <div className=\"mt-1.5 text-xs\">\n            {error && <p className=\"text-error flex items-center gap-1\">{error}</p>}\n            {success && <p className=\"text-success flex items-center gap-1\">{success}</p>}\n            {warning && <p className=\"text-warning flex items-center gap-1\">{warning}</p>}\n            {helperText && !error && !success && !warning && (\n              <p className=\"text-muted-foreground\">{helperText}</p>\n            )}\n          </div>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input, inputVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,2SACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAcF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,UAAU,EACV,GAAG,OACJ,EAAE;IACD,+CAA+C;IAC/C,MAAM,iBAAiB,QAAQ,UAAU,UAAU,YAAY,UAAU,YAAY;IAErF,MAAM,sBACJ,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cAAc;YAAE,SAAS;YAAgB;QAAK,IAC9C,YAAY,SACZ,aAAa,SACb;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;IAIb,mCAAmC;IACnC,IAAI,CAAC,YAAY,CAAC,WAAW;QAC3B,qBACE,8OAAC;YAAI,WAAU;;gBACZ;gBACA,CAAC,SAAS,WAAW,WAAW,UAAU,mBACzC,8OAAC;oBAAI,WAAU;;wBACZ,uBAAS,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAC5D,yBAAW,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;wBAChE,yBAAW,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;wBAChE,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,yBACpC,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;IAMlD;IAEA,iCAAiC;IACjC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBACZ,0BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;oBAGJ;oBACA,2BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAIN,CAAC,SAAS,WAAW,WAAW,UAAU,mBACzC,8OAAC;gBAAI,WAAU;;oBACZ,uBAAS,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;oBAC5D,yBAAW,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;oBAChE,yBAAW,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;oBAChE,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,yBACpC,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;AAMlD;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2913, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {\n    variant?: 'default' | 'error' | 'success' | 'warning'\n    size?: 'sm' | 'default' | 'lg'\n  }\n>(({ className, children, variant = 'default', size = 'default', ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      // Base styles\n      \"flex w-full items-center justify-between rounded-lg border bg-background px-4 py-3 text-sm font-medium transition-all duration-200 data-[placeholder]:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      // Variant styles\n      {\n        \"border-border hover:border-secondary-400 focus:border-primary focus:ring-2 focus:ring-primary/20\": variant === 'default',\n        \"border-error bg-red-50 focus:border-error focus:ring-2 focus:ring-error/20\": variant === 'error',\n        \"border-success bg-green-50 focus:border-success focus:ring-2 focus:ring-success/20\": variant === 'success',\n        \"border-warning bg-yellow-50 focus:border-warning focus:ring-2 focus:ring-warning/20\": variant === 'warning',\n      },\n      // Size styles\n      {\n        \"h-8 px-3 py-1.5 text-xs\": size === 'sm',\n        \"h-10 px-4 py-2.5 text-sm\": size === 'default',\n        \"h-12 px-4 py-3 text-base\": size === 'lg',\n      },\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-lg border border-border bg-popover text-popover-foreground shadow-xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-md py-2.5 pl-10 pr-3 text-sm font-medium text-popover-foreground outline-none transition-colors hover:bg-muted focus:bg-muted data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-primary\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAMnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3E,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,wQACA,iBAAiB;QACjB;YACE,oGAAoG,YAAY;YAChH,8EAA8E,YAAY;YAC1F,sFAAsF,YAAY;YAClG,uFAAuF,YAAY;QACrG,GACA,cAAc;QACd;YACE,2BAA2B,SAAS;YACpC,4BAA4B,SAAS;YACrC,4BAA4B,SAAS;QACvC,GACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+jBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qQACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3117, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst tabsListVariants = cva(\n  \"inline-flex items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-surface-100\",\n        outline: \"bg-transparent border border-border\",\n        underline: \"bg-transparent border-b border-border rounded-none p-0\",\n        pills: \"bg-surface-100 gap-1\",\n      },\n      size: {\n        sm: \"h-8 text-xs\",\n        default: \"h-10 text-sm\",\n        lg: \"h-12 text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst tabsTriggerVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n        outline: \"border border-transparent data-[state=active]:border-primary data-[state=active]:bg-primary/5 data-[state=active]:text-primary\",\n        underline: \"rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary px-4 py-3\",\n        pills: \"rounded-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\",\n      },\n      size: {\n        sm: \"px-2 py-1 text-xs\",\n        default: \"px-3 py-1.5 text-sm\",\n        lg: \"px-4 py-2 text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst Tabs = TabsPrimitive.Root\n\nexport interface TabsListProps\n  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>,\n    VariantProps<typeof tabsListVariants> {}\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  TabsListProps\n>(({ className, variant, size, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(tabsListVariants({ variant, size }), className)}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nexport interface TabsTriggerProps\n  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>,\n    VariantProps<typeof tabsTriggerVariants> {\n  /** Icon to display before text */\n  icon?: React.ReactNode\n  /** Badge/counter to display */\n  badge?: React.ReactNode\n}\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  TabsTriggerProps\n>(({ className, variant, size, icon, badge, children, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(tabsTriggerVariants({ variant, size }), className)}\n    {...props}\n  >\n    <div className=\"flex items-center gap-2\">\n      {icon && <span className=\"shrink-0\">{icon}</span>}\n      <span>{children}</span>\n      {badge && <span className=\"shrink-0\">{badge}</span>}\n    </div>\n  </TabsPrimitive.Trigger>\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent, tabsListVariants, tabsTriggerVariants }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,mBAAmB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACzB,yFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,sBAAsB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAC5B,mSACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAM/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,oBACzC,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;YAAE;YAAS;QAAK,IAAI;QAClD,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAWrD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;YAAE;YAAS;QAAK,IAAI;QACrD,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAU;;gBACZ,sBAAQ,8OAAC;oBAAK,WAAU;8BAAY;;;;;;8BACrC,8OAAC;8BAAM;;;;;;gBACN,uBAAS,8OAAC;oBAAK,WAAU;8BAAY;;;;;;;;;;;;;;;;;AAI5C,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3250, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center gap-1 rounded-full border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-surface-100 text-accent hover:bg-surface-200\",\n        secondary: \"border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n        info: \"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200\",\n        primary: \"border-transparent bg-primary-100 text-primary-900 hover:bg-primary-200\",\n        outline: \"border-border text-accent hover:bg-surface-50\",\n        ghost: \"border-transparent hover:bg-surface-100 text-accent\",\n      },\n      size: {\n        sm: \"px-2 py-0.5 text-xs\",\n        default: \"px-2.5 py-1 text-sm\",\n        lg: \"px-3 py-1.5 text-base\",\n      },\n      dot: {\n        true: \"\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      dot: false,\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {\n  /** Show a colored dot indicator */\n  dot?: boolean\n  /** Icon to display before the text */\n  icon?: React.ReactNode\n  /** Make the badge clickable */\n  onClick?: () => void\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, dot, icon, onClick, children, ...props }, ref) => {\n    const isClickable = !!onClick\n    \n    const getDotColor = () => {\n      switch (variant) {\n        case 'success': return 'bg-green-500'\n        case 'warning': return 'bg-yellow-500'\n        case 'error': return 'bg-red-500'\n        case 'info': return 'bg-blue-500'\n        case 'primary': return 'bg-primary'\n        case 'secondary': return 'bg-secondary'\n        default: return 'bg-accent'\n      }\n    }\n\n    if (isClickable) {\n      return (\n        <button\n          ref={ref as any}\n          type=\"button\"\n          className={cn(\n            badgeVariants({ variant, size }),\n            \"cursor-pointer hover:scale-105 active:scale-95\",\n            className\n          )}\n          onClick={onClick}\n          {...(props as React.ButtonHTMLAttributes<HTMLButtonElement>)}\n        >\n          {dot && (\n            <span\n              className={cn(\n                \"h-1.5 w-1.5 rounded-full\",\n                getDotColor()\n              )}\n            />\n          )}\n          \n          {icon && (\n            <span className=\"shrink-0\">\n              {icon}\n            </span>\n          )}\n          \n          {children}\n        </button>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...(props as React.HTMLAttributes<HTMLDivElement>)}\n      >\n        {dot && (\n          <span\n            className={cn(\n              \"h-1.5 w-1.5 rounded-full\",\n              getDotColor()\n            )}\n          />\n        )}\n        \n        {icon && (\n          <span className=\"shrink-0\">\n            {icon}\n          </span>\n        )}\n        \n        {children}\n      </div>\n    )\n  }\n)\nBadge.displayName = \"Badge\"\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,iKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,KAAK;YACH,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,KAAK;IACP;AACF;AAcF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrE,MAAM,cAAc,CAAC,CAAC;IAEtB,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YACC,KAAK;YACL,MAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cAAc;gBAAE;gBAAS;YAAK,IAC9B,kDACA;YAEF,SAAS;YACR,GAAI,KAAK;;gBAET,qBACC,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;gBAKL,sBACC,8OAAC;oBAAK,WAAU;8BACb;;;;;;gBAIJ;;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAI,KAAK;;YAET,qBACC,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;YAKL,sBACC,8OAAC;gBAAK,WAAU;0BACb;;;;;;YAIJ;;;;;;;AAGP;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3384, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/status-indicator.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { HTMLAttributes } from \"react\"\n\nexport interface StatusIndicatorProps extends HTMLAttributes<HTMLDivElement> {\n  status: \"success\" | \"warning\" | \"error\" | \"info\" | \"default\"\n  size?: \"sm\" | \"md\" | \"lg\"\n  label?: string\n  pulse?: boolean\n}\n\nexport function StatusIndicator({\n  status,\n  size = \"md\",\n  label,\n  pulse = false,\n  className,\n  ...props\n}: StatusIndicatorProps) {\n  const sizeClasses = {\n    sm: \"h-2 w-2\",\n    md: \"h-3 w-3\",\n    lg: \"h-4 w-4\",\n  }\n\n  const statusClasses = {\n    success: \"bg-success\",\n    warning: \"bg-warning\",\n    error: \"bg-error\",\n    info: \"bg-info\",\n    default: \"bg-muted-foreground\",\n  }\n\n  return (\n    <div\n      className={cn(\"flex items-center gap-2\", className)}\n      {...props}\n    >\n      <span className=\"relative flex\">\n        <span\n          className={cn(\n            \"rounded-full\",\n            sizeClasses[size],\n            statusClasses[status],\n            {\n              \"animate-ping absolute inline-flex h-full w-full rounded-full opacity-75\": pulse,\n            }\n          )}\n        />\n        <span\n          className={cn(\n            \"relative inline-flex rounded-full\",\n            sizeClasses[size],\n            statusClasses[status]\n          )}\n        />\n      </span>\n      {label && (\n        <span className=\"text-sm text-muted-foreground\">{label}</span>\n      )}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;;;AAUO,SAAS,gBAAgB,EAC9B,MAAM,EACN,OAAO,IAAI,EACX,KAAK,EACL,QAAQ,KAAK,EACb,SAAS,EACT,GAAG,OACkB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QACN,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,WAAW,CAAC,KAAK,EACjB,aAAa,CAAC,OAAO,EACrB;4BACE,2EAA2E;wBAC7E;;;;;;kCAGJ,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,WAAW,CAAC,KAAK,EACjB,aAAa,CAAC,OAAO;;;;;;;;;;;;YAI1B,uBACC,8OAAC;gBAAK,WAAU;0BAAiC;;;;;;;;;;;;AAIzD", "debugId": null}}, {"offset": {"line": 3454, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/book-card.tsx"], "sourcesContent": ["import Image from \"next/image\"\nimport Link from \"next/link\"\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ead<PERSON> } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { StatusIndicator } from \"@/components/ui/status-indicator\"\nimport { Text } from \"@/components/ui/typography\"\nimport {\n  MoreVertical,\n  Eye,\n  Edit,\n  Upload,\n  Trash2,\n  DollarSign,\n  Star,\n  FileText,\n  Calendar,\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface Book {\n  id: string\n  title: string\n  description: string\n  status: \"draft\" | \"generating\" | \"review\" | \"published\"\n  genre: string\n  pages: number\n  chapters: number\n  createdAt: string\n  updatedAt: string\n  coverUrl?: string | null\n  revenue: number\n  sales: number\n  rating: number\n}\n\ninterface BookCardProps {\n  book: Book\n  onAction: (bookId: string, action: string) => void\n}\n\nconst statusConfig = {\n  draft: { label: \"Draft\", variant: \"secondary\" as const, indicator: \"default\" as const },\n  generating: { label: \"Generating\", variant: \"default\" as const, indicator: \"info\" as const, pulse: true },\n  review: { label: \"Review\", variant: \"outline\" as const, indicator: \"warning\" as const },\n  published: { label: \"Published\", variant: \"default\" as const, indicator: \"success\" as const },\n}\n\nexport function BookCard({ book, onAction }: BookCardProps) {\n  const status = statusConfig[book.status]\n  const isPublished = book.status === \"published\"\n  const canEdit = book.status === \"draft\" || book.status === \"review\"\n  const canPublish = book.status === \"review\"\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      month: \"short\",\n      day: \"numeric\",\n      year: \"numeric\",\n    })\n  }\n\n  return (\n    <Card className=\"group hover:shadow-lg transition-all duration-200 hover:-translate-y-1\">\n      <CardHeader className=\"p-0\">\n        {/* Book Cover */}\n        <div className=\"aspect-[3/4] relative bg-gradient-to-br from-primary/10 to-primary/20 rounded-t-lg overflow-hidden\">\n          {book.coverUrl ? (\n            <Image\n              src={book.coverUrl}\n              alt={book.title}\n              fill\n              className=\"object-cover\"\n            />\n          ) : (\n            <div className=\"flex items-center justify-center h-full\">\n              <FileText className=\"h-16 w-16 text-primary/40\" />\n            </div>\n          )}\n          \n          {/* Status overlay */}\n          <div className=\"absolute top-3 left-3\">\n            <StatusIndicator\n              status={status.indicator}\n              label={status.label}\n              pulse={'pulse' in status ? status.pulse : false}\n            />\n          </div>\n\n          {/* Actions dropdown */}\n          <div className=\"absolute top-3 right-3\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"h-8 w-8 bg-background/80 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity\"\n                >\n                  <MoreVertical className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <DropdownMenuItem onClick={() => onAction(book.id, \"view\")}>\n                  <Eye className=\"mr-2 h-4 w-4\" />\n                  View Details\n                </DropdownMenuItem>\n                {canEdit && (\n                  <DropdownMenuItem onClick={() => onAction(book.id, \"edit\")}>\n                    <Edit className=\"mr-2 h-4 w-4\" />\n                    Edit\n                  </DropdownMenuItem>\n                )}\n                {canPublish && (\n                  <DropdownMenuItem onClick={() => onAction(book.id, \"publish\")}>\n                    <Upload className=\"mr-2 h-4 w-4\" />\n                    Publish\n                  </DropdownMenuItem>\n                )}\n                <DropdownMenuSeparator />\n                <DropdownMenuItem\n                  onClick={() => onAction(book.id, \"delete\")}\n                  className=\"text-destructive\"\n                >\n                  <Trash2 className=\"mr-2 h-4 w-4\" />\n                  Delete\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n\n          {/* Genre badge */}\n          <div className=\"absolute bottom-3 left-3\">\n            <Badge variant=\"secondary\" className=\"text-xs\">\n              {book.genre}\n            </Badge>\n          </div>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"p-4 space-y-3\">\n        {/* Title and Description */}\n        <div>\n          <Link\n            href={`/dashboard/books/${book.id}`}\n            className=\"font-semibold text-lg hover:text-primary transition-colors line-clamp-2\"\n          >\n            {book.title}\n          </Link>\n          <Text variant=\"small\" className=\"text-muted-foreground line-clamp-2 mt-1\">\n            {book.description}\n          </Text>\n        </div>\n\n        {/* Book Stats */}\n        <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n          <div className=\"flex items-center gap-1\">\n            <FileText className=\"h-3 w-3\" />\n            <span>{book.pages} pages</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <span>{book.chapters} chapters</span>\n          </div>\n        </div>\n\n        {/* Performance Stats (for published books) */}\n        {isPublished && (\n          <div className=\"flex items-center justify-between pt-2 border-t\">\n            <div className=\"flex items-center gap-1 text-sm\">\n              <DollarSign className=\"h-3 w-3 text-green-600\" />\n              <span className=\"font-medium\">${book.revenue.toFixed(2)}</span>\n            </div>\n            <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n              <span>{book.sales} sales</span>\n              {book.rating > 0 && (\n                <div className=\"flex items-center gap-1\">\n                  <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                  <span>{book.rating.toFixed(1)}</span>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </CardContent>\n\n      <CardFooter className=\"p-4 pt-0 flex items-center justify-between\">\n        <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n          <Calendar className=\"h-3 w-3\" />\n          <span>Updated {formatDate(book.updatedAt)}</span>\n        </div>\n        \n        <Link href={`/dashboard/books/${book.id}`}>\n          <Button variant=\"ghost\" size=\"sm\">\n            View\n          </Button>\n        </Link>\n      </CardFooter>\n    </Card>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;AAkCA,MAAM,eAAe;IACnB,OAAO;QAAE,OAAO;QAAS,SAAS;QAAsB,WAAW;IAAmB;IACtF,YAAY;QAAE,OAAO;QAAc,SAAS;QAAoB,WAAW;QAAiB,OAAO;IAAK;IACxG,QAAQ;QAAE,OAAO;QAAU,SAAS;QAAoB,WAAW;IAAmB;IACtF,WAAW;QAAE,OAAO;QAAa,SAAS;QAAoB,WAAW;IAAmB;AAC9F;AAEO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAiB;IACxD,MAAM,SAAS,YAAY,CAAC,KAAK,MAAM,CAAC;IACxC,MAAM,cAAc,KAAK,MAAM,KAAK;IACpC,MAAM,UAAU,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,KAAK;IAC3D,MAAM,aAAa,KAAK,MAAM,KAAK;IAEnC,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BAEpB,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,QAAQ,iBACZ,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,QAAQ;4BAClB,KAAK,KAAK,KAAK;4BACf,IAAI;4BACJ,WAAU;;;;;iDAGZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAKxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,+IAAA,CAAA,kBAAe;gCACd,QAAQ,OAAO,SAAS;gCACxB,OAAO,OAAO,KAAK;gCACnB,OAAO,WAAW,SAAS,OAAO,KAAK,GAAG;;;;;;;;;;;sCAK9C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,SAAS,KAAK,EAAE,EAAE;;kEACjD,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGjC,yBACC,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,SAAS,KAAK,EAAE,EAAE;;kEACjD,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAIpC,4BACC,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,SAAS,KAAK,EAAE,EAAE;;kEACjD,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAIvC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0DACtB,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,SAAS,KAAK,EAAE,EAAE;gDACjC,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAQ3C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAMnB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;;0CACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;gCACnC,WAAU;0CAET,KAAK,KAAK;;;;;;0CAEb,8OAAC,sIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAQ,WAAU;0CAC7B,KAAK,WAAW;;;;;;;;;;;;kCAKrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;;4CAAM,KAAK,KAAK;4CAAC;;;;;;;;;;;;;0CAEpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;wCAAM,KAAK,QAAQ;wCAAC;;;;;;;;;;;;;;;;;;oBAKxB,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAK,WAAU;;4CAAc;4CAAE,KAAK,OAAO,CAAC,OAAO,CAAC;;;;;;;;;;;;;0CAEvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAM,KAAK,KAAK;4CAAC;;;;;;;oCACjB,KAAK,MAAM,GAAG,mBACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAM,KAAK,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;;oCAAK;oCAAS,WAAW,KAAK,SAAS;;;;;;;;;;;;;kCAG1C,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;kCACvC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 3944, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/grid.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nexport interface GridProps extends HTMLAttributes<HTMLDivElement> {\n  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12\n  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8\n  responsive?: boolean\n}\n\nconst Grid = forwardRef<HTMLDivElement, GridProps>(\n  ({ className, cols = 12, gap = 4, responsive = true, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid\",\n          {\n            // Responsive grid\n            \"grid-cols-1\": (responsive && cols === 1) || (!responsive && cols === 1),\n            \"grid-cols-1 sm:grid-cols-2\": responsive && cols === 2,\n            \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3\": responsive && cols === 3,\n            \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-4\": responsive && cols === 4,\n            \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5\": responsive && cols === 5,\n            \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6\": responsive && cols === 6,\n            \"grid-cols-1 sm:grid-cols-6 lg:grid-cols-12\": responsive && cols === 12,\n            // Non-responsive grid (cols > 1)\n            \"grid-cols-2\": !responsive && cols === 2,\n            \"grid-cols-3\": !responsive && cols === 3,\n            \"grid-cols-4\": !responsive && cols === 4,\n            \"grid-cols-5\": !responsive && cols === 5,\n            \"grid-cols-6\": !responsive && cols === 6,\n            \"grid-cols-12\": !responsive && cols === 12,\n            // Gap\n            \"gap-0\": gap === 0,\n            \"gap-1\": gap === 1,\n            \"gap-2\": gap === 2,\n            \"gap-3\": gap === 3,\n            \"gap-4\": gap === 4,\n            \"gap-5\": gap === 5,\n            \"gap-6\": gap === 6,\n            \"gap-8\": gap === 8,\n          },\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\nGrid.displayName = \"Grid\"\n\nexport interface GridItemProps extends HTMLAttributes<HTMLDivElement> {\n  span?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12\n  start?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12\n}\n\nconst GridItem = forwardRef<HTMLDivElement, GridItemProps>(\n  ({ className, span, start, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          {\n            \"col-span-1\": span === 1,\n            \"col-span-2\": span === 2,\n            \"col-span-3\": span === 3,\n            \"col-span-4\": span === 4,\n            \"col-span-5\": span === 5,\n            \"col-span-6\": span === 6,\n            \"col-span-7\": span === 7,\n            \"col-span-8\": span === 8,\n            \"col-span-9\": span === 9,\n            \"col-span-10\": span === 10,\n            \"col-span-11\": span === 11,\n            \"col-span-12\": span === 12,\n            \"col-start-1\": start === 1,\n            \"col-start-2\": start === 2,\n            \"col-start-3\": start === 3,\n            \"col-start-4\": start === 4,\n            \"col-start-5\": start === 5,\n            \"col-start-6\": start === 6,\n            \"col-start-7\": start === 7,\n            \"col-start-8\": start === 8,\n            \"col-start-9\": start === 9,\n            \"col-start-10\": start === 10,\n            \"col-start-11\": start === 11,\n            \"col-start-12\": start === 12,\n          },\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\nGridItem.displayName = \"GridItem\"\n\nexport { Grid, GridItem }"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAQA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA;YACE,kBAAkB;YAClB,eAAe,AAAC,cAAc,SAAS,KAAO,CAAC,cAAc,SAAS;YACtE,8BAA8B,cAAc,SAAS;YACrD,6CAA6C,cAAc,SAAS;YACpE,6CAA6C,cAAc,SAAS;YACpE,4DAA4D,cAAc,SAAS;YACnF,4DAA4D,cAAc,SAAS;YACnF,8CAA8C,cAAc,SAAS;YACrE,iCAAiC;YACjC,eAAe,CAAC,cAAc,SAAS;YACvC,eAAe,CAAC,cAAc,SAAS;YACvC,eAAe,CAAC,cAAc,SAAS;YACvC,eAAe,CAAC,cAAc,SAAS;YACvC,eAAe,CAAC,cAAc,SAAS;YACvC,gBAAgB,CAAC,cAAc,SAAS;YACxC,MAAM;YACN,SAAS,QAAQ;YACjB,SAAS,QAAQ;YACjB,SAAS,QAAQ;YACjB,SAAS,QAAQ;YACjB,SAAS,QAAQ;YACjB,SAAS,QAAQ;YACjB,SAAS,QAAQ;YACjB,SAAS,QAAQ;QACnB,GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEF,KAAK,WAAW,GAAG;AAOnB,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACxB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;YACE,cAAc,SAAS;YACvB,cAAc,SAAS;YACvB,cAAc,SAAS;YACvB,cAAc,SAAS;YACvB,cAAc,SAAS;YACvB,cAAc,SAAS;YACvB,cAAc,SAAS;YACvB,cAAc,SAAS;YACvB,cAAc,SAAS;YACvB,eAAe,SAAS;YACxB,eAAe,SAAS;YACxB,eAAe,SAAS;YACxB,eAAe,UAAU;YACzB,eAAe,UAAU;YACzB,eAAe,UAAU;YACzB,eAAe,UAAU;YACzB,eAAe,UAAU;YACzB,eAAe,UAAU;YACzB,eAAe,UAAU;YACzB,eAAe,UAAU;YACzB,eAAe,UAAU;YACzB,gBAAgB,UAAU;YAC1B,gBAAgB,UAAU;YAC1B,gBAAgB,UAAU;QAC5B,GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4035, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/stats-cards.tsx"], "sourcesContent": ["import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Grid } from \"@/components/ui/grid\"\nimport {\n  BookOpen,\n  Upload,\n  DollarSign,\n  TrendingUp,\n  Clock,\n  Eye,\n} from \"lucide-react\"\nimport { Book } from \"./book-card\"\n\ninterface StatsCardsProps {\n  books: Book[]\n}\n\nexport function StatsCards({ books }: StatsCardsProps) {\n  // Calculate stats\n  const totalBooks = books.length\n  const publishedBooks = books.filter(book => book.status === \"published\").length\n  const draftBooks = books.filter(book => book.status === \"draft\").length\n  const generatingBooks = books.filter(book => book.status === \"generating\").length\n  \n  const totalRevenue = books.reduce((sum, book) => sum + book.revenue, 0)\n  const totalSales = books.reduce((sum, book) => sum + book.sales, 0)\n  \n  const avgRating = books.length > 0 \n    ? books.filter(book => book.rating > 0).reduce((sum, book) => sum + book.rating, 0) / \n      books.filter(book => book.rating > 0).length\n    : 0\n\n  const recentBooks = books.filter(book => {\n    const createdDate = new Date(book.createdAt)\n    const weekAgo = new Date()\n    weekAgo.setDate(weekAgo.getDate() - 7)\n    return createdDate >= weekAgo\n  }).length\n\n  const stats = [\n    {\n      title: \"Total Books\",\n      value: totalBooks.toString(),\n      description: `${publishedBooks} published, ${draftBooks} drafts`,\n      icon: BookOpen,\n      trend: recentBooks > 0 ? `+${recentBooks} this week` : \"No new books this week\",\n      trendUp: recentBooks > 0,\n    },\n    {\n      title: \"Published\",\n      value: publishedBooks.toString(),\n      description: `${((publishedBooks / Math.max(totalBooks, 1)) * 100).toFixed(0)}% of total`,\n      icon: Upload,\n      trend: generatingBooks > 0 ? `${generatingBooks} generating` : \"Ready to publish\",\n      trendUp: generatingBooks > 0,\n    },\n    {\n      title: \"Total Revenue\",\n      value: `$${totalRevenue.toFixed(2)}`,\n      description: `From ${totalSales} sales`,\n      icon: DollarSign,\n      trend: totalSales > 0 ? `$${(totalRevenue / Math.max(totalSales, 1)).toFixed(2)} avg per sale` : \"No sales yet\",\n      trendUp: totalRevenue > 0,\n    },\n    {\n      title: \"Average Rating\",\n      value: avgRating > 0 ? avgRating.toFixed(1) : \"N/A\",\n      description: avgRating > 0 ? \"Across published books\" : \"No ratings yet\",\n      icon: TrendingUp,\n      trend: avgRating > 0 ? (avgRating >= 4 ? \"Excellent!\" : avgRating >= 3.5 ? \"Good\" : \"Needs improvement\") : \"Publish to get ratings\",\n      trendUp: avgRating >= 4,\n    },\n  ]\n\n  return (\n    <div className=\"mb-8\">\n      <Grid cols={4} gap={6} className=\"grid-cols-1 sm:grid-cols-2 lg:grid-cols-4\">\n        {stats.map((stat, index) => {\n          const Icon = stat.icon\n          return (\n            <Card key={index}>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                  {stat.title}\n                </CardTitle>\n                <Icon className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stat.value}</div>\n                <p className=\"text-xs text-muted-foreground mt-1\">\n                  {stat.description}\n                </p>\n                <div className=\"flex items-center mt-2\">\n                  <Badge \n                    variant={stat.trendUp ? \"default\" : \"secondary\"}\n                    className=\"text-xs\"\n                  >\n                    {stat.trendUp ? (\n                      <TrendingUp className=\"h-3 w-3 mr-1\" />\n                    ) : (\n                      <Clock className=\"h-3 w-3 mr-1\" />\n                    )}\n                    {stat.trend}\n                  </Badge>\n                </div>\n              </CardContent>\n            </Card>\n          )\n        })}\n      </Grid>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAcO,SAAS,WAAW,EAAE,KAAK,EAAmB;IACnD,kBAAkB;IAClB,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;IAC/E,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,SAAS,MAAM;IACvE,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,cAAc,MAAM;IAEjF,MAAM,eAAe,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,OAAO,EAAE;IACrE,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAEjE,MAAM,YAAY,MAAM,MAAM,GAAG,IAC7B,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE,KAC/E,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM,GAC5C;IAEJ,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA;QAC/B,MAAM,cAAc,IAAI,KAAK,KAAK,SAAS;QAC3C,MAAM,UAAU,IAAI;QACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;QACpC,OAAO,eAAe;IACxB,GAAG,MAAM;IAET,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,WAAW,QAAQ;YAC1B,aAAa,GAAG,eAAe,YAAY,EAAE,WAAW,OAAO,CAAC;YAChE,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO,cAAc,IAAI,CAAC,CAAC,EAAE,YAAY,UAAU,CAAC,GAAG;YACvD,SAAS,cAAc;QACzB;QACA;YACE,OAAO;YACP,OAAO,eAAe,QAAQ;YAC9B,aAAa,GAAG,CAAC,AAAC,iBAAiB,KAAK,GAAG,CAAC,YAAY,KAAM,GAAG,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC;YACzF,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO,kBAAkB,IAAI,GAAG,gBAAgB,WAAW,CAAC,GAAG;YAC/D,SAAS,kBAAkB;QAC7B;QACA;YACE,OAAO;YACP,OAAO,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,IAAI;YACpC,aAAa,CAAC,KAAK,EAAE,WAAW,MAAM,CAAC;YACvC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,eAAe,KAAK,GAAG,CAAC,YAAY,EAAE,EAAE,OAAO,CAAC,GAAG,aAAa,CAAC,GAAG;YACjG,SAAS,eAAe;QAC1B;QACA;YACE,OAAO;YACP,OAAO,YAAY,IAAI,UAAU,OAAO,CAAC,KAAK;YAC9C,aAAa,YAAY,IAAI,2BAA2B;YACxD,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO,YAAY,IAAK,aAAa,IAAI,eAAe,aAAa,MAAM,SAAS,sBAAuB;YAC3G,SAAS,aAAa;QACxB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,MAAM;YAAG,KAAK;YAAG,WAAU;sBAC9B,MAAM,GAAG,CAAC,CAAC,MAAM;gBAChB,MAAM,OAAO,KAAK,IAAI;gBACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAK,WAAU;;;;;;;;;;;;sCAElB,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;oCAAI,WAAU;8CAAsB,KAAK,KAAK;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCACJ,SAAS,KAAK,OAAO,GAAG,YAAY;wCACpC,WAAU;;4CAET,KAAK,OAAO,iBACX,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;qEAEtB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAElB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;mBAtBR;;;;;YA4Bf;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 4214, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/empty-state.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { LucideIcon } from \"lucide-react\"\nimport { HTMLAttributes } from \"react\"\nimport { Button } from \"./button\"\nimport { Heading, Text } from \"./typography\"\n\nexport interface EmptyStateProps extends HTMLAttributes<HTMLDivElement> {\n  icon?: LucideIcon\n  title: string\n  description?: string\n  action?: {\n    label: string\n    onClick: () => void\n  }\n}\n\nexport function EmptyState({\n  icon: Icon,\n  title,\n  description,\n  action,\n  className,\n  ...props\n}: EmptyStateProps) {\n  return (\n    <div\n      className={cn(\n        \"flex flex-col items-center justify-center py-12 px-4 text-center\",\n        className\n      )}\n      {...props}\n    >\n      {Icon && (\n        <div className=\"rounded-full bg-surface p-3 mb-4\">\n          <Icon className=\"h-6 w-6 text-muted-foreground\" />\n        </div>\n      )}\n      <Heading as=\"h3\" variant=\"h5\" className=\"mb-2\">\n        {title}\n      </Heading>\n      {description && (\n        <Text variant=\"muted\" className=\"mb-6 max-w-sm\">\n          {description}\n        </Text>\n      )}\n      {action && (\n        <Button onClick={action.onClick}>\n          {action.label}\n        </Button>\n      )}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AAGA;AACA;;;;;AAYO,SAAS,WAAW,EACzB,MAAM,IAAI,EACV,KAAK,EACL,WAAW,EACX,MAAM,EACN,SAAS,EACT,GAAG,OACa;IAChB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;YAER,sBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;0BAGpB,8OAAC,sIAAA,CAAA,UAAO;gBAAC,IAAG;gBAAK,SAAQ;gBAAK,WAAU;0BACrC;;;;;;YAEF,6BACC,8OAAC,sIAAA,CAAA,OAAI;gBAAC,SAAQ;gBAAQ,WAAU;0BAC7B;;;;;;YAGJ,wBACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,SAAS,OAAO,OAAO;0BAC5B,OAAO,KAAK;;;;;;;;;;;;AAKvB", "debugId": null}}, {"offset": {"line": 4284, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/books/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useSession } from \"next-auth/react\"\nimport { DashboardLayout } from \"@/components/layout/dashboard-layout\"\nimport { PageHeader } from \"@/components/layout/page-header\"\nimport { ProtectedRoute } from \"@/components/auth/protected-route\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\"\nimport { BookCard } from \"@/components/books/book-card\"\nimport { StatsCards } from \"@/components/books/stats-cards\"\nimport { EmptyState } from \"@/components/ui/empty-state\"\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\"\nimport { Grid } from \"@/components/ui/grid\"\nimport { PlusCircle, Search, Filter, BookOpen } from \"lucide-react\"\n\n// Mock data for development\nconst mockBooks = [\n  {\n    id: \"1\",\n    title: \"The Future of AI in Business\",\n    description: \"A comprehensive guide to implementing artificial intelligence in modern business operations.\",\n    status: \"published\" as const,\n    genre: \"Business\",\n    pages: 245,\n    chapters: 12,\n    createdAt: \"2024-01-15\",\n    updatedAt: \"2024-01-20\",\n    coverUrl: \"/images/book-covers/ai-business.jpg\",\n    revenue: 1250.50,\n    sales: 89,\n    rating: 4.2,\n  },\n  {\n    id: \"2\",\n    title: \"Digital Marketing Mastery\",\n    description: \"Learn the latest strategies for dominating digital marketing in 2024.\",\n    status: \"draft\" as const,\n    genre: \"Marketing\",\n    pages: 198,\n    chapters: 10,\n    createdAt: \"2024-01-10\",\n    updatedAt: \"2024-01-18\",\n    coverUrl: \"/images/book-covers/digital-marketing.jpg\",\n    revenue: 0,\n    sales: 0,\n    rating: 0,\n  },\n  {\n    id: \"3\",\n    title: \"Sustainable Living Guide\",\n    description: \"Practical tips for living an eco-friendly lifestyle in the modern world.\",\n    status: \"generating\" as const,\n    genre: \"Lifestyle\",\n    pages: 0,\n    chapters: 8,\n    createdAt: \"2024-01-22\",\n    updatedAt: \"2024-01-22\",\n    coverUrl: null,\n    revenue: 0,\n    sales: 0,\n    rating: 0,\n  },\n  {\n    id: \"4\",\n    title: \"Cryptocurrency Investment Strategies\",\n    description: \"Expert insights into building a profitable crypto portfolio.\",\n    status: \"review\" as const,\n    genre: \"Finance\",\n    pages: 312,\n    chapters: 15,\n    createdAt: \"2024-01-05\",\n    updatedAt: \"2024-01-19\",\n    coverUrl: \"/images/book-covers/crypto-investment.jpg\",\n    revenue: 0,\n    sales: 0,\n    rating: 0,\n  },\n]\n\nexport default function BooksPage() {\n  const { data: session } = useSession()\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [selectedGenre, setSelectedGenre] = useState(\"all\")\n  const [selectedStatus, setSelectedStatus] = useState(\"all\")\n  const [isLoading, setIsLoading] = useState(false)\n\n  const user = session?.user\n    ? {\n        name: session.user.name || \"User\",\n        email: session.user.email || \"\",\n        avatar: session.user.image || undefined,\n      }\n    : undefined\n\n  // Filter books based on search and filters\n  const filteredBooks = mockBooks.filter((book) => {\n    const matchesSearch = book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         book.description.toLowerCase().includes(searchQuery.toLowerCase())\n    const matchesGenre = selectedGenre === \"all\" || book.genre.toLowerCase() === selectedGenre\n    const matchesStatus = selectedStatus === \"all\" || book.status === selectedStatus\n    \n    return matchesSearch && matchesGenre && matchesStatus\n  })\n\n  const handleCreateBook = () => {\n    // Navigate to book creation flow\n    console.log(\"Create new book\")\n  }\n\n  const handleBookAction = (bookId: string, action: string) => {\n    console.log(`${action} book ${bookId}`)\n  }\n\n  const genres = [\"all\", \"business\", \"marketing\", \"lifestyle\", \"finance\", \"technology\", \"self-help\"]\n  const statuses = [\"all\", \"draft\", \"generating\", \"review\", \"published\"]\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"p-6\">\n          <PageHeader\n            title=\"My Books\"\n            description=\"Manage your book collection, track performance, and create new publications.\"\n            actions={\n              <Button onClick={handleCreateBook} className=\"gap-2\">\n                <PlusCircle className=\"h-4 w-4\" />\n                Create New Book\n              </Button>\n            }\n          />\n\n          {/* Stats Cards */}\n          <StatsCards books={mockBooks} />\n\n          {/* Filters and Search */}\n          <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n            <div className=\"relative flex-1\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search books by title or description...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n            <div className=\"flex gap-2\">\n              <Select value={selectedGenre} onValueChange={setSelectedGenre}>\n                <SelectTrigger className=\"w-40\">\n                  <SelectValue placeholder=\"Genre\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {genres.map((genre) => (\n                    <SelectItem key={genre} value={genre}>\n                      {genre === \"all\" ? \"All Genres\" : genre.charAt(0).toUpperCase() + genre.slice(1)}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              <Select value={selectedStatus} onValueChange={setSelectedStatus}>\n                <SelectTrigger className=\"w-40\">\n                  <SelectValue placeholder=\"Status\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {statuses.map((status) => (\n                    <SelectItem key={status} value={status}>\n                      {status === \"all\" ? \"All Status\" : status.charAt(0).toUpperCase() + status.slice(1)}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Tabs for different views */}\n          <Tabs defaultValue=\"grid\" className=\"space-y-6\">\n            <TabsList>\n              <TabsTrigger value=\"grid\">Grid View</TabsTrigger>\n              <TabsTrigger value=\"list\">List View</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"grid\" className=\"space-y-6\">\n              {isLoading ? (\n                <div className=\"flex justify-center py-12\">\n                  <LoadingSpinner size=\"lg\" />\n                </div>\n              ) : filteredBooks.length > 0 ? (\n                <Grid cols={3} gap={6} className=\"grid-cols-1 md:grid-cols-2 xl:grid-cols-3\">\n                  {filteredBooks.map((book) => (\n                    <BookCard\n                      key={book.id}\n                      book={book}\n                      onAction={handleBookAction}\n                    />\n                  ))}\n                </Grid>\n              ) : (\n                <EmptyState\n                  icon={BookOpen}\n                  title={searchQuery || selectedGenre !== \"all\" || selectedStatus !== \"all\" \n                    ? \"No books found\" \n                    : \"No books yet\"\n                  }\n                  description={\n                    searchQuery || selectedGenre !== \"all\" || selectedStatus !== \"all\"\n                      ? \"Try adjusting your search criteria or filters.\"\n                      : \"Get started by creating your first AI-generated book.\"\n                  }\n                  action={\n                    searchQuery || selectedGenre !== \"all\" || selectedStatus !== \"all\"\n                      ? undefined\n                      : {\n                          label: \"Create Your First Book\",\n                          onClick: handleCreateBook,\n                        }\n                  }\n                />\n              )}\n            </TabsContent>\n\n            <TabsContent value=\"list\" className=\"space-y-4\">\n              {/* List view would go here - simpler table format */}\n              <div className=\"text-center py-12 text-muted-foreground\">\n                List view coming soon...\n              </div>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAhBA;;;;;;;;;;;;;;;;;AAkBA,4BAA4B;AAC5B,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;QACX,WAAW;QACX,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;QACX,WAAW;QACX,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;QACX,WAAW;QACX,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;QACX,WAAW;QACX,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,SAAS,OAClB;QACE,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;QAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;QAC7B,QAAQ,QAAQ,IAAI,CAAC,KAAK,IAAI;IAChC,IACA;IAEJ,2CAA2C;IAC3C,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAC;QACtC,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACpF,MAAM,eAAe,kBAAkB,SAAS,KAAK,KAAK,CAAC,WAAW,OAAO;QAC7E,MAAM,gBAAgB,mBAAmB,SAAS,KAAK,MAAM,KAAK;QAElE,OAAO,iBAAiB,gBAAgB;IAC1C;IAEA,MAAM,mBAAmB;QACvB,iCAAiC;QACjC,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,QAAQ,GAAG,CAAC,GAAG,OAAO,MAAM,EAAE,QAAQ;IACxC;IAEA,MAAM,SAAS;QAAC;QAAO;QAAY;QAAa;QAAa;QAAW;QAAc;KAAY;IAClG,MAAM,WAAW;QAAC;QAAO;QAAS;QAAc;QAAU;KAAY;IAEtE,qBACE,8OAAC,gJAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,mJAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,aAAU;wBACT,OAAM;wBACN,aAAY;wBACZ,uBACE,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAkB,WAAU;;8CAC3C,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAOxC,8OAAC,6IAAA,CAAA,aAAU;wBAAC,OAAO;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAe,eAAe;;0DAC3C,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;0DACX,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,kIAAA,CAAA,aAAU;wDAAa,OAAO;kEAC5B,UAAU,QAAQ,eAAe,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC;uDAD/D;;;;;;;;;;;;;;;;kDAMvB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAgB,eAAe;;0DAC5C,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;0DACX,SAAS,GAAG,CAAC,CAAC,uBACb,8OAAC,kIAAA,CAAA,aAAU;wDAAc,OAAO;kEAC7B,WAAW,QAAQ,eAAe,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;uDADlE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU3B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAO,WAAU;;0CAClC,8OAAC,gIAAA,CAAA,WAAQ;;kDACP,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAO;;;;;;kDAC1B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAO;;;;;;;;;;;;0CAG5B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;0CACjC,0BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCAAC,MAAK;;;;;;;;;;2CAErB,cAAc,MAAM,GAAG,kBACzB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,MAAM;oCAAG,KAAK;oCAAG,WAAU;8CAC9B,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,2IAAA,CAAA,WAAQ;4CAEP,MAAM;4CACN,UAAU;2CAFL,KAAK,EAAE;;;;;;;;;yDAOlB,8OAAC,0IAAA,CAAA,aAAU;oCACT,MAAM,8MAAA,CAAA,WAAQ;oCACd,OAAO,eAAe,kBAAkB,SAAS,mBAAmB,QAChE,mBACA;oCAEJ,aACE,eAAe,kBAAkB,SAAS,mBAAmB,QACzD,mDACA;oCAEN,QACE,eAAe,kBAAkB,SAAS,mBAAmB,QACzD,YACA;wCACE,OAAO;wCACP,SAAS;oCACX;;;;;;;;;;;0CAMZ,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;0CAElC,cAAA,8OAAC;oCAAI,WAAU;8CAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvE", "debugId": null}}]}