{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesLoadingSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BridgesLoadingSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call BridgesLoadingSkeleton() from the server but BridgesLoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/trends/BridgesLoadingSkeleton.tsx <module evaluation>\",\n    \"BridgesLoadingSkeleton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,4FACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesLoadingSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BridgesLoadingSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call BridgesLoadingSkeleton() from the server but BridgesLoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/trends/BridgesLoadingSkeleton.tsx\",\n    \"BridgesLoadingSkeleton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,wEACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/trends/loading.tsx"], "sourcesContent": ["import { BridgesLoadingSkeleton } from '@/components/dashboard/trends/BridgesLoadingSkeleton'\n\nexport default function Loading() {\n  return <BridgesLoadingSkeleton type=\"page\" />\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,mKAAA,CAAA,yBAAsB;QAAC,MAAK;;;;;;AACtC", "debugId": null}}]}