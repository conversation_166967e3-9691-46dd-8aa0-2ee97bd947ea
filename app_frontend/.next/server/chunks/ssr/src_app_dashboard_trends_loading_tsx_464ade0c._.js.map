{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/trends/loading.tsx"], "sourcesContent": ["import { LoadingSkeleton } from '@/components/dashboard/trends/LoadingSkeleton'\n\nexport default function Loading() {\n  return <LoadingSkeleton type=\"page\" />\n}"], "names": [], "mappings": ";;;;;;;;;;;AAEe,SAAS;IACtB,qBAAO,8OAAC;QAAgB,MAAK;;;;;;AAC/B", "debugId": null}}]}