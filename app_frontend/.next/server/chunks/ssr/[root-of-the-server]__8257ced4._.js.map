{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosError, AxiosRequestConfig } from \"axios\";\nimport { toast } from \"react-hot-toast\";\nimport { getSession } from \"next-auth/react\";\n\nexport interface ApiError {\n  message: string;\n  status?: number;\n  code?: string;\n  details?: any;\n}\n\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  status: number;\n}\n\nexport interface LoadingState {\n  isLoading: boolean;\n  error: ApiError | null;\n}\n\n// Cache configuration\ninterface CacheConfig {\n  ttl: number; // Time to live in milliseconds\n  maxSize: number;\n}\n\ninterface CacheEntry<T> {\n  data: T;\n  timestamp: number;\n  ttl: number;\n}\n\nclass ApiCache {\n  private cache = new Map<string, CacheEntry<any>>();\n  private config: CacheConfig = {\n    ttl: 5 * 60 * 1000, // 5 minutes default\n    maxSize: 100,\n  };\n\n  set<T>(key: string, data: T, ttl?: number): void {\n    // Remove oldest entries if cache is full\n    if (this.cache.size >= this.config.maxSize) {\n      const firstKey = this.cache.keys().next().value;\n      if (!firstKey) return;\n      this.cache.delete(firstKey);\n    }\n\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl: ttl || this.config.ttl,\n    });\n  }\n\n  get<T>(key: string): T | null {\n    const entry = this.cache.get(key);\n    if (!entry) return null;\n\n    // Check if expired\n    if (Date.now() - entry.timestamp > entry.ttl) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return entry.data;\n  }\n\n  clear(): void {\n    this.cache.clear();\n  }\n\n  delete(key: string): void {\n    this.cache.delete(key);\n  }\n}\n\n// Retry configuration\ninterface RetryConfig {\n  retries: number;\n  retryDelay: number;\n  retryCondition?: (error: AxiosError) => boolean;\n}\n\nclass ApiClient {\n  private client: AxiosInstance;\n  private cache = new ApiCache();\n  private loadingStates = new Map<string, boolean>();\n  private retryConfig: RetryConfig = {\n    retries: 3,\n    retryDelay: 1000,\n    retryCondition: (error: AxiosError) => {\n      // Retry on network errors and 5xx status codes\n      return (\n        !error.response ||\n        (error.response.status >= 500 && error.response.status < 600)\n      );\n    },\n  };\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      timeout: 5000, // Reduced timeout for faster failure detection\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor\n    this.client.interceptors.request.use(\n      async (config) => {\n        // Add auth token if available - use NextAuth session instead of localStorage\n        if (typeof window !== \"undefined\") {\n          try {\n            const session = await getSession();\n            \n            // Check for NextAuth session first\n            if (session?.accessToken) {\n              config.headers.Authorization = `Bearer ${session.accessToken}`;\n            } else {\n              // Fallback to localStorage for backward compatibility\n              const token = localStorage.getItem(\"auth_token\");\n              if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n              }\n            }\n          } catch (error) {\n            // If getSession fails, try localStorage as fallback\n            const token = localStorage.getItem(\"auth_token\");\n            if (token) {\n              config.headers.Authorization = `Bearer ${token}`;\n            }\n          }\n        }\n\n        // Add request ID for tracking\n        config.headers[\"X-Request-ID\"] = crypto.randomUUID();\n\n        // Set loading state\n        const requestKey = this.getRequestKey(config);\n        this.setLoading(requestKey, true);\n\n        // Security: Never log requests containing sensitive data\n        if (process.env.NODE_ENV === \"development\") {\n          const hasCredentials =\n            config.url?.includes(\"/auth/\") ||\n            config.data?.password ||\n            config.data?.token;\n          if (!hasCredentials) {\n            // Only log non-sensitive requests in development\n          }\n        }\n\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor with retry logic\n    this.client.interceptors.response.use(\n      (response) => {\n        // Clear loading state\n        const requestKey = this.getRequestKey(response.config);\n        this.setLoading(requestKey, false);\n        return response;\n      },\n      async (error: AxiosError) => {\n        const config = error.config as AxiosRequestConfig & {\n          _retryCount?: number;\n        };\n        const requestKey = this.getRequestKey(config);\n        this.setLoading(requestKey, false);\n\n        // Retry logic\n        if (this.shouldRetry(error, config)) {\n          config._retryCount = (config._retryCount || 0) + 1;\n\n          // Exponential backoff\n          const delay =\n            this.retryConfig.retryDelay * Math.pow(2, config._retryCount - 1);\n          await new Promise((resolve) => setTimeout(resolve, delay));\n\n          return this.client(config);\n        }\n\n        const apiError: ApiError = {\n          message: error.message || \"An unexpected error occurred\",\n          status: error.response?.status,\n          details: error.response?.data,\n        };\n\n        if (error.response?.data) {\n          const errorData = error.response.data as any;\n          apiError.message =\n            errorData.detail || errorData.message || apiError.message;\n          apiError.code = errorData.code;\n        }\n\n        // Handle different error types\n        this.handleError(apiError);\n\n        return Promise.reject(apiError);\n      }\n    );\n  }\n\n  private shouldRetry(\n    error: AxiosError,\n    config: AxiosRequestConfig & { _retryCount?: number }\n  ): boolean {\n    const retryCount = config._retryCount || 0;\n    return (\n      retryCount < this.retryConfig.retries &&\n      config.method?.toLowerCase() === \"get\" && // Only retry GET requests\n      this.retryConfig.retryCondition?.(error) === true\n    );\n  }\n\n  private handleError(error: ApiError): void {\n    // Handle connection errors (backend not running)\n    if (\n      !error.status ||\n      error.message.includes(\"Network Error\") ||\n      error.message.includes(\"ECONNREFUSED\")\n    ) {\n      // Don't show error toast for connection issues when backend is expected to be down\n      return;\n    }\n\n    // Handle auth errors\n    if (error.status === 401) {\n      if (typeof window !== \"undefined\") {\n        localStorage.removeItem(\"auth_token\");\n        toast.error(\"Session expired. Please login again.\");\n        window.location.href = \"/auth/login\";\n      }\n      return;\n    }\n\n    // Handle other errors with toast notifications\n    if (error.status && error.status >= 400) {\n      if (error.status >= 500) {\n        toast.error(\"Server error. Please try again later.\");\n      } else if (error.status === 404) {\n        toast.error(\"Resource not found\");\n      } else {\n        toast.error(error.message);\n      }\n    }\n  }\n\n  private getRequestKey(config: AxiosRequestConfig): string {\n    return `${config.method?.toUpperCase()}_${config.url}`;\n  }\n\n  private setLoading(key: string, loading: boolean): void {\n    if (loading) {\n      this.loadingStates.set(key, true);\n    } else {\n      this.loadingStates.delete(key);\n    }\n  }\n\n  // Generic request methods with caching\n  async get<T>(url: string, params?: any, useCache = true): Promise<T> {\n    const cacheKey = `GET_${url}_${JSON.stringify(params || {})}`;\n\n    // Check cache first\n    if (useCache) {\n      const cached = this.cache.get<T>(cacheKey);\n      if (cached) {\n        return cached;\n      }\n    }\n\n    const response = await this.client.get(url, { params });\n    const data = response.data;\n\n    // Cache successful GET requests\n    if (useCache && response.status === 200) {\n      this.cache.set(cacheKey, data);\n    }\n\n    return data;\n  }\n\n  async post<T>(url: string, data?: any): Promise<T> {\n    // If no API URL is set, return mock response\n    console.log(\"API URL: \", process.env.NEXT_PUBLIC_API_URL);\n    if (!process.env.NEXT_PUBLIC_API_URL) {\n      throw new Error(\"Backend not available\");\n    }\n\n    const response = await this.client.post(url, data);\n\n    // Invalidate related cache entries\n    this.invalidateCache(url);\n\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: any): Promise<T> {\n    const response = await this.client.put(url, data);\n\n    // Invalidate related cache entries\n    this.invalidateCache(url);\n\n    return response.data;\n  }\n\n  async patch<T>(url: string, data?: any): Promise<T> {\n    const response = await this.client.patch(url, data);\n\n    // Invalidate related cache entries\n    this.invalidateCache(url);\n\n    return response.data;\n  }\n\n  async delete<T>(url: string): Promise<T> {\n    const response = await this.client.delete(url);\n\n    // Invalidate related cache entries\n    this.invalidateCache(url);\n\n    return response.data;\n  }\n\n  // File upload with progress\n  async uploadFile<T>(\n    url: string,\n    file: File | Blob,\n    onProgress?: (progress: number) => void\n  ): Promise<T> {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n\n    const response = await this.client.post(url, formData, {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round(\n            (progressEvent.loaded * 100) / progressEvent.total\n          );\n          onProgress(progress);\n        }\n      },\n    });\n\n    return response.data;\n  }\n\n  // Optimistic updates\n  async optimisticUpdate<T>(\n    url: string,\n    data: any,\n    optimisticData: T,\n    cacheKey?: string\n  ): Promise<T> {\n    // Set optimistic data immediately\n    if (cacheKey) {\n      this.cache.set(cacheKey, optimisticData);\n    }\n\n    try {\n      const result = await this.put<T>(url, data);\n\n      // Update cache with real data\n      if (cacheKey) {\n        this.cache.set(cacheKey, result);\n      }\n\n      return result;\n    } catch (error) {\n      // Revert optimistic update on error\n      if (cacheKey) {\n        this.cache.delete(cacheKey);\n      }\n      throw error;\n    }\n  }\n\n  // Cache management\n  private invalidateCache(url: string): void {\n    // Remove cache entries that might be affected by this change\n    const keysToDelete: string[] = [];\n\n    // Simple pattern matching - could be more sophisticated\n    const baseUrl = url.split(\"/\").slice(0, -1).join(\"/\");\n\n    for (const key of Array.from(this.cache[\"cache\"].keys())) {\n      if (key.includes(baseUrl) || key.includes(url)) {\n        keysToDelete.push(key);\n      }\n    }\n\n    keysToDelete.forEach((key) => this.cache.delete(key));\n  }\n\n  // Loading state management\n  isLoading(url: string, method = \"GET\"): boolean {\n    const key = `${method.toUpperCase()}_${url}`;\n    return this.loadingStates.has(key);\n  }\n\n  // Utility methods\n  setAuthToken(token: string) {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(\"auth_token\", token);\n    }\n  }\n\n  removeAuthToken() {\n    if (typeof window !== \"undefined\") {\n      localStorage.removeItem(\"auth_token\");\n    }\n  }\n\n  // Cache utilities\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  getCacheStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache[\"cache\"].size,\n      keys: Array.from(this.cache[\"cache\"].keys()),\n    };\n  }\n\n  // Health check\n  async healthCheck(): Promise<boolean> {\n    try {\n      await this.get(\"/health\", {}, false); // Don't cache health checks\n      return true;\n    } catch {\n      return false;\n    }\n  }\n}\n\nexport const apiClient = new ApiClient();\nexport default apiClient;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAgCA,MAAM;IACI,QAAQ,IAAI,MAA+B;IAC3C,SAAsB;QAC5B,KAAK,IAAI,KAAK;QACd,SAAS;IACX,EAAE;IAEF,IAAO,GAAW,EAAE,IAAO,EAAE,GAAY,EAAQ;QAC/C,yCAAyC;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YAC1C,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK;YAC/C,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACpB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG;QAC7B;IACF;IAEA,IAAO,GAAW,EAAY;QAC5B,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,OAAO,OAAO;QAEnB,mBAAmB;QACnB,IAAI,KAAK,GAAG,KAAK,MAAM,SAAS,GAAG,MAAM,GAAG,EAAE;YAC5C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,MAAM,IAAI;IACnB;IAEA,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,OAAO,GAAW,EAAQ;QACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACpB;AACF;AASA,MAAM;IACI,OAAsB;IACtB,QAAQ,IAAI,WAAW;IACvB,gBAAgB,IAAI,MAAuB;IAC3C,cAA2B;QACjC,SAAS;QACT,YAAY;QACZ,gBAAgB,CAAC;YACf,+CAA+C;YAC/C,OACE,CAAC,MAAM,QAAQ,IACd,MAAM,QAAQ,CAAC,MAAM,IAAI,OAAO,MAAM,QAAQ,CAAC,MAAM,GAAG;QAE7D;IACF,EAAE;IAEF,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACzB,SAAS,6DAAmC;YAC5C,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,OAAO;YACL,6EAA6E;YAC7E,uCAAmC;;YAqBnC;YAEA,8BAA8B;YAC9B,OAAO,OAAO,CAAC,eAAe,GAAG,OAAO,UAAU;YAElD,oBAAoB;YACpB,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,YAAY;YAE5B,yDAAyD;YACzD,wCAA4C;gBAC1C,MAAM,iBACJ,OAAO,GAAG,EAAE,SAAS,aACrB,OAAO,IAAI,EAAE,YACb,OAAO,IAAI,EAAE;gBACf,IAAI,CAAC,gBAAgB;gBACnB,iDAAiD;gBACnD;YACF;YAEA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,wCAAwC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC;YACC,sBAAsB;YACtB,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,MAAM;YACrD,IAAI,CAAC,UAAU,CAAC,YAAY;YAC5B,OAAO;QACT,GACA,OAAO;YACL,MAAM,SAAS,MAAM,MAAM;YAG3B,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,YAAY;YAE5B,cAAc;YACd,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,SAAS;gBACnC,OAAO,WAAW,GAAG,CAAC,OAAO,WAAW,IAAI,CAAC,IAAI;gBAEjD,sBAAsB;gBACtB,MAAM,QACJ,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG;gBACjE,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBAEnD,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB;YAEA,MAAM,WAAqB;gBACzB,SAAS,MAAM,OAAO,IAAI;gBAC1B,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS,MAAM,QAAQ,EAAE;YAC3B;YAEA,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,MAAM,YAAY,MAAM,QAAQ,CAAC,IAAI;gBACrC,SAAS,OAAO,GACd,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,SAAS,OAAO;gBAC3D,SAAS,IAAI,GAAG,UAAU,IAAI;YAChC;YAEA,+BAA+B;YAC/B,IAAI,CAAC,WAAW,CAAC;YAEjB,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEQ,YACN,KAAiB,EACjB,MAAqD,EAC5C;QACT,MAAM,aAAa,OAAO,WAAW,IAAI;QACzC,OACE,aAAa,IAAI,CAAC,WAAW,CAAC,OAAO,IACrC,OAAO,MAAM,EAAE,kBAAkB,SAAS,0BAA0B;QACpE,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,WAAW;IAEjD;IAEQ,YAAY,KAAe,EAAQ;QACzC,iDAAiD;QACjD,IACE,CAAC,MAAM,MAAM,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,iBACvB;YACA,mFAAmF;YACnF;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM,MAAM,KAAK,KAAK;YACxB,uCAAmC;;YAInC;YACA;QACF;QAEA,+CAA+C;QAC/C,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,KAAK;YACvC,IAAI,MAAM,MAAM,IAAI,KAAK;gBACvB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;YAC3B;QACF;IACF;IAEQ,cAAc,MAA0B,EAAU;QACxD,OAAO,GAAG,OAAO,MAAM,EAAE,cAAc,CAAC,EAAE,OAAO,GAAG,EAAE;IACxD;IAEQ,WAAW,GAAW,EAAE,OAAgB,EAAQ;QACtD,IAAI,SAAS;YACX,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;YACL,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5B;IACF;IAEA,uCAAuC;IACvC,MAAM,IAAO,GAAW,EAAE,MAAY,EAAE,WAAW,IAAI,EAAc;QACnE,MAAM,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;QAE7D,oBAAoB;QACpB,IAAI,UAAU;YACZ,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAI;YACjC,IAAI,QAAQ;gBACV,OAAO;YACT;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK;YAAE;QAAO;QACrD,MAAM,OAAO,SAAS,IAAI;QAE1B,gCAAgC;QAChC,IAAI,YAAY,SAAS,MAAM,KAAK,KAAK;YACvC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;QAC3B;QAEA,OAAO;IACT;IAEA,MAAM,KAAQ,GAAW,EAAE,IAAU,EAAc;QACjD,6CAA6C;QAC7C,QAAQ,GAAG,CAAC;QACZ,uCAAsC;;QAEtC;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;QAE7C,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC;QAErB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,IAAO,GAAW,EAAE,IAAU,EAAc;QAChD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK;QAE5C,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC;QAErB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAS,GAAW,EAAE,IAAU,EAAc;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK;QAE9C,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC;QAErB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAU,GAAW,EAAc;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAE1C,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC;QAErB,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,WACJ,GAAW,EACX,IAAiB,EACjB,UAAuC,EAC3B;QACZ,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,UAAU;YACrD,SAAS;gBACP,gBAAgB;YAClB;YACA,kBAAkB,CAAC;gBACjB,IAAI,cAAc,cAAc,KAAK,EAAE;oBACrC,MAAM,WAAW,KAAK,KAAK,CACzB,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBAEpD,WAAW;gBACb;YACF;QACF;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,iBACJ,GAAW,EACX,IAAS,EACT,cAAiB,EACjB,QAAiB,EACL;QACZ,kCAAkC;QAClC,IAAI,UAAU;YACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;QAC3B;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,GAAG,CAAI,KAAK;YAEtC,8BAA8B;YAC9B,IAAI,UAAU;gBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;YAC3B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,oCAAoC;YACpC,IAAI,UAAU;gBACZ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;YACA,MAAM;QACR;IACF;IAEA,mBAAmB;IACX,gBAAgB,GAAW,EAAQ;QACzC,6DAA6D;QAC7D,MAAM,eAAyB,EAAE;QAEjC,wDAAwD;QACxD,MAAM,UAAU,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;QAEjD,KAAK,MAAM,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAK;YACxD,IAAI,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,MAAM;gBAC9C,aAAa,IAAI,CAAC;YACpB;QACF;QAEA,aAAa,OAAO,CAAC,CAAC,MAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAClD;IAEA,2BAA2B;IAC3B,UAAU,GAAW,EAAE,SAAS,KAAK,EAAW;QAC9C,MAAM,MAAM,GAAG,OAAO,WAAW,GAAG,CAAC,EAAE,KAAK;QAC5C,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;IAChC;IAEA,kBAAkB;IAClB,aAAa,KAAa,EAAE;QAC1B,uCAAmC;;QAEnC;IACF;IAEA,kBAAkB;QAChB,uCAAmC;;QAEnC;IACF;IAEA,kBAAkB;IAClB,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,gBAAkD;QAChD,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI;YAC9B,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI;QAC3C;IACF;IAEA,eAAe;IACf,MAAM,cAAgC;QACpC,IAAI;YACF,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,QAAQ,4BAA4B;YAClE,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}]}