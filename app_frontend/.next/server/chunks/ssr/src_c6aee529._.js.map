{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesTrendsHeader.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { RefreshCw, Download, Bell, Settings } from 'lucide-react'\nimport { TrendsHeaderProps } from '@/types/trends'\nimport { formatDistanceToNow } from 'date-fns'\n\nexport const BridgesTrendsHeader: React.FC<TrendsHeaderProps> = ({\n  lastUpdated,\n  isLoading,\n  onRefresh,\n  onExport,\n  onConfigureAlerts,\n}) => {\n  return (\n    <div className=\"bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-6\">\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n        {/* Title Section */}\n        <div className=\"mb-6 lg:mb-0\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"w-1 h-8 bg-[#FF5A5F] rounded-full mr-4\"></div>\n            <h1 className=\"text-3xl font-semibold text-[#484848] tracking-tight\">\n              Bridges Market Trends\n            </h1>\n          </div>\n          <p className=\"text-sm text-[#767676] flex items-center\">\n            <span className=\"inline-block w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"></span>\n            Last updated {formatDistanceToNow(lastUpdated, { addSuffix: true })}\n          </p>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-wrap gap-3\">\n          <button\n            onClick={onRefresh}\n            disabled={isLoading}\n            className=\"flex items-center px-4 py-3 text-sm font-medium text-[#767676] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <RefreshCw \n              className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} \n            />\n            Refresh\n          </button>\n\n          <button\n            onClick={() => onExport('csv')}\n            className=\"flex items-center px-4 py-3 text-sm font-medium text-[#767676] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200\"\n          >\n            <Download className=\"w-4 h-4 mr-2\" />\n            Export\n          </button>\n\n          <button\n            onClick={onConfigureAlerts}\n            className=\"flex items-center px-4 py-3 text-sm font-medium text-[#767676] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200\"\n          >\n            <Bell className=\"w-4 h-4 mr-2\" />\n            Alerts\n          </button>\n\n          <button className=\"flex items-center px-6 py-3 text-sm font-medium text-white bg-[#FF5A5F] hover:bg-[#E04E53] rounded-xl shadow-sm transition-all duration-200 hover:shadow-md\">\n            <Settings className=\"w-4 h-4 mr-2\" />\n            Configure\n          </button>\n        </div>\n      </div>\n\n      {/* Quick Stats Bar */}\n      <div className=\"mt-6 pt-6 border-t border-gray-200\">\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          <div className=\"text-center\">\n            <div className=\"text-lg font-semibold text-[#484848]\">24/7</div>\n            <div className=\"text-xs text-[#767676]\">Market Monitoring</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-lg font-semibold text-[#484848]\">15+</div>\n            <div className=\"text-xs text-[#767676]\">Global Markets</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-lg font-semibold text-[#484848]\">AI</div>\n            <div className=\"text-xs text-[#767676]\">Powered Insights</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-lg font-semibold text-[#484848]\">Real-time</div>\n            <div className=\"text-xs text-[#767676]\">Data Updates</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAEA;AALA;;;;AAOO,MAAM,sBAAmD,CAAC,EAC/D,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,iBAAiB,EAClB;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;;0CAIvE,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;;;;;;oCAA2E;oCAC7E,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;wCAAE,WAAW;oCAAK;;;;;;;;;;;;;kCAKrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCACR,WAAW,CAAC,aAAa,EAAE,YAAY,iBAAiB,IAAI;;;;;;oCAC5D;;;;;;;0CAIJ,8OAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIvC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAuC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAuC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAuC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAuC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpD", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesTrendsFilters.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { <PERSON>, Sparkles } from 'lucide-react'\nimport { TrendsFiltersProps, TimePeriod } from '@/types/trends'\n\nconst TIME_PERIODS: { value: TimePeriod; label: string }[] = [\n  { value: '1D', label: '24h' },\n  { value: '7D', label: '7d' },\n  { value: '30D', label: '30d' },\n  { value: '3M', label: '3m' },\n  { value: '1Y', label: '1y' },\n  { value: 'custom', label: 'Custom' },\n]\n\nexport const BridgesTrendsFilters: React.FC<TrendsFiltersProps> = ({\n  filters,\n  onFiltersChange,\n  availableAssetTypes,\n  availableRegions,\n  presets,\n}) => {\n  const handleAssetTypeToggle = (assetTypeId: string) => {\n    const newAssetTypes = filters.assetTypes.includes(assetTypeId)\n      ? filters.assetTypes.filter(id => id !== assetTypeId)\n      : [...filters.assetTypes, assetTypeId]\n    \n    onFiltersChange({ assetTypes: newAssetTypes })\n  }\n\n  const handleRegionToggle = (regionId: string) => {\n    const newRegions = filters.regions.includes(regionId)\n      ? filters.regions.filter(id => id !== regionId)\n      : [...filters.regions, regionId]\n    \n    onFiltersChange({ regions: newRegions })\n  }\n\n  const handleTimePeriodChange = (timePeriod: TimePeriod) => {\n    onFiltersChange({ timePeriod })\n  }\n\n  const handlePresetApply = (presetFilters: Partial<typeof filters>) => {\n    onFiltersChange(presetFilters)\n  }\n\n  const clearAllFilters = () => {\n    onFiltersChange({\n      assetTypes: [],\n      regions: [],\n      timePeriod: '30D',\n      customDateRange: undefined,\n    })\n  }\n\n  const hasActiveFilters = filters.assetTypes.length > 0 || filters.regions.length > 0\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-medium text-[#484848]\">Market Filters</h3>\n        {hasActiveFilters && (\n          <button\n            onClick={clearAllFilters}\n            className=\"text-sm text-[#767676] hover:text-[#484848] transition-colors duration-200\"\n          >\n            Clear all\n          </button>\n        )}\n      </div>\n\n      {/* Filter Categories */}\n      <div className=\"space-y-6\">\n        {/* Asset Types */}\n        <div>\n          <label className=\"text-sm font-medium text-[#484848] mb-3 block\">\n            Asset Types\n          </label>\n          <div className=\"flex flex-wrap gap-2\">\n            {availableAssetTypes.map((assetType) => {\n              const isActive = filters.assetTypes.includes(assetType.id)\n              return (\n                <button\n                  key={assetType.id}\n                  onClick={() => handleAssetTypeToggle(assetType.id)}\n                  className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                    isActive\n                      ? 'bg-[#FF5A5F] text-white shadow-sm hover:bg-[#E04E53]'\n                      : 'bg-gray-100 text-[#767676] hover:bg-gray-200 hover:text-[#484848]'\n                  }`}\n                >\n                  {assetType.name}\n                  {isActive && (\n                    <X className=\"w-3 h-3 ml-2\" />\n                  )}\n                </button>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Regions */}\n        <div>\n          <label className=\"text-sm font-medium text-[#484848] mb-3 block\">\n            Regions\n          </label>\n          <div className=\"flex flex-wrap gap-2\">\n            {availableRegions.map((region) => {\n              const isActive = filters.regions.includes(region.id)\n              return (\n                <button\n                  key={region.id}\n                  onClick={() => handleRegionToggle(region.id)}\n                  className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                    isActive\n                      ? 'bg-[#FF5A5F] text-white shadow-sm hover:bg-[#E04E53]'\n                      : 'bg-gray-100 text-[#767676] hover:bg-gray-200 hover:text-[#484848]'\n                  }`}\n                >\n                  {region.name}\n                  {isActive && (\n                    <X className=\"w-3 h-3 ml-2\" />\n                  )}\n                </button>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Time Period & Quick Presets */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Time Period */}\n          <div>\n            <label className=\"text-sm font-medium text-[#484848] mb-3 block\">\n              Time Period\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {TIME_PERIODS.map((period) => {\n                const isActive = filters.timePeriod === period.value\n                return (\n                  <button\n                    key={period.value}\n                    onClick={() => handleTimePeriodChange(period.value)}\n                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                      isActive\n                        ? 'bg-[#FF5A5F] text-white shadow-sm'\n                        : 'bg-gray-100 text-[#767676] hover:bg-gray-200 hover:text-[#484848]'\n                    }`}\n                  >\n                    {period.label}\n                  </button>\n                )\n              })}\n            </div>\n          </div>\n\n          {/* Quick Presets */}\n          <div>\n            <label className=\"text-sm font-medium text-[#484848] mb-3 flex items-center\">\n              <Sparkles className=\"w-4 h-4 mr-2 text-[#FF5A5F]\" />\n              Quick Filters\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {presets.map((preset) => (\n                <button\n                  key={preset.id}\n                  onClick={() => handlePresetApply(preset.filters)}\n                  className=\"px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-[#FF5A5F] to-[#FF8E53] text-white hover:from-[#E04E53] hover:to-[#E07B4A] transition-all duration-200 shadow-sm hover:shadow-md\"\n                >\n                  {preset.name}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Active Filters Summary */}\n        {hasActiveFilters && (\n          <div className=\"pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center gap-3\">\n              <span className=\"text-sm font-medium text-[#484848]\">Active:</span>\n              <div className=\"flex flex-wrap gap-2\">\n                {filters.assetTypes.map((assetTypeId) => {\n                  const assetType = availableAssetTypes.find(at => at.id === assetTypeId)\n                  return assetType ? (\n                    <span\n                      key={assetTypeId}\n                      className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#FFB5B7] text-[#484848]\"\n                    >\n                      {assetType.name}\n                    </span>\n                  ) : null\n                })}\n                {filters.regions.map((regionId) => {\n                  const region = availableRegions.find(r => r.id === regionId)\n                  return region ? (\n                    <span\n                      key={regionId}\n                      className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#FFB5B7] text-[#484848]\"\n                    >\n                      {region.name}\n                    </span>\n                  ) : null\n                })}\n                <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#FFB5B7] text-[#484848]\">\n                  {TIME_PERIODS.find(p => p.value === filters.timePeriod)?.label}\n                </span>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAMA,MAAM,eAAuD;IAC3D;QAAE,OAAO;QAAM,OAAO;IAAM;IAC5B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAO,OAAO;IAAM;IAC7B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAU,OAAO;IAAS;CACpC;AAEM,MAAM,uBAAqD,CAAC,EACjE,OAAO,EACP,eAAe,EACf,mBAAmB,EACnB,gBAAgB,EAChB,OAAO,EACR;IACC,MAAM,wBAAwB,CAAC;QAC7B,MAAM,gBAAgB,QAAQ,UAAU,CAAC,QAAQ,CAAC,eAC9C,QAAQ,UAAU,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,eACvC;eAAI,QAAQ,UAAU;YAAE;SAAY;QAExC,gBAAgB;YAAE,YAAY;QAAc;IAC9C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,aAAa,QAAQ,OAAO,CAAC,QAAQ,CAAC,YACxC,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,YACpC;eAAI,QAAQ,OAAO;YAAE;SAAS;QAElC,gBAAgB;YAAE,SAAS;QAAW;IACxC;IAEA,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB;YAAE;QAAW;IAC/B;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;YACd,YAAY,EAAE;YACd,SAAS,EAAE;YACX,YAAY;YACZ,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB,QAAQ,UAAU,CAAC,MAAM,GAAG,KAAK,QAAQ,OAAO,CAAC,MAAM,GAAG;IAEnF,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;oBAClD,kCACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAgD;;;;;;0CAGjE,8OAAC;gCAAI,WAAU;0CACZ,oBAAoB,GAAG,CAAC,CAAC;oCACxB,MAAM,WAAW,QAAQ,UAAU,CAAC,QAAQ,CAAC,UAAU,EAAE;oCACzD,qBACE,8OAAC;wCAEC,SAAS,IAAM,sBAAsB,UAAU,EAAE;wCACjD,WAAW,CAAC,gGAAgG,EAC1G,WACI,yDACA,qEACJ;;4CAED,UAAU,IAAI;4CACd,0BACC,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;uCAVV,UAAU,EAAE;;;;;gCAcvB;;;;;;;;;;;;kCAKJ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAgD;;;;;;0CAGjE,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC;oCACrB,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE;oCACnD,qBACE,8OAAC;wCAEC,SAAS,IAAM,mBAAmB,OAAO,EAAE;wCAC3C,WAAW,CAAC,gGAAgG,EAC1G,WACI,yDACA,qEACJ;;4CAED,OAAO,IAAI;4CACX,0BACC,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;uCAVV,OAAO,EAAE;;;;;gCAcpB;;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAgD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC;4CACjB,MAAM,WAAW,QAAQ,UAAU,KAAK,OAAO,KAAK;4CACpD,qBACE,8OAAC;gDAEC,SAAS,IAAM,uBAAuB,OAAO,KAAK;gDAClD,WAAW,CAAC,uEAAuE,EACjF,WACI,sCACA,qEACJ;0DAED,OAAO,KAAK;+CARR,OAAO,KAAK;;;;;wCAWvB;;;;;;;;;;;;0CAKJ,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAgC;;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;gDAEC,SAAS,IAAM,kBAAkB,OAAO,OAAO;gDAC/C,WAAU;0DAET,OAAO,IAAI;+CAJP,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAYvB,kCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC;4CACvB,MAAM,YAAY,oBAAoB,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;4CAC3D,OAAO,0BACL,8OAAC;gDAEC,WAAU;0DAET,UAAU,IAAI;+CAHV;;;;uDAKL;wCACN;wCACC,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;4CACpB,MAAM,SAAS,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;4CACnD,OAAO,uBACL,8OAAC;gDAEC,WAAU;0DAET,OAAO,IAAI;+CAHP;;;;uDAKL;wCACN;sDACA,8OAAC;4CAAK,WAAU;sDACb,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,UAAU,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3E", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesMetricsGrid.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { TrendingUp, TrendingDown, DollarSign, Clock, BarChart3, Activity } from 'lucide-react'\nimport { TrendsMetricsProps } from '@/types/trends'\n\ninterface MetricCardProps {\n  title: string\n  value: string\n  change: number\n  icon: React.ReactNode\n  loading: boolean\n  description: string\n}\n\nconst MetricCard: React.FC<MetricCardProps> = ({\n  title,\n  value,\n  change,\n  icon,\n  loading,\n  description,\n}) => {\n  const isPositive = change >= 0\n  const TrendIcon = isPositive ? TrendingUp : TrendingDown\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"h-4 bg-gray-200 rounded w-24\"></div>\n            <div className=\"w-10 h-10 bg-gray-200 rounded-xl\"></div>\n          </div>\n          <div className=\"h-8 bg-gray-200 rounded w-20 mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-32 mb-1\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-24\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 cursor-pointer group\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-sm font-medium text-[#767676] group-hover:text-[#484848] transition-colors duration-200\">\n          {title}\n        </h3>\n        <div className=\"w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center group-hover:bg-[#FFB5B7] transition-colors duration-200\">\n          {icon}\n        </div>\n      </div>\n      \n      {/* Main Value */}\n      <div className=\"mb-3\">\n        <p className=\"text-2xl font-semibold text-[#484848] leading-tight\">{value}</p>\n        <p className=\"text-xs text-[#767676] mt-1\">{description}</p>\n      </div>\n      \n      {/* Change Indicator */}\n      <div className=\"flex items-center\">\n        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n          isPositive \n            ? 'bg-green-100 text-green-700' \n            : 'bg-red-100 text-red-700'\n        }`}>\n          <TrendIcon className=\"w-3 h-3 mr-1\" />\n          {isPositive ? '+' : ''}{change.toFixed(1)}%\n        </div>\n        <span className=\"text-xs text-[#B0B0B0] ml-2\">vs previous period</span>\n      </div>\n\n      {/* Trend Sparkline Placeholder */}\n      <div className=\"mt-4 h-6 bg-gradient-to-r from-transparent via-[#FFB5B7] to-transparent opacity-20 rounded-full\"></div>\n    </div>\n  )\n}\n\nconst formatCurrency = (value: number): string => {\n  if (value >= 1000000000) {\n    return `$${(value / 1000000000).toFixed(1)}B`\n  } else if (value >= 1000000) {\n    return `$${(value / 1000000).toFixed(1)}M`\n  } else if (value >= 1000) {\n    return `$${(value / 1000).toFixed(1)}K`\n  }\n  return `$${value.toFixed(0)}`\n}\n\nconst formatDuration = (months: number): string => {\n  if (months >= 12) {\n    const years = months / 12\n    return `${years.toFixed(1)}y`\n  }\n  return `${months.toFixed(0)}m`\n}\n\nexport const BridgesMetricsGrid: React.FC<TrendsMetricsProps> = ({\n  metrics,\n  loading,\n  timeframe,\n}) => {\n  const metricCards = [\n    {\n      title: 'Trading Volume',\n      value: formatCurrency(metrics.totalVolume),\n      change: metrics.volumeChange,\n      icon: <BarChart3 className=\"w-5 h-5 text-[#767676]\" />,\n      description: 'Total token transactions',\n    },\n    {\n      title: 'Average Price Index',\n      value: formatCurrency(metrics.avgPrice),\n      change: metrics.priceChange,\n      icon: <DollarSign className=\"w-5 h-5 text-[#767676]\" />,\n      description: 'Weighted average across assets',\n    },\n    {\n      title: 'Holding Duration',\n      value: formatDuration(metrics.avgHoldingDuration),\n      change: metrics.durationChange,\n      icon: <Clock className=\"w-5 h-5 text-[#767676]\" />,\n      description: 'Average investment period',\n    },\n  ]\n\n  return (\n    <div className=\"mb-8\">\n      {/* Section Header */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center mb-2\">\n          <Activity className=\"w-5 h-5 text-[#FF5A5F] mr-3\" />\n          <h2 className=\"text-xl font-semibold text-[#484848]\">Key Performance Metrics</h2>\n        </div>\n        <p className=\"text-sm text-[#767676]\">\n          Real-time market performance indicators for the {timeframe.toLowerCase()} period\n        </p>\n      </div>\n      \n      {/* Metrics Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        {metricCards.map((metric, index) => (\n          <MetricCard\n            key={index}\n            title={metric.title}\n            value={metric.value}\n            change={metric.change}\n            icon={metric.icon}\n            loading={loading}\n            description={metric.description}\n          />\n        ))}\n      </div>\n\n      {/* Additional Insights Bar */}\n      <div className=\"mt-6 bg-gradient-to-r from-[#FF5A5F] to-[#FF8E53] rounded-xl p-4\">\n        <div className=\"flex items-center justify-between text-white\">\n          <div className=\"flex items-center\">\n            <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3\">\n              <TrendingUp className=\"w-4 h-4\" />\n            </div>\n            <div>\n              <div className=\"text-sm font-medium\">Market Sentiment</div>\n              <div className=\"text-xs opacity-90\">Overall market confidence is strong</div>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-lg font-semibold\">Bullish</div>\n            <div className=\"text-xs opacity-90\">75% confidence</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAeA,MAAM,aAAwC,CAAC,EAC7C,KAAK,EACL,KAAK,EACL,MAAM,EACN,IAAI,EACJ,OAAO,EACP,WAAW,EACZ;IACC,MAAM,aAAa,UAAU;IAC7B,MAAM,YAAY,aAAa,kNAAA,CAAA,aAAU,GAAG,sNAAA,CAAA,eAAY;IAExD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAuD;;;;;;kCACpE,8OAAC;wBAAE,WAAU;kCAA+B;;;;;;;;;;;;0BAI9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,oEAAoE,EACnF,aACI,gCACA,2BACJ;;0CACA,8OAAC;gCAAU,WAAU;;;;;;4BACpB,aAAa,MAAM;4BAAI,OAAO,OAAO,CAAC;4BAAG;;;;;;;kCAE5C,8OAAC;wBAAK,WAAU;kCAA8B;;;;;;;;;;;;0BAIhD,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;AAEA,MAAM,iBAAiB,CAAC;IACtB,IAAI,SAAS,YAAY;QACvB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC/C,OAAO,IAAI,SAAS,SAAS;QAC3B,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5C,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACzC;IACA,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;AAC/B;AAEA,MAAM,iBAAiB,CAAC;IACtB,IAAI,UAAU,IAAI;QAChB,MAAM,QAAQ,SAAS;QACvB,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;IAC/B;IACA,OAAO,GAAG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAChC;AAEO,MAAM,qBAAmD,CAAC,EAC/D,OAAO,EACP,OAAO,EACP,SAAS,EACV;IACC,MAAM,cAAc;QAClB;YACE,OAAO;YACP,OAAO,eAAe,QAAQ,WAAW;YACzC,QAAQ,QAAQ,YAAY;YAC5B,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,eAAe,QAAQ,QAAQ;YACtC,QAAQ,QAAQ,WAAW;YAC3B,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,eAAe,QAAQ,kBAAkB;YAChD,QAAQ,QAAQ,cAAc;YAC9B,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;kCAEvD,8OAAC;wBAAE,WAAU;;4BAAyB;4BACa,UAAU,WAAW;4BAAG;;;;;;;;;;;;;0BAK7E,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;wBAEC,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,QAAQ,OAAO,MAAM;wBACrB,MAAM,OAAO,IAAI;wBACjB,SAAS;wBACT,aAAa,OAAO,WAAW;uBAN1B;;;;;;;;;;0BAYX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAsB;;;;;;sDACrC,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;sCAGxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesChartsGrid.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { TrendsChartsProps } from '@/types/trends'\nimport { Line<PERSON>hart, BarChart3, TrendingUp, Zap } from 'lucide-react'\n\nexport const BridgesChartsGrid: React.FC<TrendsChartsProps> = ({\n  volumeData,\n  priceData,\n  yieldData,\n  onChartInteraction,\n  selectedTimeRange,\n}) => {\n  return (\n    <div className=\"mb-8\">\n      {/* Section Header */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center mb-2\">\n          <LineChart className=\"w-5 h-5 text-[#FF5A5F] mr-3\" />\n          <h2 className=\"text-xl font-semibold text-[#484848]\">Market Visualizations</h2>\n        </div>\n        <p className=\"text-sm text-[#767676]\">\n          Interactive charts showing trading patterns, price movements, and yield analytics\n        </p>\n      </div>\n      \n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6\">\n        {/* Volume Trends Chart */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-medium text-[#484848]\">Trading Volume</h3>\n            <div className=\"flex items-center text-xs text-[#767676]\">\n              <div className=\"w-2 h-2 bg-[#FF5A5F] rounded-full mr-2\"></div>\n              Real-time data\n            </div>\n          </div>\n          <div className=\"h-64 bg-gradient-to-br from-gray-50 to-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-200\">\n            <div className=\"text-center\">\n              <BarChart3 className=\"w-12 h-12 text-[#FF5A5F] mx-auto mb-3\" />\n              <p className=\"text-[#767676] font-medium\">Volume Chart</p>\n              <p className=\"text-xs text-[#B0B0B0] mt-1\">Recharts implementation coming soon</p>\n            </div>\n          </div>\n          {/* Chart Legend */}\n          <div className=\"flex items-center justify-center mt-4 space-x-4\">\n            <div className=\"flex items-center text-xs\">\n              <div className=\"w-3 h-3 bg-[#FF5A5F] rounded mr-2\"></div>\n              <span className=\"text-[#767676]\">Volume</span>\n            </div>\n            <div className=\"flex items-center text-xs\">\n              <div className=\"w-3 h-3 bg-[#00A699] rounded mr-2\"></div>\n              <span className=\"text-[#767676]\">Trend</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Price Index Chart */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-medium text-[#484848]\">Price Index</h3>\n            <div className=\"flex space-x-2\">\n              <button className=\"px-3 py-1 text-xs bg-[#FF5A5F] text-white rounded-full\">All</button>\n              <button className=\"px-3 py-1 text-xs bg-gray-100 text-[#767676] rounded-full hover:bg-gray-200 transition-colors duration-200\">Residential</button>\n              <button className=\"px-3 py-1 text-xs bg-gray-100 text-[#767676] rounded-full hover:bg-gray-200 transition-colors duration-200\">Commercial</button>\n            </div>\n          </div>\n          <div className=\"h-64 bg-gradient-to-br from-gray-50 to-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-200\">\n            <div className=\"text-center\">\n              <TrendingUp className=\"w-12 h-12 text-[#FF5A5F] mx-auto mb-3\" />\n              <p className=\"text-[#767676] font-medium\">Price Trends</p>\n              <p className=\"text-xs text-[#B0B0B0] mt-1\">Multi-asset line chart with filters</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Yield Curve - Full Width */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h3 className=\"text-lg font-medium text-[#484848] mb-1\">Yield Curve Analysis</h3>\n            <p className=\"text-sm text-[#767676]\">Historical vs. forecasted yields with confidence intervals</p>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex items-center text-xs\">\n              <div className=\"w-3 h-3 bg-[#FF5A5F] rounded mr-2\"></div>\n              <span className=\"text-[#767676]\">Historical</span>\n            </div>\n            <div className=\"flex items-center text-xs\">\n              <div className=\"w-3 h-3 bg-[#FF8E53] rounded mr-2\"></div>\n              <span className=\"text-[#767676]\">Forecast</span>\n            </div>\n            <div className=\"flex items-center text-xs\">\n              <div className=\"w-3 h-1 bg-[#FFB5B7] rounded mr-2\"></div>\n              <span className=\"text-[#767676]\">Confidence Band</span>\n            </div>\n          </div>\n        </div>\n        <div className=\"h-80 bg-gradient-to-br from-gray-50 to-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-200\">\n          <div className=\"text-center\">\n            <Zap className=\"w-16 h-16 text-[#FF5A5F] mx-auto mb-4\" />\n            <p className=\"text-[#767676] font-medium text-lg\">Advanced Yield Curve</p>\n            <p className=\"text-sm text-[#B0B0B0] mt-2\">Area chart with confidence intervals and interactive tooltips</p>\n            <div className=\"mt-4 flex items-center justify-center space-x-6 text-xs text-[#767676]\">\n              <span>• Duration: 1M - 30Y</span>\n              <span>• Confidence: 95%</span>\n              <span>• Updated: Real-time</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAJA;;;AAMO,MAAM,oBAAiD,CAAC,EAC7D,UAAU,EACV,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB,iBAAiB,EAClB;IACC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;kCAEvD,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;0BAMxC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAA+C;;;;;;;;;;;;;0CAIlE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;;;;;;0CAI/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;;;;;;;kDAEnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAMvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAAyD;;;;;;0DAC3E,8OAAC;gDAAO,WAAU;0DAA6G;;;;;;0DAC/H,8OAAC;gDAAO,WAAU;0DAA6G;;;;;;;;;;;;;;;;;;0CAGnI,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;0CAExC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;;;;;;;kDAEnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;;;;;;;kDAEnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1569, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesGeoHeatmap.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { MapPin, Globe, Filter } from 'lucide-react'\n\ninterface BridgesGeoHeatmapProps {\n  data: any[]\n  selectedMetric: 'volume' | 'price' | 'sentiment'\n  onRegionSelect: (region: string) => void\n  onMetricChange: (metric: string) => void\n}\n\nexport const BridgesGeoHeatmap: React.FC<BridgesGeoHeatmapProps> = ({\n  data,\n  selectedMetric,\n  onRegionSelect,\n  onMetricChange,\n}) => {\n  const metrics = [\n    { value: 'volume', label: 'Volume', icon: '📊', color: 'bg-[#FF5A5F]' },\n    { value: 'price', label: 'Price Index', icon: '💰', color: 'bg-[#00A699]' },\n    { value: 'sentiment', label: 'Sentiment', icon: '🎯', color: 'bg-[#FC642D]' },\n  ]\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8\">\n      {/* Header */}\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\n        <div className=\"mb-4 lg:mb-0\">\n          <div className=\"flex items-center mb-2\">\n            <Globe className=\"w-5 h-5 text-[#FF5A5F] mr-3\" />\n            <h2 className=\"text-xl font-semibold text-[#484848]\">Geographic Market Heatmap</h2>\n          </div>\n          <p className=\"text-sm text-[#767676]\">\n            Interactive world map showing regional activity and performance data\n          </p>\n        </div>\n        \n        {/* Metric Toggle Buttons */}\n        <div className=\"flex items-center space-x-2\">\n          <Filter className=\"w-4 h-4 text-[#767676] mr-2\" />\n          <div className=\"flex bg-gray-100 rounded-lg p-1\">\n            {metrics.map((metric) => (\n              <button\n                key={metric.value}\n                onClick={() => onMetricChange(metric.value)}\n                className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                  selectedMetric === metric.value\n                    ? 'bg-white text-[#484848] shadow-sm'\n                    : 'text-[#767676] hover:text-[#484848]'\n                }`}\n              >\n                <span className=\"mr-2\">{metric.icon}</span>\n                {metric.label}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n      \n      {/* Map Container */}\n      <div className=\"relative h-96 bg-gradient-to-br from-gray-50 to-white rounded-lg border-2 border-dashed border-gray-200 overflow-hidden\">\n        {/* Map Placeholder */}\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <MapPin className=\"w-16 h-16 text-[#FF5A5F] mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-[#484848] mb-2\">Interactive World Map</h3>\n            <p className=\"text-sm text-[#767676] mb-4 max-w-md\">\n              Real-time geographic visualization showing {selectedMetric} data across global markets\n            </p>\n            <div className=\"flex items-center justify-center space-x-6 text-xs text-[#767676]\">\n              <span>• 15+ Countries</span>\n              <span>• Real-time Updates</span>\n              <span>• Click to Drill Down</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Sample Data Points */}\n        <div className=\"absolute top-1/4 left-1/4 w-3 h-3 bg-[#FF5A5F] rounded-full animate-pulse\"></div>\n        <div className=\"absolute top-1/3 right-1/3 w-4 h-4 bg-[#FF8E53] rounded-full animate-pulse delay-300\"></div>\n        <div className=\"absolute bottom-1/3 left-1/2 w-2 h-2 bg-[#FFB5B7] rounded-full animate-pulse delay-700\"></div>\n        <div className=\"absolute top-1/2 right-1/4 w-5 h-5 bg-[#E04E53] rounded-full animate-pulse delay-1000\"></div>\n      </div>\n      \n      {/* Legend and Stats */}\n      <div className=\"mt-6 flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n        {/* Color Legend */}\n        <div className=\"flex items-center space-x-4 mb-4 lg:mb-0\">\n          <span className=\"text-sm font-medium text-[#484848]\">Intensity:</span>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-xs text-[#767676]\">Low</span>\n            <div className=\"flex space-x-1\">\n              <div className=\"w-4 h-4 rounded bg-[#FFB5B7]\"></div>\n              <div className=\"w-4 h-4 rounded bg-[#FF8E53]\"></div>\n              <div className=\"w-4 h-4 rounded bg-[#FF5A5F]\"></div>\n              <div className=\"w-4 h-4 rounded bg-[#E04E53]\"></div>\n              <div className=\"w-4 h-4 rounded bg-[#C13515]\"></div>\n            </div>\n            <span className=\"text-xs text-[#767676]\">High</span>\n          </div>\n        </div>\n        \n        {/* Summary Stats */}\n        <div className=\"flex items-center space-x-6 text-sm\">\n          <div className=\"flex items-center\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full mr-2\"></div>\n            <span className=\"text-[#767676]\">{data.length} active regions</span>\n          </div>\n          <div className=\"flex items-center\">\n            <div className=\"w-2 h-2 bg-[#FF5A5F] rounded-full mr-2\"></div>\n            <span className=\"text-[#767676]\">Live data</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Regional Highlights */}\n      <div className=\"mt-6 pt-6 border-t border-gray-200\">\n        <h4 className=\"text-sm font-medium text-[#484848] mb-3\">Top Performing Regions</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {['North America', 'Europe', 'Asia Pacific'].map((region, index) => (\n            <div \n              key={region}\n              className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer\"\n              onClick={() => onRegionSelect(region.toLowerCase().replace(' ', '-'))}\n            >\n              <div className=\"flex items-center\">\n                <div className={`w-3 h-3 rounded-full mr-3 ${\n                  index === 0 ? 'bg-[#FF5A5F]' : index === 1 ? 'bg-[#FF8E53]' : 'bg-[#FFB5B7]'\n                }`}></div>\n                <span className=\"text-sm font-medium text-[#484848]\">{region}</span>\n              </div>\n              <span className=\"text-sm text-[#767676]\">\n                {index === 0 ? '+15.2%' : index === 1 ? '+8.7%' : '+5.3%'}\n              </span>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAHA;;;AAYO,MAAM,oBAAsD,CAAC,EAClE,IAAI,EACJ,cAAc,EACd,cAAc,EACd,cAAc,EACf;IACC,MAAM,UAAU;QACd;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM;YAAM,OAAO;QAAe;QACtE;YAAE,OAAO;YAAS,OAAO;YAAe,MAAM;YAAM,OAAO;QAAe;QAC1E;YAAE,OAAO;YAAa,OAAO;YAAa,MAAM;YAAM,OAAO;QAAe;KAC7E;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;;;;;;;0CAEvD,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAMxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAEC,SAAS,IAAM,eAAe,OAAO,KAAK;wCAC1C,WAAW,CAAC,uFAAuF,EACjG,mBAAmB,OAAO,KAAK,GAC3B,sCACA,uCACJ;;0DAEF,8OAAC;gDAAK,WAAU;0DAAQ,OAAO,IAAI;;;;;;4CAClC,OAAO,KAAK;;uCATR,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAiB3B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,8OAAC;oCAAE,WAAU;;wCAAuC;wCACN;wCAAe;;;;;;;8CAE7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAqC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;kDACzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;;4CAAkB,KAAK,MAAM;4CAAC;;;;;;;;;;;;;0CAEhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAiB;4BAAU;yBAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACxD,8OAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,eAAe,OAAO,WAAW,GAAG,OAAO,CAAC,KAAK;;kDAEhE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,0BAA0B,EACzC,UAAU,IAAI,iBAAiB,UAAU,IAAI,iBAAiB,gBAC9D;;;;;;0DACF,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;;kDAExD,8OAAC;wCAAK,WAAU;kDACb,UAAU,IAAI,WAAW,UAAU,IAAI,UAAU;;;;;;;+BAX/C;;;;;;;;;;;;;;;;;;;;;;AAmBnB", "debugId": null}}, {"offset": {"line": 2043, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesAIForecasts.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { RefreshCw, TrendingUp, TrendingDown, Minus, Sparkles, Brain, CheckCircle, Clock } from 'lucide-react'\nimport { TrendsForecastsProps } from '@/types/trends'\n\nconst BridgesSentimentGauge: React.FC<{ \n  value: number; \n  trend: 'up' | 'down' | 'stable';\n  confidence: number;\n}> = ({ value, trend, confidence }) => {\n  const normalizedValue = Math.max(0, Math.min(100, (value + 100) / 2)) // Convert -100,100 to 0,100\n  const rotation = (normalizedValue * 180) / 100 - 90 // Convert to degrees (-90 to 90)\n  \n  const getSentimentColor = (val: number) => {\n    if (val > 20) return '#00A699' // Airbnb green\n    if (val < -20) return '#C13515' // Airbnb red\n    return '#FC642D' // Airbnb orange\n  }\n\n  const getSentimentLabel = (val: number) => {\n    if (val > 40) return 'Very Bullish'\n    if (val > 20) return 'Bullish'\n    if (val > -20) return 'Neutral'\n    if (val > -40) return 'Bearish'\n    return 'Very Bearish'\n  }\n\n  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus\n\n  return (\n    <div className=\"relative w-full max-w-xs mx-auto\">\n      <svg viewBox=\"0 0 200 120\" className=\"w-full h-32\">\n        {/* Background arc */}\n        <path\n          d=\"M 20 100 A 80 80 0 0 1 180 100\"\n          fill=\"none\"\n          stroke=\"#E5E7EB\"\n          strokeWidth=\"8\"\n          strokeLinecap=\"round\"\n        />\n        {/* Colored progress arc */}\n        <path\n          d=\"M 20 100 A 80 80 0 0 1 180 100\"\n          fill=\"none\"\n          stroke={getSentimentColor(value)}\n          strokeWidth=\"8\"\n          strokeLinecap=\"round\"\n          strokeDasharray={`${(normalizedValue * 251.2) / 100} 251.2`}\n        />\n        {/* Needle */}\n        <line\n          x1=\"100\"\n          y1=\"100\"\n          x2={100 + 60 * Math.cos((rotation * Math.PI) / 180)}\n          y2={100 + 60 * Math.sin((rotation * Math.PI) / 180)}\n          stroke=\"#484848\"\n          strokeWidth=\"3\"\n          strokeLinecap=\"round\"\n        />\n        <circle cx=\"100\" cy=\"100\" r=\"6\" fill=\"#484848\" />\n        <circle cx=\"100\" cy=\"100\" r=\"3\" fill=\"white\" />\n      </svg>\n      \n      <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 text-center\">\n        <div className=\"text-2xl font-bold text-[#484848] mb-1\">\n          {value > 0 ? '+' : ''}{value}\n        </div>\n        <div className=\"flex items-center justify-center text-sm text-[#767676] mb-1\">\n          <TrendIcon className=\"w-3 h-3 mr-1\" />\n          <span>{getSentimentLabel(value)}</span>\n        </div>\n        <div className=\"text-xs text-[#B0B0B0]\">\n          {confidence}% confidence\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport const BridgesAIForecasts: React.FC<TrendsForecastsProps> = ({\n  forecasts,\n  commentary,\n  confidenceLevel,\n  onCommentaryRefresh,\n}) => {\n  return (\n    <div className=\"mb-8\">\n      {/* Section Header */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center mb-2\">\n          <Brain className=\"w-5 h-5 text-[#FF5A5F] mr-3\" />\n          <h2 className=\"text-xl font-semibold text-[#484848]\">AI-Powered Forecasts</h2>\n          <div className=\"ml-3 px-2 py-1 bg-gradient-to-r from-[#FF5A5F] to-[#FF8E53] text-white text-xs font-medium rounded-full\">\n            BETA\n          </div>\n        </div>\n        <p className=\"text-sm text-[#767676]\">\n          Machine learning predictions and market sentiment analysis powered by advanced algorithms\n        </p>\n      </div>\n      \n      {/* Forecast Cards Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n        {/* Price Forecast */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-sm font-medium text-[#767676]\">Price Forecast</h3>\n            <div className=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-4 h-4 text-green-600\" />\n            </div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-green-600 mb-2\">+15%</div>\n            <div className=\"text-sm text-[#767676] mb-1\">±3% confidence interval</div>\n            <div className=\"text-xs text-[#B0B0B0] mb-4\">(Next 3 months)</div>\n            <div className=\"bg-green-50 rounded-lg p-3\">\n              <div className=\"text-xs text-green-700 font-medium\">Strong Upward Trend</div>\n              <div className=\"text-xs text-green-600 mt-1\">Based on 15 market factors</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Yield Forecast */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-sm font-medium text-[#767676]\">Yield Forecast</h3>\n            <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-4 h-4 text-blue-600\" />\n            </div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600 mb-2\">6.2%</div>\n            <div className=\"text-sm text-[#767676] mb-1\">±0.5% confidence interval</div>\n            <div className=\"text-xs text-[#B0B0B0] mb-4\">(12-month average)</div>\n            <div className=\"bg-blue-50 rounded-lg p-3\">\n              <div className=\"text-xs text-blue-700 font-medium\">Stable Growth</div>\n              <div className=\"text-xs text-blue-600 mt-1\">Historical pattern analysis</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Sentiment Gauge */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-sm font-medium text-[#767676]\">Market Sentiment</h3>\n            <div className=\"w-8 h-8 bg-[#FFB5B7] rounded-lg flex items-center justify-center\">\n              <Sparkles className=\"w-4 h-4 text-[#FF5A5F]\" />\n            </div>\n          </div>\n          <BridgesSentimentGauge \n            value={forecasts.sentimentIndex.value || 75} \n            trend={forecasts.sentimentIndex.trend || 'up'}\n            confidence={forecasts.sentimentIndex.confidence || 82}\n          />\n        </div>\n      </div>\n\n      {/* AI Commentary Section */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-[#FF5A5F] to-[#FF8E53] rounded-lg flex items-center justify-center mr-3\">\n              <Brain className=\"w-5 h-5 text-white\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-[#484848]\">AI Market Commentary</h3>\n              <p className=\"text-sm text-[#767676]\">Generated insights from multiple data sources</p>\n            </div>\n          </div>\n          <button\n            onClick={onCommentaryRefresh}\n            className=\"flex items-center px-4 py-2 text-sm font-medium text-[#767676] bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200\"\n          >\n            <RefreshCw className=\"w-4 h-4 mr-2\" />\n            Refresh\n          </button>\n        </div>\n        \n        <div className=\"space-y-4\">\n          {/* Commentary Content */}\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <div className=\"prose prose-sm text-[#484848] leading-relaxed\">\n              {commentary.content || (\n                <>\n                  <p className=\"mb-3\">\n                    <strong>Market momentum strengthening in urban areas.</strong> Rising institutional \n                    interest driving volume increases across major metropolitan regions. Short-term outlook \n                    remains positive with continued growth expected through Q2.\n                  </p>\n                  <p className=\"mb-3\">\n                    Key drivers include increased liquidity from institutional investors, favorable \n                    regulatory developments, and strong fundamentals in the residential sector. \n                    Geographic analysis shows particular strength in North American and European markets.\n                  </p>\n                  <p className=\"mb-0\">\n                    Risk factors to monitor include potential policy changes and global economic \n                    indicators that could impact investor sentiment in the coming quarters.\n                  </p>\n                </>\n              )}\n            </div>\n          </div>\n          \n          {/* Metadata and Validation */}\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center space-x-6 mb-4 lg:mb-0\">\n              <div className=\"flex items-center text-sm\">\n                <div className=\"w-4 h-4 bg-[#FF5A5F] rounded-full mr-2\"></div>\n                <span className=\"text-[#767676]\">Confidence: {commentary.confidence || confidenceLevel}%</span>\n              </div>\n              <div className=\"flex items-center text-sm\">\n                <div className=\"w-4 h-4 bg-blue-500 rounded-full mr-2\"></div>\n                <span className=\"text-[#767676]\">Sources: {commentary.sources?.length || 12}</span>\n              </div>\n              <div className={`flex items-center px-3 py-1 rounded-full text-xs font-medium ${\n                commentary.validatedSchema \n                  ? 'bg-green-100 text-green-700' \n                  : 'bg-yellow-100 text-yellow-700'\n              }`}>\n                {commentary.validatedSchema ? (\n                  <CheckCircle className=\"w-3 h-3 mr-1\" />\n                ) : (\n                  <Clock className=\"w-3 h-3 mr-1\" />\n                )}\n                {commentary.validatedSchema ? 'PydanticAI Validated' : 'Validation Pending'}\n              </div>\n            </div>\n            <div className=\"text-xs text-[#B0B0B0]\">\n              Last updated: {commentary.lastUpdated?.toLocaleTimeString() || 'Never'}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAMA,MAAM,wBAID,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE;IAChC,MAAM,kBAAkB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,IAAI,4BAA4B;;IAClG,MAAM,WAAW,AAAC,kBAAkB,MAAO,MAAM,GAAG,iCAAiC;;IAErF,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,IAAI,OAAO,UAAU,eAAe;;QAC9C,IAAI,MAAM,CAAC,IAAI,OAAO,UAAU,aAAa;;QAC7C,OAAO,UAAU,gBAAgB;;IACnC;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,MAAM,IAAI,OAAO;QACrB,IAAI,MAAM,CAAC,IAAI,OAAO;QACtB,IAAI,MAAM,CAAC,IAAI,OAAO;QACtB,OAAO;IACT;IAEA,MAAM,YAAY,UAAU,OAAO,kNAAA,CAAA,aAAU,GAAG,UAAU,SAAS,sNAAA,CAAA,eAAY,GAAG,oMAAA,CAAA,QAAK;IAEvF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,SAAQ;gBAAc,WAAU;;kCAEnC,8OAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAGhB,8OAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAQ,kBAAkB;wBAC1B,aAAY;wBACZ,eAAc;wBACd,iBAAiB,GAAG,AAAC,kBAAkB,QAAS,IAAI,MAAM,CAAC;;;;;;kCAG7D,8OAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,AAAC,WAAW,KAAK,EAAE,GAAI;wBAC/C,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,AAAC,WAAW,KAAK,EAAE,GAAI;wBAC/C,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBAAO,IAAG;wBAAM,IAAG;wBAAM,GAAE;wBAAI,MAAK;;;;;;kCACrC,8OAAC;wBAAO,IAAG;wBAAM,IAAG;wBAAM,GAAE;wBAAI,MAAK;;;;;;;;;;;;0BAGvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,IAAI,MAAM;4BAAI;;;;;;;kCAEzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAU,WAAU;;;;;;0CACrB,8OAAC;0CAAM,kBAAkB;;;;;;;;;;;;kCAE3B,8OAAC;wBAAI,WAAU;;4BACZ;4BAAW;;;;;;;;;;;;;;;;;;;AAKtB;AAEO,MAAM,qBAAqD,CAAC,EACjE,SAAS,EACT,UAAU,EACV,eAAe,EACf,mBAAmB,EACpB;IACC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;0CAA0G;;;;;;;;;;;;kCAI3H,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;0BAMxC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAyC;;;;;;kDACxD,8OAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,8OAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;;kCAMnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,8OAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;kCAMlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,8OAAC;gCACC,OAAO,UAAU,cAAc,CAAC,KAAK,IAAI;gCACzC,OAAO,UAAU,cAAc,CAAC,KAAK,IAAI;gCACzC,YAAY,UAAU,cAAc,CAAC,UAAU,IAAI;;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;0CAG1C,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,WAAW,OAAO,kBACjB;;0DACE,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAsD;;;;;;;0DAIhE,8OAAC;gDAAE,WAAU;0DAAO;;;;;;0DAKpB,8OAAC;gDAAE,WAAU;0DAAO;;;;;;;;;;;;;;;;;;0CAU5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;;4DAAiB;4DAAa,WAAW,UAAU,IAAI;4DAAgB;;;;;;;;;;;;;0DAEzF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;;4DAAiB;4DAAU,WAAW,OAAO,EAAE,UAAU;;;;;;;;;;;;;0DAE3E,8OAAC;gDAAI,WAAW,CAAC,6DAA6D,EAC5E,WAAW,eAAe,GACtB,gCACA,iCACJ;;oDACC,WAAW,eAAe,iBACzB,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAElB,WAAW,eAAe,GAAG,yBAAyB;;;;;;;;;;;;;kDAG3D,8OAAC;wCAAI,WAAU;;4CAAyB;4CACvB,WAAW,WAAW,EAAE,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7E", "debugId": null}}, {"offset": {"line": 2779, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesActionsBar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Download, Bell, Share2, Code, FileText, Database, Webhook } from 'lucide-react'\n\ninterface BridgesActionsBarProps {\n  onExport: (format: 'csv' | 'json' | 'pdf') => void\n  onSetAlert: () => void\n  onShare: () => void\n  onAPIAccess: () => void\n}\n\nexport const BridgesActionsBar: React.FC<BridgesActionsBarProps> = ({\n  onExport,\n  onSetAlert,\n  onShare,\n  onAPIAccess,\n}) => {\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n        {/* Section Info */}\n        <div className=\"mb-6 lg:mb-0\">\n          <h3 className=\"text-lg font-medium text-[#484848] mb-1\">Export & Integration</h3>\n          <p className=\"text-sm text-[#767676]\">\n            Download reports, configure alerts, and integrate with your systems\n          </p>\n        </div>\n        \n        {/* Action Buttons */}\n        <div className=\"flex flex-wrap gap-3\">\n          {/* Alert Setup */}\n          <button\n            onClick={onSetAlert}\n            className=\"flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 hover:shadow-sm\"\n          >\n            <Bell className=\"w-4 h-4 mr-2\" />\n            Set Alert\n          </button>\n          \n          {/* Export Options */}\n          <div className=\"flex items-center\">\n            <button\n              onClick={() => onExport('csv')}\n              className=\"flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 rounded-l-xl transition-all duration-200 border-r border-gray-200\"\n            >\n              <Download className=\"w-4 h-4 mr-2\" />\n              CSV\n            </button>\n            <button\n              onClick={() => onExport('json')}\n              className=\"flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 transition-all duration-200 border-r border-gray-200\"\n            >\n              <Database className=\"w-4 h-4 mr-2\" />\n              JSON\n            </button>\n            <button\n              onClick={() => onExport('pdf')}\n              className=\"flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 rounded-r-xl transition-all duration-200\"\n            >\n              <FileText className=\"w-4 h-4 mr-2\" />\n              PDF\n            </button>\n          </div>\n          \n          {/* Share */}\n          <button\n            onClick={onShare}\n            className=\"flex items-center px-4 py-3 text-sm font-medium text-[#484848] bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 hover:shadow-sm\"\n          >\n            <Share2 className=\"w-4 h-4 mr-2\" />\n            Share\n          </button>\n          \n          {/* API Access */}\n          <button\n            onClick={onAPIAccess}\n            className=\"flex items-center px-6 py-3 text-sm font-medium text-white bg-[#FF5A5F] hover:bg-[#E04E53] rounded-xl shadow-sm transition-all duration-200 hover:shadow-md\"\n          >\n            <Code className=\"w-4 h-4 mr-2\" />\n            API Access\n          </button>\n        </div>\n      </div>\n\n      {/* Quick Integration Options */}\n      <div className=\"mt-6 pt-6 border-t border-gray-200\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {/* Webhook Integration */}\n          <div className=\"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer\">\n            <div className=\"w-10 h-10 bg-[#FFB5B7] rounded-lg flex items-center justify-center mr-3\">\n              <Webhook className=\"w-5 h-5 text-[#FF5A5F]\" />\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-[#484848]\">Webhook Alerts</div>\n              <div className=\"text-xs text-[#767676]\">Real-time notifications</div>\n            </div>\n          </div>\n\n          {/* REST API */}\n          <div className=\"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer\">\n            <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3\">\n              <Code className=\"w-5 h-5 text-blue-600\" />\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-[#484848]\">REST API</div>\n              <div className=\"text-xs text-[#767676]\">Programmatic access</div>\n            </div>\n          </div>\n\n          {/* Scheduled Reports */}\n          <div className=\"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer\">\n            <div className=\"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3\">\n              <FileText className=\"w-5 h-5 text-green-600\" />\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-[#484848]\">Scheduled Reports</div>\n              <div className=\"text-xs text-[#767676]\">Automated delivery</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAYO,MAAM,oBAAsD,CAAC,EAClE,QAAQ,EACR,UAAU,EACV,OAAO,EACP,WAAW,EACZ;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CACxD,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAMxC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAKnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,SAAS;wCACxB,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC;wCACC,SAAS,IAAM,SAAS;wCACxB,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC;wCACC,SAAS,IAAM,SAAS;wCACxB,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAMzC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAKrC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}, {"offset": {"line": 3127, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesTrendsPage.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { BridgesTrendsHeader } from './BridgesTrendsHeader'\nimport { BridgesTrendsFilters } from './BridgesTrendsFilters'\nimport { BridgesMetricsGrid } from './BridgesMetricsGrid'\nimport { BridgesChartsGrid } from './BridgesChartsGrid'\nimport { BridgesGeoHeatmap } from './BridgesGeoHeatmap'\nimport { BridgesAIForecasts } from './BridgesAIForecasts'\nimport { BridgesActionsBar } from './BridgesActionsBar'\nimport { useBridgesTrendsData } from '@/hooks/dashboard/useBridgesTrendsData'\nimport { TrendsFilters as TrendsFiltersType, ChartEvent } from '@/types/trends'\n\nconst defaultFilters: TrendsFiltersType = {\n  assetTypes: ['residential', 'commercial'],\n  regions: ['north-america', 'europe'],\n  timePeriod: '30D',\n}\n\nexport const BridgesTrendsPage: React.FC = () => {\n  const [filters, setFilters] = useState<TrendsFiltersType>(defaultFilters)\n  const [selectedTimeRange, setSelectedTimeRange] = useState<[Date, Date]>([\n    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\n    new Date(),\n  ])\n\n  const {\n    data: trendsData,\n    isLoading,\n    error,\n    refetch,\n  } = useBridgesTrendsData(filters)\n\n  const handleFiltersChange = useCallback((newFilters: Partial<TrendsFiltersType>) => {\n    setFilters(prev => ({ ...prev, ...newFilters }))\n  }, [])\n\n  const handleChartInteraction = useCallback((event: ChartEvent) => {\n    console.log('Chart interaction:', event)\n    // Handle chart interactions (zoom, hover, etc.)\n  }, [])\n\n  const handleExport = useCallback((format: 'csv' | 'json' | 'pdf') => {\n    console.log('Export requested:', format)\n    // Export functionality will be implemented\n  }, [])\n\n  const handleRefresh = useCallback(() => {\n    refetch()\n  }, [refetch])\n\n  const handleConfigureAlerts = useCallback(() => {\n    console.log('Configure alerts requested')\n    // Alert configuration will be implemented\n  }, [])\n\n  const handleRegionClick = useCallback((region: string) => {\n    console.log('Region clicked:', region)\n    // Handle region selection\n  }, [])\n\n  const handleMetricChange = useCallback((metric: string) => {\n    console.log('Metric changed:', metric)\n    // Handle heatmap metric change\n  }, [])\n\n  const handleCommentaryRefresh = useCallback(() => {\n    console.log('Commentary refresh requested')\n    // Refresh AI commentary\n  }, [])\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-[#F7F7F7] flex items-center justify-center\">\n        <div className=\"max-w-md mx-4\">\n          <div className=\"bg-white rounded-2xl shadow-sm border border-red-200 p-8 text-center\">\n            <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <svg className=\"w-8 h-8 text-[#C13515]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            </div>\n            <h2 className=\"text-xl font-semibold text-[#484848] mb-2\">\n              Unable to load market data\n            </h2>\n            <p className=\"text-[#767676] mb-6\">{error.message}</p>\n            <button\n              onClick={handleRefresh}\n              className=\"bg-[#FF5A5F] hover:bg-[#E04E53] text-white font-medium px-6 py-3 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md\"\n            >\n              Try Again\n            </button>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-[#F7F7F7]\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <BridgesTrendsHeader\n          lastUpdated={trendsData?.commentary?.lastUpdated || new Date()}\n          isLoading={isLoading}\n          onRefresh={handleRefresh}\n          onExport={handleExport}\n          onConfigureAlerts={handleConfigureAlerts}\n        />\n\n        {/* Filters */}\n        <BridgesTrendsFilters\n          filters={filters}\n          onFiltersChange={handleFiltersChange}\n          availableAssetTypes={[\n            { id: 'residential', name: 'Residential', category: 'property' },\n            { id: 'commercial', name: 'Commercial', category: 'property' },\n            { id: 'land', name: 'Land', category: 'property' },\n          ]}\n          availableRegions={[\n            { id: 'north-america', name: 'Americas', code: 'NA', continent: 'Americas' },\n            { id: 'europe', name: 'Europe', code: 'EU', continent: 'Europe' },\n            { id: 'asia', name: 'Asia', code: 'AS', continent: 'Asia' },\n          ]}\n          presets={[\n            { id: 'hot-markets', name: 'Hot Markets', filters: { assetTypes: ['residential'], timePeriod: '7D' } },\n            { id: 'institutional', name: 'Institutional', filters: { assetTypes: ['commercial'], timePeriod: '30D' } },\n          ]}\n        />\n\n        {/* Key Metrics */}\n        <BridgesMetricsGrid\n          metrics={trendsData?.metrics || {\n            totalVolume: 0,\n            avgPrice: 0,\n            avgHoldingDuration: 0,\n            volumeChange: 0,\n            priceChange: 0,\n            durationChange: 0,\n          }}\n          loading={isLoading}\n          timeframe={filters.timePeriod}\n        />\n\n        {/* Charts Grid */}\n        <BridgesChartsGrid\n          volumeData={trendsData?.chartData || []}\n          priceData={trendsData?.chartData || []}\n          yieldData={[]}\n          onChartInteraction={handleChartInteraction}\n          selectedTimeRange={selectedTimeRange}\n        />\n\n        {/* Geographic Heatmap */}\n        <BridgesGeoHeatmap\n          data={trendsData?.heatmapData || []}\n          selectedMetric=\"volume\"\n          onRegionSelect={handleRegionClick}\n          onMetricChange={handleMetricChange}\n        />\n\n        {/* AI Forecasts */}\n        <BridgesAIForecasts\n          forecasts={trendsData?.forecasts || {\n            priceForecasts: [],\n            yieldForecasts: [],\n            sentimentIndex: { value: 0, trend: 'stable', confidence: 0, factors: [] },\n          }}\n          commentary={trendsData?.commentary || {\n            content: '',\n            sources: [],\n            confidence: 0,\n            lastUpdated: new Date(),\n            validatedSchema: false,\n          }}\n          confidenceLevel={75}\n          onCommentaryRefresh={handleCommentaryRefresh}\n        />\n\n        {/* Actions Bar */}\n        <BridgesActionsBar\n          onExport={handleExport}\n          onSetAlert={handleConfigureAlerts}\n          onShare={() => console.log('Share requested')}\n          onAPIAccess={() => console.log('API access requested')}\n        />\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AATA;;;;;;;;;;;AAaA,MAAM,iBAAoC;IACxC,YAAY;QAAC;QAAe;KAAa;IACzC,SAAS;QAAC;QAAiB;KAAS;IACpC,YAAY;AACd;AAEO,MAAM,oBAA8B;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC1D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACvE,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;QAC1C,IAAI;KACL;IAED,MAAM,EACJ,MAAM,UAAU,EAChB,SAAS,EACT,KAAK,EACL,OAAO,EACR,GAAG,qBAAqB;IAEzB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,UAAU;YAAC,CAAC;IAChD,GAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,QAAQ,GAAG,CAAC,sBAAsB;IAClC,gDAAgD;IAClD,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,QAAQ,GAAG,CAAC,qBAAqB;IACjC,2CAA2C;IAC7C,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,QAAQ,GAAG,CAAC;IACZ,0CAA0C;IAC5C,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,0BAA0B;IAC5B,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,+BAA+B;IACjC,GAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,QAAQ,GAAG,CAAC;IACZ,wBAAwB;IAC1B,GAAG,EAAE;IAEL,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAyB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAChF,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAuB,MAAM,OAAO;;;;;;sCACjD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gKAAA,CAAA,sBAAmB;oBAClB,aAAa,YAAY,YAAY,eAAe,IAAI;oBACxD,WAAW;oBACX,WAAW;oBACX,UAAU;oBACV,mBAAmB;;;;;;8BAIrB,8OAAC,iKAAA,CAAA,uBAAoB;oBACnB,SAAS;oBACT,iBAAiB;oBACjB,qBAAqB;wBACnB;4BAAE,IAAI;4BAAe,MAAM;4BAAe,UAAU;wBAAW;wBAC/D;4BAAE,IAAI;4BAAc,MAAM;4BAAc,UAAU;wBAAW;wBAC7D;4BAAE,IAAI;4BAAQ,MAAM;4BAAQ,UAAU;wBAAW;qBAClD;oBACD,kBAAkB;wBAChB;4BAAE,IAAI;4BAAiB,MAAM;4BAAY,MAAM;4BAAM,WAAW;wBAAW;wBAC3E;4BAAE,IAAI;4BAAU,MAAM;4BAAU,MAAM;4BAAM,WAAW;wBAAS;wBAChE;4BAAE,IAAI;4BAAQ,MAAM;4BAAQ,MAAM;4BAAM,WAAW;wBAAO;qBAC3D;oBACD,SAAS;wBACP;4BAAE,IAAI;4BAAe,MAAM;4BAAe,SAAS;gCAAE,YAAY;oCAAC;iCAAc;gCAAE,YAAY;4BAAK;wBAAE;wBACrG;4BAAE,IAAI;4BAAiB,MAAM;4BAAiB,SAAS;gCAAE,YAAY;oCAAC;iCAAa;gCAAE,YAAY;4BAAM;wBAAE;qBAC1G;;;;;;8BAIH,8OAAC,+JAAA,CAAA,qBAAkB;oBACjB,SAAS,YAAY,WAAW;wBAC9B,aAAa;wBACb,UAAU;wBACV,oBAAoB;wBACpB,cAAc;wBACd,aAAa;wBACb,gBAAgB;oBAClB;oBACA,SAAS;oBACT,WAAW,QAAQ,UAAU;;;;;;8BAI/B,8OAAC,8JAAA,CAAA,oBAAiB;oBAChB,YAAY,YAAY,aAAa,EAAE;oBACvC,WAAW,YAAY,aAAa,EAAE;oBACtC,WAAW,EAAE;oBACb,oBAAoB;oBACpB,mBAAmB;;;;;;8BAIrB,8OAAC,8JAAA,CAAA,oBAAiB;oBAChB,MAAM,YAAY,eAAe,EAAE;oBACnC,gBAAe;oBACf,gBAAgB;oBAChB,gBAAgB;;;;;;8BAIlB,8OAAC,+JAAA,CAAA,qBAAkB;oBACjB,WAAW,YAAY,aAAa;wBAClC,gBAAgB,EAAE;wBAClB,gBAAgB,EAAE;wBAClB,gBAAgB;4BAAE,OAAO;4BAAG,OAAO;4BAAU,YAAY;4BAAG,SAAS,EAAE;wBAAC;oBAC1E;oBACA,YAAY,YAAY,cAAc;wBACpC,SAAS;wBACT,SAAS,EAAE;wBACX,YAAY;wBACZ,aAAa,IAAI;wBACjB,iBAAiB;oBACnB;oBACA,iBAAiB;oBACjB,qBAAqB;;;;;;8BAIvB,8OAAC,8JAAA,CAAA,oBAAiB;oBAChB,UAAU;oBACV,YAAY;oBACZ,SAAS,IAAM,QAAQ,GAAG,CAAC;oBAC3B,aAAa,IAAM,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 3458, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesTrendsErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n}\n\nexport class BridgesTrendsErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Bridges Trends page error:', error, errorInfo)\n    // Send to monitoring service in production\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"min-h-screen bg-[#F7F7F7] flex items-center justify-center p-4\">\n          <div className=\"max-w-lg w-full\">\n            <div className=\"bg-white rounded-2xl shadow-sm border border-gray-200 p-8 text-center\">\n              {/* Error Icon */}\n              <div className=\"flex justify-center mb-6\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center\">\n                  <AlertTriangle className=\"w-8 h-8 text-red-600\" />\n                </div>\n              </div>\n              \n              {/* Error Message */}\n              <h2 className=\"text-2xl font-semibold text-[#484848] mb-3\">\n                Oops! Something went wrong\n              </h2>\n              \n              <p className=\"text-[#767676] mb-6 leading-relaxed\">\n                {this.state.error?.message || \n                 'We encountered an unexpected error while loading the Bridges Market Trends. Our team has been notified and is working on a fix.'}\n              </p>\n              \n              {/* Action Buttons */}\n              <div className=\"space-y-3\">\n                <button \n                  onClick={() => {\n                    this.setState({ hasError: false })\n                    window.location.reload()\n                  }}\n                  className=\"w-full flex items-center justify-center px-6 py-3 bg-[#FF5A5F] hover:bg-[#E04E53] text-white font-medium rounded-xl shadow-sm transition-all duration-200 hover:shadow-md\"\n                >\n                  <RefreshCw className=\"w-4 h-4 mr-2\" />\n                  Try Again\n                </button>\n                \n                <button \n                  onClick={() => window.location.href = '/dashboard'}\n                  className=\"w-full flex items-center justify-center px-6 py-3 text-[#484848] bg-gray-100 hover:bg-gray-200 font-medium rounded-xl transition-all duration-200\"\n                >\n                  <Home className=\"w-4 h-4 mr-2\" />\n                  Back to Dashboard\n                </button>\n              </div>\n\n              {/* Support Info */}\n              <div className=\"mt-6 pt-6 border-t border-gray-200\">\n                <p className=\"text-sm text-[#B0B0B0] mb-2\">\n                  Need help? Contact our support team\n                </p>\n                <div className=\"flex items-center justify-center space-x-4 text-xs text-[#767676]\">\n                  <span><EMAIL></span>\n                  <span>•</span>\n                  <span>24/7 Support</span>\n                </div>\n              </div>\n              \n              {/* Development Error Details */}\n              {process.env.NODE_ENV === 'development' && this.state.error && (\n                <details className=\"mt-6 text-left\">\n                  <summary className=\"text-sm text-[#767676] cursor-pointer hover:text-[#484848] transition-colors duration-200 flex items-center\">\n                    <Bug className=\"w-4 h-4 mr-2\" />\n                    Error Details (Development)\n                  </summary>\n                  <div className=\"mt-3 p-4 bg-gray-100 rounded-lg\">\n                    <pre className=\"text-xs text-[#484848] overflow-auto whitespace-pre-wrap\">\n                      {this.state.error.stack}\n                    </pre>\n                  </div>\n                </details>\n              )}\n            </div>\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAcO,MAAM,mCAAmC,qMAAA,CAAA,UAAK,CAAC,SAAS;IAC7D,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,8BAA8B,OAAO;IACnD,2CAA2C;IAC7C;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAI3D,8OAAC;gCAAE,WAAU;0CACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAClB;;;;;;0CAIH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;4CACP,IAAI,CAAC,QAAQ,CAAC;gDAAE,UAAU;4CAAM;4CAChC,OAAO,QAAQ,CAAC,MAAM;wCACxB;wCACA,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIxC,8OAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACtC,WAAU;;0DAEV,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAMrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAG3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;4BAKT,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAQ,WAAU;;0DACjB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QASzC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 3698, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/BridgesLoadingSkeleton.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ninterface BridgesLoadingSkeletonProps {\n  type: 'page' | 'metrics' | 'chart' | 'heatmap'\n}\n\nconst MetricsSkeleton: React.FC = () => (\n  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n    {[1, 2, 3].map(i => (\n      <div key={i} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"animate-pulse\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"h-4 bg-gray-200 rounded w-20\"></div>\n            <div className=\"w-10 h-10 bg-gray-200 rounded-xl\"></div>\n          </div>\n          {/* Value */}\n          <div className=\"h-8 bg-gray-200 rounded w-16 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-32 mb-3\"></div>\n          {/* Change indicator */}\n          <div className=\"flex items-center\">\n            <div className=\"h-6 bg-gray-200 rounded-full w-16\"></div>\n            <div className=\"h-3 bg-gray-200 rounded w-24 ml-2\"></div>\n          </div>\n          {/* Sparkline */}\n          <div className=\"mt-4 h-6 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-full\"></div>\n        </div>\n      </div>\n    ))}\n  </div>\n)\n\nconst ChartSkeleton: React.FC = () => (\n  <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n    <div className=\"animate-pulse\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"h-5 bg-gray-200 rounded w-32\"></div>\n        <div className=\"flex space-x-2\">\n          <div className=\"h-6 bg-gray-200 rounded-full w-12\"></div>\n          <div className=\"h-6 bg-gray-200 rounded-full w-16\"></div>\n          <div className=\"h-6 bg-gray-200 rounded-full w-20\"></div>\n        </div>\n      </div>\n      {/* Chart area */}\n      <div className=\"h-64 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4\"></div>\n      {/* Legend */}\n      <div className=\"flex items-center justify-center space-x-4\">\n        <div className=\"flex items-center\">\n          <div className=\"w-3 h-3 bg-gray-200 rounded mr-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-12\"></div>\n        </div>\n        <div className=\"flex items-center\">\n          <div className=\"w-3 h-3 bg-gray-200 rounded mr-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-16\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n)\n\nconst HeatmapSkeleton: React.FC = () => (\n  <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8\">\n    <div className=\"animate-pulse\">\n      {/* Header */}\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\n        <div className=\"mb-4 lg:mb-0\">\n          <div className=\"h-5 bg-gray-200 rounded w-48 mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n        </div>\n        <div className=\"flex space-x-2\">\n          <div className=\"h-8 bg-gray-200 rounded-lg w-20\"></div>\n          <div className=\"h-8 bg-gray-200 rounded-lg w-24\"></div>\n          <div className=\"h-8 bg-gray-200 rounded-lg w-20\"></div>\n        </div>\n      </div>\n      {/* Map area */}\n      <div className=\"h-96 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-6\"></div>\n      {/* Legend and stats */}\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n        <div className=\"flex items-center space-x-4 mb-4 lg:mb-0\">\n          <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\n          <div className=\"flex space-x-1\">\n            {[1,2,3,4,5].map(i => (\n              <div key={i} className=\"w-4 h-4 bg-gray-200 rounded\"></div>\n            ))}\n          </div>\n          <div className=\"h-4 bg-gray-200 rounded w-12\"></div>\n        </div>\n        <div className=\"flex items-center space-x-6\">\n          <div className=\"h-4 bg-gray-200 rounded w-24\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n)\n\nconst PageSkeleton: React.FC = () => (\n  <div className=\"min-h-screen bg-[#F7F7F7]\">\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header Skeleton */}\n      <div className=\"bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n            <div className=\"mb-6 lg:mb-0\">\n              <div className=\"flex items-center mb-3\">\n                <div className=\"w-1 h-8 bg-gray-200 rounded-full mr-4\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-64\"></div>\n              </div>\n              <div className=\"h-4 bg-gray-200 rounded w-48\"></div>\n            </div>\n            <div className=\"flex space-x-3\">\n              {[1,2,3,4].map(i => (\n                <div key={i} className=\"h-10 bg-gray-200 rounded-xl w-24\"></div>\n              ))}\n            </div>\n          </div>\n          {/* Quick stats */}\n          <div className=\"mt-6 pt-6 border-t border-gray-200\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {[1,2,3,4].map(i => (\n                <div key={i} className=\"text-center\">\n                  <div className=\"h-6 bg-gray-200 rounded w-12 mx-auto mb-1\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-20 mx-auto\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters Skeleton */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-5 bg-gray-200 rounded w-32 mb-6\"></div>\n          <div className=\"space-y-6\">\n            <div>\n              <div className=\"h-4 bg-gray-200 rounded w-24 mb-3\"></div>\n              <div className=\"flex flex-wrap gap-2\">\n                {[1,2,3].map(i => (\n                  <div key={i} className=\"h-8 bg-gray-200 rounded-full w-24\"></div>\n                ))}\n              </div>\n            </div>\n            <div>\n              <div className=\"h-4 bg-gray-200 rounded w-20 mb-3\"></div>\n              <div className=\"flex flex-wrap gap-2\">\n                {[1,2,3].map(i => (\n                  <div key={i} className=\"h-8 bg-gray-200 rounded-full w-20\"></div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Metrics Skeleton */}\n      <MetricsSkeleton />\n\n      {/* Charts Skeleton */}\n      <div className=\"mb-8\">\n        <div className=\"animate-pulse mb-6\">\n          <div className=\"h-5 bg-gray-200 rounded w-40 mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-80\"></div>\n        </div>\n        <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6\">\n          <ChartSkeleton />\n          <ChartSkeleton />\n        </div>\n        <ChartSkeleton />\n      </div>\n\n      {/* Heatmap Skeleton */}\n      <HeatmapSkeleton />\n\n      {/* Forecasts Skeleton */}\n      <div className=\"mb-8\">\n        <div className=\"animate-pulse mb-6\">\n          <div className=\"h-5 bg-gray-200 rounded w-48 mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-96\"></div>\n        </div>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          {[1,2,3].map(i => (\n            <div key={i} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"animate-pulse\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"h-4 bg-gray-200 rounded w-20\"></div>\n                  <div className=\"w-8 h-8 bg-gray-200 rounded-lg\"></div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"h-10 bg-gray-200 rounded w-16 mx-auto mb-2\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-32 mx-auto mb-1\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-24 mx-auto\"></div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"animate-pulse\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <div className=\"flex items-center\">\n                <div className=\"w-10 h-10 bg-gray-200 rounded-lg mr-3\"></div>\n                <div>\n                  <div className=\"h-5 bg-gray-200 rounded w-40 mb-1\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-56\"></div>\n                </div>\n              </div>\n              <div className=\"h-8 bg-gray-200 rounded-lg w-20\"></div>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"h-4 bg-gray-200 rounded\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-4/6\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Actions Skeleton */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n            <div className=\"mb-6 lg:mb-0\">\n              <div className=\"h-5 bg-gray-200 rounded w-40 mb-1\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n            </div>\n            <div className=\"flex space-x-3\">\n              {[1,2,3,4,5].map(i => (\n                <div key={i} className=\"h-10 bg-gray-200 rounded-xl w-20\"></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n)\n\nexport const BridgesLoadingSkeleton: React.FC<BridgesLoadingSkeletonProps> = ({ type }) => {\n  switch (type) {\n    case 'metrics':\n      return <MetricsSkeleton />\n    case 'chart':\n      return <ChartSkeleton />\n    case 'heatmap':\n      return <HeatmapSkeleton />\n    case 'page':\n    default:\n      return <PageSkeleton />\n  }\n}"], "names": [], "mappings": ";;;;AAAA;;AAQA,MAAM,kBAA4B,kBAChC,8OAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAA,kBACb,8OAAC;gBAAY,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;;;;;;;;;;;eAhBT;;;;;;;;;;AAuBhB,MAAM,gBAA0B,kBAC9B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;;;;;8BAEf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB,MAAM,kBAA4B,kBAChC,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;;;;;8BAEf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAE;wCAAE;wCAAE;wCAAE;qCAAE,CAAC,GAAG,CAAC,CAAA,kBACf,8OAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB,MAAM,eAAyB,kBAC7B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAE;4CAAE;4CAAE;yCAAE,CAAC,GAAG,CAAC,CAAA,kBACb,8OAAC;gDAAY,WAAU;+CAAb;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAE;wCAAE;wCAAE;qCAAE,CAAC,GAAG,CAAC,CAAA,kBACb,8OAAC;4CAAY,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;2CAFP;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAE;oDAAE;iDAAE,CAAC,GAAG,CAAC,CAAA,kBACX,8OAAC;wDAAY,WAAU;uDAAb;;;;;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAE;oDAAE;iDAAE,CAAC,GAAG,CAAC,CAAA,kBACX,8OAAC;wDAAY,WAAU;uDAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStB,8OAAC;;;;;8BAGD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;;;;8CACD,8OAAC;;;;;;;;;;;sCAEH,8OAAC;;;;;;;;;;;8BAIH,8OAAC;;;;;8BAGD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAE;gCAAE;6BAAE,CAAC,GAAG,CAAC,CAAA,kBACX,8OAAC;oCAAY,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;mCATX;;;;;;;;;;sCAed,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;0DAGnB,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAE;wCAAE;wCAAE;wCAAE;qCAAE,CAAC,GAAG,CAAC,CAAA,kBACf,8OAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnB,MAAM,yBAAgE,CAAC,EAAE,IAAI,EAAE;IACpF,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;QACL;YACE,qBAAO,8OAAC;;;;;IACZ;AACF", "debugId": null}}, {"offset": {"line": 4700, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/index.ts"], "sourcesContent": ["// Bridges Trends Components Barrel Export\n\nexport { BridgesTrendsPage } from './BridgesTrendsPage'\nexport { BridgesTrendsHeader } from './BridgesTrendsHeader'\nexport { BridgesTrendsFilters } from './BridgesTrendsFilters'\nexport { BridgesMetricsGrid } from './BridgesMetricsGrid'\nexport { BridgesChartsGrid } from './BridgesChartsGrid'\nexport { BridgesGeoHeatmap } from './BridgesGeoHeatmap'\nexport { BridgesAIForecasts } from './BridgesAIForecasts'\nexport { BridgesActionsBar } from './BridgesActionsBar'\nexport { BridgesTrendsErrorBoundary } from './BridgesTrendsErrorBoundary'\nexport { BridgesLoadingSkeleton } from './BridgesLoadingSkeleton'"], "names": [], "mappings": "AAAA,0CAA0C;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 4746, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/trends/page.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense } from 'react'\nimport { BridgesTrendsPage } from '@/components/dashboard/trends'\nimport { BridgesTrendsErrorBoundary } from '@/components/dashboard/trends/BridgesTrendsErrorBoundary'\nimport { BridgesLoadingSkeleton } from '@/components/dashboard/trends/BridgesLoadingSkeleton'\n\nexport default function TrendsPageRoute() {\n  return (\n    <BridgesTrendsErrorBoundary>\n      <Suspense fallback={<BridgesLoadingSkeleton type=\"page\" />}>\n        <BridgesTrendsPage />\n      </Suspense>\n    </BridgesTrendsErrorBoundary>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,qBACE,8OAAC,uKAAA,CAAA,6BAA0B;kBACzB,cAAA,8OAAC,qMAAA,CAAA,WAAQ;YAAC,wBAAU,8OAAC,mKAAA,CAAA,yBAAsB;gBAAC,MAAK;;;;;;sBAC/C,cAAA,8OAAC,8JAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;AAI1B", "debugId": null}}]}