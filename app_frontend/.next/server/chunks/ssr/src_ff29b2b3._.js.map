{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/LoadingSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoadingSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/trends/LoadingSkeleton.tsx <module evaluation>\",\n    \"LoadingSkeleton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2EACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/LoadingSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoadingSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/trends/LoadingSkeleton.tsx\",\n    \"LoadingSkeleton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/trends/loading.tsx"], "sourcesContent": ["import { LoadingSkeleton } from '@/components/trends/LoadingSkeleton'\n\nexport default function Loading() {\n  return <LoadingSkeleton type=\"page\" />\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,+IAAA,CAAA,kBAAe;QAAC,MAAK;;;;;;AAC/B", "debugId": null}}]}