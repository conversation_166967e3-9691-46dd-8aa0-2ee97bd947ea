{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/dashboard/trends/LoadingSkeleton.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ninterface LoadingSkeletonProps {\n  type: 'page' | 'metrics' | 'chart' | 'heatmap'\n}\n\nconst MetricsSkeleton: React.FC = () => (\n  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n    {[1, 2, 3].map(i => (\n      <div key={i} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"animate-pulse\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"h-4 bg-gray-200 rounded w-20\"></div>\n            <div className=\"w-10 h-10 bg-gray-200 rounded-xl\"></div>\n          </div>\n          {/* Value */}\n          <div className=\"h-8 bg-gray-200 rounded w-16 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-32 mb-3\"></div>\n          {/* Change indicator */}\n          <div className=\"flex items-center\">\n            <div className=\"h-6 bg-gray-200 rounded-full w-16\"></div>\n            <div className=\"h-3 bg-gray-200 rounded w-24 ml-2\"></div>\n          </div>\n          {/* Sparkline */}\n          <div className=\"mt-4 h-6 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-full\"></div>\n        </div>\n      </div>\n    ))}\n  </div>\n)\n\nconst ChartSkeleton: React.FC = () => (\n  <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n    <div className=\"animate-pulse\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"h-5 bg-gray-200 rounded w-32\"></div>\n        <div className=\"flex space-x-2\">\n          <div className=\"h-6 bg-gray-200 rounded-full w-12\"></div>\n          <div className=\"h-6 bg-gray-200 rounded-full w-16\"></div>\n          <div className=\"h-6 bg-gray-200 rounded-full w-20\"></div>\n        </div>\n      </div>\n      {/* Chart area */}\n      <div className=\"h-64 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4\"></div>\n      {/* Legend */}\n      <div className=\"flex items-center justify-center space-x-4\">\n        <div className=\"flex items-center\">\n          <div className=\"w-3 h-3 bg-gray-200 rounded mr-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-12\"></div>\n        </div>\n        <div className=\"flex items-center\">\n          <div className=\"w-3 h-3 bg-gray-200 rounded mr-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-16\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n)\n\nconst HeatmapSkeleton: React.FC = () => (\n  <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8\">\n    <div className=\"animate-pulse\">\n      {/* Header */}\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\n        <div className=\"mb-4 lg:mb-0\">\n          <div className=\"h-5 bg-gray-200 rounded w-48 mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n        </div>\n        <div className=\"flex space-x-2\">\n          <div className=\"h-8 bg-gray-200 rounded-lg w-20\"></div>\n          <div className=\"h-8 bg-gray-200 rounded-lg w-24\"></div>\n          <div className=\"h-8 bg-gray-200 rounded-lg w-20\"></div>\n        </div>\n      </div>\n      {/* Map area */}\n      <div className=\"h-96 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-6\"></div>\n      {/* Legend and stats */}\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n        <div className=\"flex items-center space-x-4 mb-4 lg:mb-0\">\n          <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\n          <div className=\"flex space-x-1\">\n            {[1,2,3,4,5].map(i => (\n              <div key={i} className=\"w-4 h-4 bg-gray-200 rounded\"></div>\n            ))}\n          </div>\n          <div className=\"h-4 bg-gray-200 rounded w-12\"></div>\n        </div>\n        <div className=\"flex items-center space-x-6\">\n          <div className=\"h-4 bg-gray-200 rounded w-24\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n)\n\nconst PageSkeleton: React.FC = () => (\n  <div className=\"min-h-screen bg-[#F7F7F7]\">\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header Skeleton */}\n      <div className=\"bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n            <div className=\"mb-6 lg:mb-0\">\n              <div className=\"flex items-center mb-3\">\n                <div className=\"w-1 h-8 bg-gray-200 rounded-full mr-4\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-64\"></div>\n              </div>\n              <div className=\"h-4 bg-gray-200 rounded w-48\"></div>\n            </div>\n            <div className=\"flex space-x-3\">\n              {[1,2,3,4].map(i => (\n                <div key={i} className=\"h-10 bg-gray-200 rounded-xl w-24\"></div>\n              ))}\n            </div>\n          </div>\n          {/* Quick stats */}\n          <div className=\"mt-6 pt-6 border-t border-gray-200\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {[1,2,3,4].map(i => (\n                <div key={i} className=\"text-center\">\n                  <div className=\"h-6 bg-gray-200 rounded w-12 mx-auto mb-1\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-20 mx-auto\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters Skeleton */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-5 bg-gray-200 rounded w-32 mb-6\"></div>\n          <div className=\"space-y-6\">\n            <div>\n              <div className=\"h-4 bg-gray-200 rounded w-24 mb-3\"></div>\n              <div className=\"flex flex-wrap gap-2\">\n                {[1,2,3].map(i => (\n                  <div key={i} className=\"h-8 bg-gray-200 rounded-full w-24\"></div>\n                ))}\n              </div>\n            </div>\n            <div>\n              <div className=\"h-4 bg-gray-200 rounded w-20 mb-3\"></div>\n              <div className=\"flex flex-wrap gap-2\">\n                {[1,2,3].map(i => (\n                  <div key={i} className=\"h-8 bg-gray-200 rounded-full w-20\"></div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Metrics Skeleton */}\n      <MetricsSkeleton />\n\n      {/* Charts Skeleton */}\n      <div className=\"mb-8\">\n        <div className=\"animate-pulse mb-6\">\n          <div className=\"h-5 bg-gray-200 rounded w-40 mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-80\"></div>\n        </div>\n        <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6\">\n          <ChartSkeleton />\n          <ChartSkeleton />\n        </div>\n        <ChartSkeleton />\n      </div>\n\n      {/* Heatmap Skeleton */}\n      <HeatmapSkeleton />\n\n      {/* Forecasts Skeleton */}\n      <div className=\"mb-8\">\n        <div className=\"animate-pulse mb-6\">\n          <div className=\"h-5 bg-gray-200 rounded w-48 mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-96\"></div>\n        </div>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          {[1,2,3].map(i => (\n            <div key={i} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"animate-pulse\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"h-4 bg-gray-200 rounded w-20\"></div>\n                  <div className=\"w-8 h-8 bg-gray-200 rounded-lg\"></div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"h-10 bg-gray-200 rounded w-16 mx-auto mb-2\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-32 mx-auto mb-1\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-24 mx-auto\"></div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"animate-pulse\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <div className=\"flex items-center\">\n                <div className=\"w-10 h-10 bg-gray-200 rounded-lg mr-3\"></div>\n                <div>\n                  <div className=\"h-5 bg-gray-200 rounded w-40 mb-1\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-56\"></div>\n                </div>\n              </div>\n              <div className=\"h-8 bg-gray-200 rounded-lg w-20\"></div>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"h-4 bg-gray-200 rounded\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-4/6\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Actions Skeleton */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n            <div className=\"mb-6 lg:mb-0\">\n              <div className=\"h-5 bg-gray-200 rounded w-40 mb-1\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n            </div>\n            <div className=\"flex space-x-3\">\n              {[1,2,3,4,5].map(i => (\n                <div key={i} className=\"h-10 bg-gray-200 rounded-xl w-20\"></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n)\n\nexport const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ type }) => {\n  switch (type) {\n    case 'metrics':\n      return <MetricsSkeleton />\n    case 'chart':\n      return <ChartSkeleton />\n    case 'heatmap':\n      return <HeatmapSkeleton />\n    case 'page':\n    default:\n      return <PageSkeleton />\n  }\n}"], "names": [], "mappings": ";;;;AAAA;;AAQA,MAAM,kBAA4B,kBAChC,8OAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAA,kBACb,8OAAC;gBAAY,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;;;;;;;;;;;eAhBT;;;;;;;;;;AAuBhB,MAAM,gBAA0B,kBAC9B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;;;;;8BAEf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB,MAAM,kBAA4B,kBAChC,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;;;;;8BAEf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAE;wCAAE;wCAAE;wCAAE;qCAAE,CAAC,GAAG,CAAC,CAAA,kBACf,8OAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB,MAAM,eAAyB,kBAC7B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAE;4CAAE;4CAAE;yCAAE,CAAC,GAAG,CAAC,CAAA,kBACb,8OAAC;gDAAY,WAAU;+CAAb;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAE;wCAAE;wCAAE;qCAAE,CAAC,GAAG,CAAC,CAAA,kBACb,8OAAC;4CAAY,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;2CAFP;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAE;oDAAE;iDAAE,CAAC,GAAG,CAAC,CAAA,kBACX,8OAAC;wDAAY,WAAU;uDAAb;;;;;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAE;oDAAE;iDAAE,CAAC,GAAG,CAAC,CAAA,kBACX,8OAAC;wDAAY,WAAU;uDAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStB,8OAAC;;;;;8BAGD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;;;;8CACD,8OAAC;;;;;;;;;;;sCAEH,8OAAC;;;;;;;;;;;8BAIH,8OAAC;;;;;8BAGD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAE;gCAAE;6BAAE,CAAC,GAAG,CAAC,CAAA,kBACX,8OAAC;oCAAY,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;mCATX;;;;;;;;;;sCAed,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;0DAGnB,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAE;wCAAE;wCAAE;wCAAE;qCAAE,CAAC,GAAG,CAAC,CAAA,kBACf,8OAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnB,MAAM,kBAAkD,CAAC,EAAE,IAAI,EAAE;IACtE,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;QACL;YACE,qBAAO,8OAAC;;;;;IACZ;AACF", "debugId": null}}]}