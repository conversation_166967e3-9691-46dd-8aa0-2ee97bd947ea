{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_c3420690._.js", "server/edge/chunks/[root-of-the-server]__2b98656d._.js", "server/edge/chunks/edge-wrapper_10e7b869.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public|auth).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public|auth).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4flSotCLXonGOT7GEGrvbIxW1I7zAQcbWCuoezE387s=", "__NEXT_PREVIEW_MODE_ID": "51c89e2638fd0897318a86515f6b85b4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f81815fcd056b46511d26f4b04ddfb01b9e9e2b44ec7e4a1f88e329d445aaaff", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b65b157a6b8d3efe112a18461476732a6650f4f65fd8e332ade12ac01c2229a4"}}}, "instrumentation": null, "functions": {}}