{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c3420690._.js", "server/edge/chunks/[root-of-the-server]__2b98656d._.js", "server/edge/chunks/edge-wrapper_10e7b869.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public|auth).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public|auth).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4flSotCLXonGOT7GEGrvbIxW1I7zAQcbWCuoezE387s=", "__NEXT_PREVIEW_MODE_ID": "a74723fbe4013e69f92824985477e42b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ee463aedccd544dbc5bbb66eb03d6220cd592457c657c5677608c6b51db71edf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4aa4c9e18b90bccdc880f09dc29ee1df78a2e270c309c422ea3f0f09cfdedb85"}}}, "sortedMiddleware": ["/"], "functions": {}}