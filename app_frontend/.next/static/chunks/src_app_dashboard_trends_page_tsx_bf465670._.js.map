{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/trends/page.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense } from 'react'\nimport { BridgesTrendsPage } from '@/components/dashboard/trends'\nimport { BridgesTrendsErrorBoundary } from '@/components/dashboard/trends/BridgesTrendsErrorBoundary'\nimport { BridgesLoadingSkeleton } from '@/components/dashboard/trends/BridgesLoadingSkeleton'\n\nexport default function TrendsPageRoute() {\n  return (\n    <BridgesTrendsErrorBoundary>\n      <Suspense fallback={<BridgesLoadingSkeleton type=\"page\" />}>\n        <BridgesTrendsPage />\n      </Suspense>\n    </BridgesTrendsErrorBoundary>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;AAFA;;;;;;AAOe,SAAS;IACtB,qBACE,6LAAC;kBACC,cAAA,6LAAC,6JAAA,CAAA,WAAQ;YAAC,wBAAU,6LAAC;gBAAuB,MAAK;;;;;;sBAC/C,cAAA,6LAAC;;;;;;;;;;;;;;;AAIT;KARwB", "debugId": null}}]}