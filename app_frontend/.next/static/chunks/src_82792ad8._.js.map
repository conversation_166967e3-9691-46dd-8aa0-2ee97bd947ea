{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-white hover:bg-primary-600 active:bg-primary-700 shadow-sm hover:shadow-md\",\n        secondary: \"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300 border border-secondary-300\",\n        outline: \"border border-border bg-background hover:bg-surface-200 hover:border-secondary-400 text-foreground\",\n        ghost: \"hover:bg-surface-200 hover:text-accent text-secondary-700\",\n        link: \"text-primary hover:text-primary-600 underline-offset-4 hover:underline p-0 h-auto\",\n        destructive: \"bg-error text-white hover:bg-red-600 active:bg-red-700 shadow-sm\",\n        success: \"bg-success text-white hover:bg-green-600 active:bg-green-700 shadow-sm\",\n      },\n      size: {\n        sm: \"h-8 px-3 py-1.5 text-xs rounded-md\",\n        default: \"h-10 px-4 py-2 text-sm\",\n        lg: \"h-12 px-6 py-3 text-base rounded-xl\",\n        xl: \"h-14 px-8 py-4 text-lg rounded-xl\",\n        icon: \"h-10 w-10 p-0\",\n        \"icon-sm\": \"h-8 w-8 p-0\",\n        \"icon-lg\": \"h-12 w-12 p-0\",\n      },\n      loading: {\n        true: \"cursor-not-allowed\",\n        false: \"\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      loading: false,\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  loadingText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant, \n    size, \n    loading = false,\n    loadingText,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled,\n    asChild = false, \n    ...props \n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    const isDisabled = disabled || loading\n    \n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, loading, className }))}\n        disabled={isDisabled}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            aria-hidden=\"true\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {!loading && leftIcon && <span className=\"mr-1\">{leftIcon}</span>}\n        {loading ? loadingText || children : children}\n        {!loading && rightIcon && <span className=\"ml-1\">{rightIcon}</span>}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,qYACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAaF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,WAAW,EACX,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,aAAa,YAAY;IAE/B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAS;QAAU;QACjE,UAAU;QACV,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,eAAY;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,WAAW,0BAAY,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD,UAAU,eAAe,WAAW;YACpC,CAAC,WAAW,2BAAa,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsHeader.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { RefreshCw, Download, Bell, Settings } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { TrendsHeaderProps } from '@/types/trends'\nimport { formatDistanceToNow } from 'date-fns'\n\nexport const TrendsHeader: React.FC<TrendsHeaderProps> = ({\n  lastUpdated,\n  isLoading,\n  onRefresh,\n  onExport,\n  onConfigureAlerts,\n}) => {\n  return (\n    <div className=\"bg-white rounded-lg border p-6 mb-6\">\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        {/* Title and Last Updated */}\n        <div className=\"mb-4 sm:mb-0\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            Bridges Market Trends\n          </h1>\n          <p className=\"text-sm text-gray-600\">\n            Last updated {formatDistanceToNow(lastUpdated, { addSuffix: true })}\n          </p>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-wrap gap-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onRefresh}\n            disabled={isLoading}\n            className=\"flex items-center\"\n          >\n            <RefreshCw \n              className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} \n            />\n            Refresh\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onExport('csv')}\n            className=\"flex items-center\"\n          >\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onConfigureAlerts}\n            className=\"flex items-center\"\n          >\n            <Bell className=\"h-4 w-4 mr-2\" />\n            Alerts\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"flex items-center\"\n          >\n            <Settings className=\"h-4 w-4 mr-2\" />\n            Settings\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AAEA;AANA;;;;;AAQO,MAAM,eAA4C,CAAC,EACxD,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,iBAAiB,EAClB;IACC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;;gCAAwB;gCACrB,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;oCAAE,WAAW;gCAAK;;;;;;;;;;;;;8BAKrE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCACR,WAAW,CAAC,aAAa,EAAE,YAAY,iBAAiB,IAAI;;;;;;gCAC5D;;;;;;;sCAIJ,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,SAAS;4BACxB,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;KAnEa", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {\n    variant?: 'default' | 'error' | 'success' | 'warning'\n    size?: 'sm' | 'default' | 'lg'\n  }\n>(({ className, children, variant = 'default', size = 'default', ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      // Base styles\n      \"flex w-full items-center justify-between rounded-lg border bg-background px-4 py-3 text-sm font-medium transition-all duration-200 data-[placeholder]:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      // Variant styles\n      {\n        \"border-border hover:border-secondary-400 focus:border-primary focus:ring-2 focus:ring-primary/20\": variant === 'default',\n        \"border-error bg-red-50 focus:border-error focus:ring-2 focus:ring-error/20\": variant === 'error',\n        \"border-success bg-green-50 focus:border-success focus:ring-2 focus:ring-success/20\": variant === 'success',\n        \"border-warning bg-yellow-50 focus:border-warning focus:ring-2 focus:ring-warning/20\": variant === 'warning',\n      },\n      // Size styles\n      {\n        \"h-8 px-3 py-1.5 text-xs\": size === 'sm',\n        \"h-10 px-4 py-2.5 text-sm\": size === 'default',\n        \"h-12 px-4 py-3 text-base\": size === 'lg',\n      },\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-lg border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-md py-2.5 pl-10 pr-3 text-sm font-medium outline-none transition-colors hover:bg-surface-100 focus:bg-surface-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-primary\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAMnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3E,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,wQACA,iBAAiB;QACjB;YACE,oGAAoG,YAAY;YAChH,8EAA8E,YAAY;YAC1F,sFAAsF,YAAY;YAClG,uFAAuF,YAAY;QACrG,GACA,cAAc;QACd;YACE,2BAA2B,SAAS;YACpC,4BAA4B,SAAS;YACrC,4BAA4B,SAAS;QACvC,GACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yPACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center gap-1 rounded-full border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-surface-100 text-accent hover:bg-surface-200\",\n        secondary: \"border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n        info: \"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200\",\n        primary: \"border-transparent bg-primary-100 text-primary-900 hover:bg-primary-200\",\n        outline: \"border-border text-accent hover:bg-surface-50\",\n        ghost: \"border-transparent hover:bg-surface-100 text-accent\",\n      },\n      size: {\n        sm: \"px-2 py-0.5 text-xs\",\n        default: \"px-2.5 py-1 text-sm\",\n        lg: \"px-3 py-1.5 text-base\",\n      },\n      dot: {\n        true: \"\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      dot: false,\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {\n  /** Show a colored dot indicator */\n  dot?: boolean\n  /** Icon to display before the text */\n  icon?: React.ReactNode\n  /** Make the badge clickable */\n  onClick?: () => void\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, dot, icon, onClick, children, ...props }, ref) => {\n    const isClickable = !!onClick\n    \n    const getDotColor = () => {\n      switch (variant) {\n        case 'success': return 'bg-green-500'\n        case 'warning': return 'bg-yellow-500'\n        case 'error': return 'bg-red-500'\n        case 'info': return 'bg-blue-500'\n        case 'primary': return 'bg-primary'\n        case 'secondary': return 'bg-secondary'\n        default: return 'bg-accent'\n      }\n    }\n\n    if (isClickable) {\n      return (\n        <button\n          ref={ref as any}\n          type=\"button\"\n          className={cn(\n            badgeVariants({ variant, size }),\n            \"cursor-pointer hover:scale-105 active:scale-95\",\n            className\n          )}\n          onClick={onClick}\n          {...(props as React.ButtonHTMLAttributes<HTMLButtonElement>)}\n        >\n          {dot && (\n            <span\n              className={cn(\n                \"h-1.5 w-1.5 rounded-full\",\n                getDotColor()\n              )}\n            />\n          )}\n          \n          {icon && (\n            <span className=\"shrink-0\">\n              {icon}\n            </span>\n          )}\n          \n          {children}\n        </button>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...(props as React.HTMLAttributes<HTMLDivElement>)}\n      >\n        {dot && (\n          <span\n            className={cn(\n              \"h-1.5 w-1.5 rounded-full\",\n              getDotColor()\n            )}\n          />\n        )}\n        \n        {icon && (\n          <span className=\"shrink-0\">\n            {icon}\n          </span>\n        )}\n        \n        {children}\n      </div>\n    )\n  }\n)\nBadge.displayName = \"Badge\"\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,iKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,KAAK;YACH,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,KAAK;IACP;AACF;AAcF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrE,MAAM,cAAc,CAAC,CAAC;IAEtB,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YACC,KAAK;YACL,MAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;gBAAE;gBAAS;YAAK,IAC9B,kDACA;YAEF,SAAS;YACR,GAAI,KAAK;;gBAET,qBACC,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;gBAKL,sBACC,6LAAC;oBAAK,WAAU;8BACb;;;;;;gBAIJ;;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAI,KAAK;;YAET,qBACC,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;YAKL,sBACC,6LAAC;gBAAK,WAAU;0BACb;;;;;;YAIJ;;;;;;;AAGP;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsFilters.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { X, Filter } from 'lucide-react'\nimport { TrendsFiltersProps, TimePeriod } from '@/types/trends'\n\nconst TIME_PERIODS: { value: TimePeriod; label: string }[] = [\n  { value: '1D', label: 'Last 24 hours' },\n  { value: '7D', label: 'Last 7 days' },\n  { value: '30D', label: 'Last 30 days' },\n  { value: '3M', label: 'Last 3 months' },\n  { value: '1Y', label: 'Last year' },\n  { value: 'custom', label: 'Custom range' },\n]\n\nexport const TrendsFilters: React.FC<TrendsFiltersProps> = ({\n  filters,\n  onFiltersChange,\n  availableAssetTypes,\n  availableRegions,\n  presets,\n}) => {\n  const handleAssetTypeToggle = (assetTypeId: string) => {\n    const newAssetTypes = filters.assetTypes.includes(assetTypeId)\n      ? filters.assetTypes.filter(id => id !== assetTypeId)\n      : [...filters.assetTypes, assetTypeId]\n    \n    onFiltersChange({ assetTypes: newAssetTypes })\n  }\n\n  const handleRegionToggle = (regionId: string) => {\n    const newRegions = filters.regions.includes(regionId)\n      ? filters.regions.filter(id => id !== regionId)\n      : [...filters.regions, regionId]\n    \n    onFiltersChange({ regions: newRegions })\n  }\n\n  const handleTimePeriodChange = (timePeriod: TimePeriod) => {\n    onFiltersChange({ timePeriod })\n  }\n\n  const handlePresetApply = (presetFilters: Partial<typeof filters>) => {\n    onFiltersChange(presetFilters)\n  }\n\n  const clearFilters = () => {\n    onFiltersChange({\n      assetTypes: [],\n      regions: [],\n      timePeriod: '30D',\n      customDateRange: undefined,\n    })\n  }\n\n  const hasActiveFilters = filters.assetTypes.length > 0 || filters.regions.length > 0\n\n  return (\n    <div className=\"bg-white rounded-lg border p-4\">\n      <div className=\"flex flex-col space-y-4\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <Filter className=\"h-5 w-5 text-gray-400 mr-2\" />\n            <h3 className=\"text-sm font-medium text-gray-900\">Filters</h3>\n          </div>\n          {hasActiveFilters && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={clearFilters}\n              className=\"text-xs\"\n            >\n              Clear all\n            </Button>\n          )}\n        </div>\n\n        {/* Filter Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          {/* Asset Types */}\n          <div>\n            <label className=\"text-xs font-medium text-gray-700 mb-2 block\">\n              Asset Type\n            </label>\n            <div className=\"flex flex-wrap gap-1\">\n              {availableAssetTypes.map((assetType) => (\n                <Badge\n                  key={assetType.id}\n                  variant={filters.assetTypes.includes(assetType.id) ? \"default\" : \"outline\"}\n                  className=\"cursor-pointer hover:bg-primary/10\"\n                  onClick={() => handleAssetTypeToggle(assetType.id)}\n                >\n                  {assetType.name}\n                  {filters.assetTypes.includes(assetType.id) && (\n                    <X className=\"h-3 w-3 ml-1\" />\n                  )}\n                </Badge>\n              ))}\n            </div>\n          </div>\n\n          {/* Regions */}\n          <div>\n            <label className=\"text-xs font-medium text-gray-700 mb-2 block\">\n              Region\n            </label>\n            <div className=\"flex flex-wrap gap-1\">\n              {availableRegions.map((region) => (\n                <Badge\n                  key={region.id}\n                  variant={filters.regions.includes(region.id) ? \"default\" : \"outline\"}\n                  className=\"cursor-pointer hover:bg-primary/10\"\n                  onClick={() => handleRegionToggle(region.id)}\n                >\n                  {region.name}\n                  {filters.regions.includes(region.id) && (\n                    <X className=\"h-3 w-3 ml-1\" />\n                  )}\n                </Badge>\n              ))}\n            </div>\n          </div>\n\n          {/* Time Period */}\n          <div>\n            <label className=\"text-xs font-medium text-gray-700 mb-2 block\">\n              Time Period\n            </label>\n            <Select\n              value={filters.timePeriod}\n              onValueChange={(value: TimePeriod) => handleTimePeriodChange(value)}\n            >\n              <SelectTrigger className=\"w-full\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {TIME_PERIODS.map((period) => (\n                  <SelectItem key={period.value} value={period.value}>\n                    {period.label}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Presets */}\n          <div>\n            <label className=\"text-xs font-medium text-gray-700 mb-2 block\">\n              Quick Filters\n            </label>\n            <div className=\"flex flex-wrap gap-1\">\n              {presets.map((preset) => (\n                <Button\n                  key={preset.id}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"text-xs h-7\"\n                  onClick={() => handlePresetApply(preset.filters)}\n                >\n                  {preset.name}\n                </Button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Active Filters Summary */}\n        {hasActiveFilters && (\n          <div className=\"flex items-center gap-2 pt-2 border-t\">\n            <span className=\"text-xs text-gray-600\">Active filters:</span>\n            <div className=\"flex flex-wrap gap-1\">\n              {filters.assetTypes.map((assetTypeId) => {\n                const assetType = availableAssetTypes.find(at => at.id === assetTypeId)\n                return assetType ? (\n                  <Badge key={assetTypeId} variant=\"secondary\" className=\"text-xs\">\n                    {assetType.name}\n                  </Badge>\n                ) : null\n              })}\n              {filters.regions.map((regionId) => {\n                const region = availableRegions.find(r => r.id === regionId)\n                return region ? (\n                  <Badge key={regionId} variant=\"secondary\" className=\"text-xs\">\n                    {region.name}\n                  </Badge>\n                ) : null\n              })}\n              <Badge variant=\"secondary\" className=\"text-xs\">\n                {TIME_PERIODS.find(p => p.value === filters.timePeriod)?.label}\n              </Badge>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AANA;;;;;;AASA,MAAM,eAAuD;IAC3D;QAAE,OAAO;QAAM,OAAO;IAAgB;IACtC;QAAE,OAAO;QAAM,OAAO;IAAc;IACpC;QAAE,OAAO;QAAO,OAAO;IAAe;IACtC;QAAE,OAAO;QAAM,OAAO;IAAgB;IACtC;QAAE,OAAO;QAAM,OAAO;IAAY;IAClC;QAAE,OAAO;QAAU,OAAO;IAAe;CAC1C;AAEM,MAAM,gBAA8C,CAAC,EAC1D,OAAO,EACP,eAAe,EACf,mBAAmB,EACnB,gBAAgB,EAChB,OAAO,EACR;IACC,MAAM,wBAAwB,CAAC;QAC7B,MAAM,gBAAgB,QAAQ,UAAU,CAAC,QAAQ,CAAC,eAC9C,QAAQ,UAAU,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,eACvC;eAAI,QAAQ,UAAU;YAAE;SAAY;QAExC,gBAAgB;YAAE,YAAY;QAAc;IAC9C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,aAAa,QAAQ,OAAO,CAAC,QAAQ,CAAC,YACxC,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,YACpC;eAAI,QAAQ,OAAO;YAAE;SAAS;QAElC,gBAAgB;YAAE,SAAS;QAAW;IACxC;IAEA,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB;YAAE;QAAW;IAC/B;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,gBAAgB;YACd,YAAY,EAAE;YACd,SAAS,EAAE;YACX,YAAY;YACZ,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB,QAAQ,UAAU,CAAC,MAAM,GAAG,KAAK,QAAQ,OAAO,CAAC,MAAM,GAAG;IAEnF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;wBAEnD,kCACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,0BACxB,6LAAC,oIAAA,CAAA,QAAK;4CAEJ,SAAS,QAAQ,UAAU,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,YAAY;4CACjE,WAAU;4CACV,SAAS,IAAM,sBAAsB,UAAU,EAAE;;gDAEhD,UAAU,IAAI;gDACd,QAAQ,UAAU,CAAC,QAAQ,CAAC,UAAU,EAAE,mBACvC,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;2CAPV,UAAU,EAAE;;;;;;;;;;;;;;;;sCAezB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,uBACrB,6LAAC,oIAAA,CAAA,QAAK;4CAEJ,SAAS,QAAQ,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,YAAY;4CAC3D,WAAU;4CACV,SAAS,IAAM,mBAAmB,OAAO,EAAE;;gDAE1C,OAAO,IAAI;gDACX,QAAQ,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,mBACjC,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;2CAPV,OAAO,EAAE;;;;;;;;;;;;;;;;sCAetB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,QAAQ,UAAU;oCACzB,eAAe,CAAC,QAAsB,uBAAuB;;sDAE7D,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;sDACX,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,aAAU;oDAAoB,OAAO,OAAO,KAAK;8DAC/C,OAAO,KAAK;mDADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASrC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB,OAAO,OAAO;sDAE9C,OAAO,IAAI;2CANP,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;gBAcvB,kCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;sCACxC,6LAAC;4BAAI,WAAU;;gCACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC;oCACvB,MAAM,YAAY,oBAAoB,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;oCAC3D,OAAO,0BACL,6LAAC,oIAAA,CAAA,QAAK;wCAAmB,SAAQ;wCAAY,WAAU;kDACpD,UAAU,IAAI;uCADL;;;;+CAGV;gCACN;gCACC,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;oCACpB,MAAM,SAAS,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oCACnD,OAAO,uBACL,6LAAC,oIAAA,CAAA,QAAK;wCAAgB,SAAQ;wCAAY,WAAU;kDACjD,OAAO,IAAI;uCADF;;;;+CAGV;gCACN;8CACA,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,UAAU,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzE;KAtLa", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsMetrics.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { TrendingUp, TrendingDown, DollarSign, Clock, BarChart3 } from 'lucide-react'\nimport { TrendsMetricsProps } from '@/types/trends'\n\ninterface MetricCardProps {\n  title: string\n  value: string\n  change: number\n  icon: React.ReactNode\n  loading: boolean\n}\n\nconst MetricCard: React.FC<MetricCardProps> = ({\n  title,\n  value,\n  change,\n  icon,\n  loading,\n}) => {\n  const isPositive = change >= 0\n  const TrendIcon = isPositive ? TrendingUp : TrendingDown\n\n  if (loading) {\n    return (\n      <div className=\"bg-white p-6 rounded-lg border\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-4\"></div>\n          <div className=\"h-8 bg-gray-200 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-1/3\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg border hover:shadow-md transition-shadow\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-sm font-medium text-gray-600\">{title}</h3>\n        <div className=\"p-2 bg-gray-100 rounded-lg\">\n          {icon}\n        </div>\n      </div>\n      \n      <div className=\"mb-2\">\n        <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n      </div>\n      \n      <div className=\"flex items-center\">\n        <TrendIcon \n          className={`h-4 w-4 mr-1 ${\n            isPositive ? 'text-green-600' : 'text-red-600'\n          }`} \n        />\n        <span\n          className={`text-sm font-medium ${\n            isPositive ? 'text-green-600' : 'text-red-600'\n          }`}\n        >\n          {isPositive ? '+' : ''}{change.toFixed(1)}%\n        </span>\n        <span className=\"text-sm text-gray-500 ml-1\">vs previous period</span>\n      </div>\n    </div>\n  )\n}\n\nconst formatCurrency = (value: number): string => {\n  if (value >= 1000000) {\n    return `$${(value / 1000000).toFixed(1)}M`\n  } else if (value >= 1000) {\n    return `$${(value / 1000).toFixed(1)}K`\n  }\n  return `$${value.toFixed(0)}`\n}\n\nconst formatDuration = (months: number): string => {\n  if (months >= 12) {\n    const years = months / 12\n    return `${years.toFixed(1)}y`\n  }\n  return `${months.toFixed(0)}m`\n}\n\nexport const TrendsMetrics: React.FC<TrendsMetricsProps> = ({\n  metrics,\n  loading,\n  timeframe,\n}) => {\n  const metricCards = [\n    {\n      title: 'Trading Volume',\n      value: formatCurrency(metrics.totalVolume),\n      change: metrics.volumeChange,\n      icon: <BarChart3 className=\"h-5 w-5 text-gray-600\" />,\n    },\n    {\n      title: 'Average Price',\n      value: formatCurrency(metrics.avgPrice),\n      change: metrics.priceChange,\n      icon: <DollarSign className=\"h-5 w-5 text-gray-600\" />,\n    },\n    {\n      title: 'Holding Duration',\n      value: formatDuration(metrics.avgHoldingDuration),\n      change: metrics.durationChange,\n      icon: <Clock className=\"h-5 w-5 text-gray-600\" />,\n    },\n  ]\n\n  return (\n    <div>\n      <div className=\"mb-4\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">Key Metrics</h2>\n        <p className=\"text-sm text-gray-600\">\n          Market performance for the {timeframe.toLowerCase()} period\n        </p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        {metricCards.map((metric, index) => (\n          <MetricCard\n            key={index}\n            title={metric.title}\n            value={metric.value}\n            change={metric.change}\n            icon={metric.icon}\n            loading={loading}\n          />\n        ))}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAcA,MAAM,aAAwC,CAAC,EAC7C,KAAK,EACL,KAAK,EACL,MAAM,EACN,IAAI,EACJ,OAAO,EACR;IACC,MAAM,aAAa,UAAU;IAC7B,MAAM,YAAY,aAAa,qNAAA,CAAA,aAAU,GAAG,yNAAA,CAAA,eAAY;IAExD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAIL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAoC;;;;;;;;;;;0BAGnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAW,CAAC,aAAa,EACvB,aAAa,mBAAmB,gBAChC;;;;;;kCAEJ,6LAAC;wBACC,WAAW,CAAC,oBAAoB,EAC9B,aAAa,mBAAmB,gBAChC;;4BAED,aAAa,MAAM;4BAAI,OAAO,OAAO,CAAC;4BAAG;;;;;;;kCAE5C,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAIrD;KApDM;AAsDN,MAAM,iBAAiB,CAAC;IACtB,IAAI,SAAS,SAAS;QACpB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5C,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACzC;IACA,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;AAC/B;AAEA,MAAM,iBAAiB,CAAC;IACtB,IAAI,UAAU,IAAI;QAChB,MAAM,QAAQ,SAAS;QACvB,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;IAC/B;IACA,OAAO,GAAG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAChC;AAEO,MAAM,gBAA8C,CAAC,EAC1D,OAAO,EACP,OAAO,EACP,SAAS,EACV;IACC,MAAM,cAAc;QAClB;YACE,OAAO;YACP,OAAO,eAAe,QAAQ,WAAW;YACzC,QAAQ,QAAQ,YAAY;YAC5B,oBAAM,6LAAC,qNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QACA;YACE,OAAO;YACP,OAAO,eAAe,QAAQ,QAAQ;YACtC,QAAQ,QAAQ,WAAW;YAC3B,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,OAAO;YACP,OAAO,eAAe,QAAQ,kBAAkB;YAChD,QAAQ,QAAQ,cAAc;YAC9B,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;KACD;IAED,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;;4BAAwB;4BACP,UAAU,WAAW;4BAAG;;;;;;;;;;;;;0BAIxD,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC;wBAEC,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,QAAQ,OAAO,MAAM;wBACrB,MAAM,OAAO,IAAI;wBACjB,SAAS;uBALJ;;;;;;;;;;;;;;;;AAWjB;MAjDa", "debugId": null}}, {"offset": {"line": 1342, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsCharts.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { TrendsChartsProps } from '@/types/trends'\n\nexport const TrendsCharts: React.FC<TrendsChartsProps> = ({\n  volumeData,\n  priceData,\n  yieldData,\n  onChartInteraction,\n  selectedTimeRange,\n}) => {\n  return (\n    <div>\n      <div className=\"mb-4\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">Market Charts</h2>\n        <p className=\"text-sm text-gray-600\">\n          Interactive visualizations of trading activity and price trends\n        </p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n        {/* Volume Chart */}\n        <div className=\"bg-white p-6 rounded-lg border\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-4\">Trading Volume</h3>\n          <div className=\"h-64 bg-gray-100 rounded flex items-center justify-center\">\n            <p className=\"text-gray-500\">Volume Chart - Recharts implementation coming soon</p>\n          </div>\n        </div>\n\n        {/* Price Chart */}\n        <div className=\"bg-white p-6 rounded-lg border\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-4\">Price Trends</h3>\n          <div className=\"h-64 bg-gray-100 rounded flex items-center justify-center\">\n            <p className=\"text-gray-500\">Price Chart - Recharts implementation coming soon</p>\n          </div>\n        </div>\n\n        {/* Yield Curve - Full Width */}\n        <div className=\"xl:col-span-2 bg-white p-6 rounded-lg border\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-4\">Yield Curve</h3>\n          <div className=\"h-64 bg-gray-100 rounded flex items-center justify-center\">\n            <p className=\"text-gray-500\">Yield Curve Chart - Recharts implementation coming soon</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;;AAKO,MAAM,eAA4C,CAAC,EACxD,UAAU,EACV,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB,iBAAiB,EAClB;IACC,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAKjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAKjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;KA3Ca", "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsHeatmap.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Button } from '@/components/ui/button'\nimport { TrendsHeatmapProps } from '@/types/trends'\n\nexport const TrendsHeatmap: React.FC<TrendsHeatmapProps> = ({\n  data,\n  metric,\n  onRegionClick,\n  onMetricChange,\n  colorScale,\n}) => {\n  const metrics = [\n    { value: 'volume', label: 'Volume' },\n    { value: 'price', label: 'Price' },\n    { value: 'sentiment', label: 'Sentiment' },\n  ]\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg border\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div>\n          <h2 className=\"text-lg font-semibold text-gray-900\">Geographic Heatmap</h2>\n          <p className=\"text-sm text-gray-600\">\n            Regional market activity and performance data\n          </p>\n        </div>\n        \n        <div className=\"flex gap-2\">\n          {metrics.map((m) => (\n            <Button\n              key={m.value}\n              variant={metric === m.value ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={() => onMetricChange(m.value)}\n            >\n              {m.label}\n            </Button>\n          ))}\n        </div>\n      </div>\n      \n      <div className=\"h-96 bg-gray-100 rounded flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-gray-500 mb-2\">Interactive World Map - Implementation coming soon</p>\n          <p className=\"text-xs text-gray-400\">\n            Will show {metric} data with color-coded regions\n          </p>\n        </div>\n      </div>\n      \n      {/* Legend */}\n      <div className=\"mt-4 flex items-center justify-between\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-xs text-gray-600\">Low</span>\n          <div className=\"flex space-x-1\">\n            <div \n              className=\"w-4 h-4 rounded\" \n              style={{ backgroundColor: colorScale.min }}\n            ></div>\n            <div \n              className=\"w-4 h-4 rounded\" \n              style={{ backgroundColor: colorScale.mid }}\n            ></div>\n            <div \n              className=\"w-4 h-4 rounded\" \n              style={{ backgroundColor: colorScale.max }}\n            ></div>\n          </div>\n          <span className=\"text-xs text-gray-600\">High</span>\n        </div>\n        \n        <div className=\"text-xs text-gray-500\">\n          {data.length} regions with data\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,MAAM,gBAA8C,CAAC,EAC1D,IAAI,EACJ,MAAM,EACN,aAAa,EACb,cAAc,EACd,UAAU,EACX;IACC,MAAM,UAAU;QACd;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAa,OAAO;QAAY;KAC1C;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,kBACZ,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAS,WAAW,EAAE,KAAK,GAAG,YAAY;gCAC1C,MAAK;gCACL,SAAS,IAAM,eAAe,EAAE,KAAK;0CAEpC,EAAE,KAAK;+BALH,EAAE,KAAK;;;;;;;;;;;;;;;;0BAWpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,6LAAC;4BAAE,WAAU;;gCAAwB;gCACxB;gCAAO;;;;;;;;;;;;;;;;;;0BAMxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,WAAW,GAAG;wCAAC;;;;;;kDAE3C,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,WAAW,GAAG;wCAAC;;;;;;kDAE3C,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,WAAW,GAAG;wCAAC;;;;;;;;;;;;0CAG7C,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAG1C,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAKvB;KAzEa", "debugId": null}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsForecasts.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { RefreshCw, TrendingUp, TrendingDown, Minus } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { TrendsForecastsProps } from '@/types/trends'\n\nconst SentimentGauge: React.FC<{ value: number; trend: 'up' | 'down' | 'stable' }> = ({ value, trend }) => {\n  const normalizedValue = (value + 100) / 200 // Convert -100,100 to 0,1\n  const rotation = normalizedValue * 180 - 90 // Convert to degrees\n  \n  const getSentimentColor = (val: number) => {\n    if (val > 20) return '#22c55e' // Green\n    if (val < -20) return '#ef4444' // Red\n    return '#f59e0b' // Yellow\n  }\n\n  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus\n\n  return (\n    <div className=\"relative w-48 h-32 mx-auto\">\n      <svg viewBox=\"0 0 200 120\" className=\"w-full h-full\">\n        {/* Background arc */}\n        <path\n          d=\"M 20 100 A 80 80 0 0 1 180 100\"\n          fill=\"none\"\n          stroke=\"#e5e7eb\"\n          strokeWidth=\"8\"\n          strokeLinecap=\"round\"\n        />\n        {/* Colored progress arc */}\n        <path\n          d=\"M 20 100 A 80 80 0 0 1 180 100\"\n          fill=\"none\"\n          stroke={getSentimentColor(value)}\n          strokeWidth=\"8\"\n          strokeLinecap=\"round\"\n          strokeDasharray={`${normalizedValue * 251.2} 251.2`}\n        />\n        {/* Needle */}\n        <line\n          x1=\"100\"\n          y1=\"100\"\n          x2={100 + 70 * Math.cos((rotation * Math.PI) / 180)}\n          y2={100 + 70 * Math.sin((rotation * Math.PI) / 180)}\n          stroke=\"#374151\"\n          strokeWidth=\"2\"\n        />\n        <circle cx=\"100\" cy=\"100\" r=\"4\" fill=\"#374151\" />\n      </svg>\n      \n      <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 text-center\">\n        <div className=\"text-2xl font-bold\">{value > 0 ? '+' : ''}{value}</div>\n        <div className=\"text-sm text-gray-600 flex items-center justify-center\">\n          <TrendIcon className=\"h-3 w-3 mr-1\" />\n          <span>\n            {value > 20 ? 'Bullish' : value < -20 ? 'Bearish' : 'Neutral'}\n          </span>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport const TrendsForecasts: React.FC<TrendsForecastsProps> = ({\n  forecasts,\n  commentary,\n  confidenceLevel,\n  onCommentaryRefresh,\n}) => {\n  return (\n    <div>\n      <div className=\"mb-4\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">AI Forecasts</h2>\n        <p className=\"text-sm text-gray-600\">\n          Machine learning predictions and market sentiment analysis\n        </p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n        {/* Price Forecast */}\n        <div className=\"bg-white p-6 rounded-lg border text-center\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-4\">Price Forecast</h3>\n          <div className=\"space-y-2\">\n            <div className=\"text-2xl font-bold text-green-600\">+15%</div>\n            <div className=\"text-sm text-gray-600\">±3% confidence</div>\n            <div className=\"text-xs text-gray-500\">(Next 3M)</div>\n          </div>\n        </div>\n\n        {/* Yield Forecast */}\n        <div className=\"bg-white p-6 rounded-lg border text-center\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-4\">Yield Forecast</h3>\n          <div className=\"space-y-2\">\n            <div className=\"text-2xl font-bold text-blue-600\">6.2%</div>\n            <div className=\"text-sm text-gray-600\">±0.5% confidence</div>\n            <div className=\"text-xs text-gray-500\">(12M Average)</div>\n          </div>\n        </div>\n\n        {/* Sentiment Gauge */}\n        <div className=\"bg-white p-6 rounded-lg border\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-4 text-center\">Sentiment Gauge</h3>\n          <SentimentGauge \n            value={forecasts.sentimentIndex.value} \n            trend={forecasts.sentimentIndex.trend}\n          />\n        </div>\n      </div>\n\n      {/* AI Commentary */}\n      <div className=\"bg-white p-6 rounded-lg border\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-sm font-medium text-gray-900\">AI Commentary</h3>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onCommentaryRefresh}\n            className=\"flex items-center\"\n          >\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            Refresh\n          </Button>\n        </div>\n        \n        <div className=\"space-y-4\">\n          <div className=\"prose prose-sm text-gray-700\">\n            {commentary.content || (\n              <p className=\"italic text-gray-500\">\n                Market momentum strengthening in urban areas. Rising institutional interest \n                driving volume increases across major metropolitan regions. Short-term outlook \n                remains positive with continued growth expected through Q2.\n              </p>\n            )}\n          </div>\n          \n          <div className=\"flex items-center justify-between text-xs text-gray-500 pt-4 border-t\">\n            <div className=\"flex items-center space-x-4\">\n              <span>Confidence: {commentary.confidence || confidenceLevel}%</span>\n              <span>Sources: {commentary.sources?.length || 3}</span>\n              <span className={`px-2 py-1 rounded ${\n                commentary.validatedSchema ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'\n              }`}>\n                {commentary.validatedSchema ? 'Validated' : 'Pending Validation'}\n              </span>\n            </div>\n            <span>\n              Last updated: {commentary.lastUpdated?.toLocaleTimeString() || 'Never'}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAOA,MAAM,iBAA+E,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE;IACpG,MAAM,kBAAkB,CAAC,QAAQ,GAAG,IAAI,IAAI,0BAA0B;;IACtE,MAAM,WAAW,kBAAkB,MAAM,GAAG,qBAAqB;;IAEjE,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,IAAI,OAAO,UAAU,QAAQ;;QACvC,IAAI,MAAM,CAAC,IAAI,OAAO,UAAU,MAAM;;QACtC,OAAO,UAAU,SAAS;;IAC5B;IAEA,MAAM,YAAY,UAAU,OAAO,qNAAA,CAAA,aAAU,GAAG,UAAU,SAAS,yNAAA,CAAA,eAAY,GAAG,uMAAA,CAAA,QAAK;IAEvF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,SAAQ;gBAAc,WAAU;;kCAEnC,6LAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAGhB,6LAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAQ,kBAAkB;wBAC1B,aAAY;wBACZ,eAAc;wBACd,iBAAiB,GAAG,kBAAkB,MAAM,MAAM,CAAC;;;;;;kCAGrD,6LAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,AAAC,WAAW,KAAK,EAAE,GAAI;wBAC/C,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,AAAC,WAAW,KAAK,EAAE,GAAI;wBAC/C,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBAAO,IAAG;wBAAM,IAAG;wBAAM,GAAE;wBAAI,MAAK;;;;;;;;;;;;0BAGvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAAsB,QAAQ,IAAI,MAAM;4BAAI;;;;;;;kCAC3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAU,WAAU;;;;;;0CACrB,6LAAC;0CACE,QAAQ,KAAK,YAAY,QAAQ,CAAC,KAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;KAvDM;AAyDC,MAAM,kBAAkD,CAAC,EAC9D,SAAS,EACT,UAAU,EACV,eAAe,EACf,mBAAmB,EACpB;IACC,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,6LAAC;gCACC,OAAO,UAAU,cAAc,CAAC,KAAK;gCACrC,OAAO,UAAU,cAAc,CAAC,KAAK;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAK1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,WAAW,OAAO,kBACjB,6LAAC;oCAAE,WAAU;8CAAuB;;;;;;;;;;;0CAQxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAa,WAAW,UAAU,IAAI;oDAAgB;;;;;;;0DAC5D,6LAAC;;oDAAK;oDAAU,WAAW,OAAO,EAAE,UAAU;;;;;;;0DAC9C,6LAAC;gDAAK,WAAW,CAAC,kBAAkB,EAClC,WAAW,eAAe,GAAG,gCAAgC,iCAC7D;0DACC,WAAW,eAAe,GAAG,cAAc;;;;;;;;;;;;kDAGhD,6LAAC;;4CAAK;4CACW,WAAW,WAAW,EAAE,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7E;MA1Fa", "debugId": null}}, {"offset": {"line": 2160, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsActions.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Download, Bell, Share2, Code } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { TrendsActionsProps } from '@/types/trends'\n\nexport const TrendsActions: React.FC<TrendsActionsProps> = ({\n  onExport,\n  onSetAlert,\n  onShare,\n  onConfigureAPI,\n}) => {\n  return (\n    <div className=\"bg-white p-6 rounded-lg border\">\n      <div className=\"flex flex-col sm:flex-row items-center justify-between\">\n        <div className=\"mb-4 sm:mb-0\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Actions</h3>\n          <p className=\"text-xs text-gray-600\">\n            Export data, configure alerts, and manage integrations\n          </p>\n        </div>\n        \n        <div className=\"flex flex-wrap gap-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onSetAlert}\n            className=\"flex items-center\"\n          >\n            <Bell className=\"h-4 w-4 mr-2\" />\n            Set Alert\n          </Button>\n          \n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onExport('csv')}\n            className=\"flex items-center\"\n          >\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export CSV\n          </Button>\n          \n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onExport('json')}\n            className=\"flex items-center\"\n          >\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export JSON\n          </Button>\n          \n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onExport('pdf')}\n            className=\"flex items-center\"\n          >\n            <Download className=\"h-4 w-4 mr-2\" />\n            PDF Report\n          </Button>\n          \n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onShare}\n            className=\"flex items-center\"\n          >\n            <Share2 className=\"h-4 w-4 mr-2\" />\n            Share\n          </Button>\n          \n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onConfigureAPI}\n            className=\"flex items-center\"\n          >\n            <Code className=\"h-4 w-4 mr-2\" />\n            API\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAOO,MAAM,gBAA8C,CAAC,EAC1D,QAAQ,EACR,UAAU,EACV,OAAO,EACP,cAAc,EACf;IACC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,SAAS;4BACxB,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,SAAS;4BACxB,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,SAAS;4BACxB,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIrC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;KAhFa", "debugId": null}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/hooks/api/use-trends.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { TrendsFilters, TrendsData } from '@/types/trends'\n\ninterface UseTrendsDataResult {\n  data: TrendsData | undefined\n  isLoading: boolean\n  error: Error | null\n  refetch: () => void\n}\n\n// Mock data for development\nconst mockTrendsData: TrendsData = {\n  metrics: {\n    totalVolume: 2400000,\n    avgPrice: 245000,\n    avgHoldingDuration: 18,\n    volumeChange: 12.5,\n    priceChange: -3.2,\n    durationChange: 5.1,\n  },\n  chartData: [\n    { timestamp: '2024-01-01', value: 1200000, volume: 1200000 },\n    { timestamp: '2024-01-02', value: 1350000, volume: 1350000 },\n    { timestamp: '2024-01-03', value: 1100000, volume: 1100000 },\n    { timestamp: '2024-01-04', value: 1800000, volume: 1800000 },\n    { timestamp: '2024-01-05', value: 2400000, volume: 2400000 },\n  ],\n  heatmapData: [\n    {\n      region: 'New York',\n      lat: 40.7128,\n      lng: -74.0060,\n      volume: 500000,\n      avgPrice: 800000,\n      sentiment: 75,\n      count: 25,\n    },\n    {\n      region: 'London',\n      lat: 51.5074,\n      lng: -0.1278,\n      volume: 350000,\n      avgPrice: 650000,\n      sentiment: 60,\n      count: 18,\n    },\n    {\n      region: 'Tokyo',\n      lat: 35.6762,\n      lng: 139.6503,\n      volume: 280000,\n      avgPrice: 550000,\n      sentiment: 45,\n      count: 12,\n    },\n  ],\n  forecasts: {\n    priceForecasts: [\n      {\n        assetType: 'residential',\n        currentPrice: 245000,\n        predictedPrice: 282000,\n        confidenceInterval: [275000, 289000],\n        timeHorizon: '3M',\n      },\n    ],\n    yieldForecasts: [\n      {\n        assetType: 'residential',\n        currentYield: 5.8,\n        predictedYield: 6.2,\n        confidenceInterval: [5.7, 6.7],\n        timeHorizon: '12M',\n      },\n    ],\n    sentimentIndex: {\n      value: 75,\n      trend: 'up',\n      confidence: 82,\n      factors: ['institutional_interest', 'urban_growth', 'policy_support'],\n    },\n  },\n  commentary: {\n    content: 'Market momentum strengthening in urban areas. Rising institutional interest driving volume increases across major metropolitan regions. Short-term outlook remains positive with continued growth expected through Q2.',\n    sources: ['market_data', 'news_analysis', 'social_sentiment'],\n    confidence: 85,\n    lastUpdated: new Date(),\n    validatedSchema: true,\n  },\n  filters: {\n    assetTypes: ['residential', 'commercial'],\n    regions: ['north-america', 'europe'],\n    timePeriod: '30D',\n  },\n}\n\nexport const useTrendsData = (filters: TrendsFilters): UseTrendsDataResult => {\n  const [data, setData] = useState<TrendsData | undefined>(undefined)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<Error | null>(null)\n\n  const fetchData = async () => {\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // In a real implementation, this would make an API call:\n      // const response = await fetch('/api/trends/data', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(filters),\n      // })\n      // const data = await response.json()\n      \n      setData(mockTrendsData)\n    } catch (err) {\n      setError(err as Error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchData()\n  }, [filters.assetTypes, filters.regions, filters.timePeriod])\n\n  const refetch = () => {\n    fetchData()\n  }\n\n  return {\n    data,\n    isLoading,\n    error,\n    refetch,\n  }\n}\n\n// Additional hooks for specific data types\nexport const useTrendsMetrics = (filters: TrendsFilters) => {\n  const { data, isLoading, error, refetch } = useTrendsData(filters)\n  \n  return {\n    data: data?.metrics,\n    isLoading,\n    error,\n    refetch,\n  }\n}\n\nexport const useTrendsChartData = (filters: TrendsFilters) => {\n  const { data, isLoading, error, refetch } = useTrendsData(filters)\n  \n  return {\n    data: data?.chartData,\n    isLoading,\n    error,\n    refetch,\n  }\n}\n\nexport const useAICommentary = (filters: TrendsFilters) => {\n  const { data, isLoading, error, refetch } = useTrendsData(filters)\n  \n  return {\n    data: data?.commentary,\n    isLoading,\n    error,\n    refetch,\n  }\n}"], "names": [], "mappings": ";;;;;;AAEA;;AAFA;;AAYA,4BAA4B;AAC5B,MAAM,iBAA6B;IACjC,SAAS;QACP,aAAa;QACb,UAAU;QACV,oBAAoB;QACpB,cAAc;QACd,aAAa,CAAC;QACd,gBAAgB;IAClB;IACA,WAAW;QACT;YAAE,WAAW;YAAc,OAAO;YAAS,QAAQ;QAAQ;QAC3D;YAAE,WAAW;YAAc,OAAO;YAAS,QAAQ;QAAQ;QAC3D;YAAE,WAAW;YAAc,OAAO;YAAS,QAAQ;QAAQ;QAC3D;YAAE,WAAW;YAAc,OAAO;YAAS,QAAQ;QAAQ;QAC3D;YAAE,WAAW;YAAc,OAAO;YAAS,QAAQ;QAAQ;KAC5D;IACD,aAAa;QACX;YACE,QAAQ;YACR,KAAK;YACL,KAAK,CAAC;YACN,QAAQ;YACR,UAAU;YACV,WAAW;YACX,OAAO;QACT;QACA;YACE,QAAQ;YACR,KAAK;YACL,KAAK,CAAC;YACN,QAAQ;YACR,UAAU;YACV,WAAW;YACX,OAAO;QACT;QACA;YACE,QAAQ;YACR,KAAK;YACL,KAAK;YACL,QAAQ;YACR,UAAU;YACV,WAAW;YACX,OAAO;QACT;KACD;IACD,WAAW;QACT,gBAAgB;YACd;gBACE,WAAW;gBACX,cAAc;gBACd,gBAAgB;gBAChB,oBAAoB;oBAAC;oBAAQ;iBAAO;gBACpC,aAAa;YACf;SACD;QACD,gBAAgB;YACd;gBACE,WAAW;gBACX,cAAc;gBACd,gBAAgB;gBAChB,oBAAoB;oBAAC;oBAAK;iBAAI;gBAC9B,aAAa;YACf;SACD;QACD,gBAAgB;YACd,OAAO;YACP,OAAO;YACP,YAAY;YACZ,SAAS;gBAAC;gBAA0B;gBAAgB;aAAiB;QACvE;IACF;IACA,YAAY;QACV,SAAS;QACT,SAAS;YAAC;YAAe;YAAiB;SAAmB;QAC7D,YAAY;QACZ,aAAa,IAAI;QACjB,iBAAiB;IACnB;IACA,SAAS;QACP,YAAY;YAAC;YAAe;SAAa;QACzC,SAAS;YAAC;YAAiB;SAAS;QACpC,YAAY;IACd;AACF;AAEO,MAAM,gBAAgB,CAAC;;IAC5B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,YAAY;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,yDAAyD;YACzD,qDAAqD;YACrD,oBAAoB;YACpB,qDAAqD;YACrD,mCAAmC;YACnC,KAAK;YACL,qCAAqC;YAErC,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC,QAAQ,UAAU;QAAE,QAAQ,OAAO;QAAE,QAAQ,UAAU;KAAC;IAE5D,MAAM,UAAU;QACd;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;GA3Ca;AA8CN,MAAM,mBAAmB,CAAC;;IAC/B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,cAAc;IAE1D,OAAO;QACL,MAAM,MAAM;QACZ;QACA;QACA;IACF;AACF;IATa;;QACiC;;;AAUvC,MAAM,qBAAqB,CAAC;;IACjC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,cAAc;IAE1D,OAAO;QACL,MAAM,MAAM;QACZ;QACA;QACA;IACF;AACF;IATa;;QACiC;;;AAUvC,MAAM,kBAAkB,CAAC;;IAC9B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,cAAc;IAE1D,OAAO;QACL,MAAM,MAAM;QACZ;QACA;QACA;IACF;AACF;IATa;;QACiC", "debugId": null}}, {"offset": {"line": 2591, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsPage.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { <PERSON>rends<PERSON>eader } from './TrendsHeader'\nimport { TrendsFilters } from './TrendsFilters'\nimport { TrendsMetrics } from './TrendsMetrics'\nimport { TrendsCharts } from './TrendsCharts'\nimport { TrendsHeatmap } from './TrendsHeatmap'\nimport { TrendsForecasts } from './TrendsForecasts'\nimport { TrendsActions } from './TrendsActions'\nimport { useTrendsData } from '@/hooks/api/use-trends'\nimport { TrendsFilters as TrendsFiltersType, ChartEvent } from '@/types/trends'\n\nconst defaultFilters: TrendsFiltersType = {\n  assetTypes: ['residential', 'commercial'],\n  regions: ['north-america', 'europe'],\n  timePeriod: '30D',\n}\n\nexport const TrendsPage: React.FC = () => {\n  const [filters, setFilters] = useState<TrendsFiltersType>(defaultFilters)\n  const [selectedTimeRange, setSelectedTimeRange] = useState<[Date, Date]>([\n    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\n    new Date(),\n  ])\n\n  const {\n    data: trendsData,\n    isLoading,\n    error,\n    refetch,\n  } = useTrendsData(filters)\n\n  const handleFiltersChange = useCallback((newFilters: Partial<TrendsFiltersType>) => {\n    setFilters(prev => ({ ...prev, ...newFilters }))\n  }, [])\n\n  const handleChartInteraction = useCallback((event: ChartEvent) => {\n    console.log('Chart interaction:', event)\n    // Handle chart interactions (zoom, hover, etc.)\n  }, [])\n\n  const handleExport = useCallback((format: 'csv' | 'json' | 'pdf') => {\n    console.log('Export requested:', format)\n    // Export functionality will be implemented\n  }, [])\n\n  const handleRefresh = useCallback(() => {\n    refetch()\n  }, [refetch])\n\n  const handleConfigureAlerts = useCallback(() => {\n    console.log('Configure alerts requested')\n    // Alert configuration will be implemented\n  }, [])\n\n  const handleRegionClick = useCallback((region: string) => {\n    console.log('Region clicked:', region)\n    // Handle region selection\n  }, [])\n\n  const handleMetricChange = useCallback((metric: string) => {\n    console.log('Metric changed:', metric)\n    // Handle heatmap metric change\n  }, [])\n\n  const handleCommentaryRefresh = useCallback(() => {\n    console.log('Commentary refresh requested')\n    // Refresh AI commentary\n  }, [])\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Unable to load trends data\n          </h2>\n          <p className=\"text-gray-600 mb-4\">{error.message}</p>\n          <button\n            onClick={handleRefresh}\n            className=\"px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <TrendsHeader\n          lastUpdated={trendsData?.commentary?.lastUpdated || new Date()}\n          isLoading={isLoading}\n          onRefresh={handleRefresh}\n          onExport={handleExport}\n          onConfigureAlerts={handleConfigureAlerts}\n        />\n\n        {/* Filters */}\n        <div className=\"mb-6\">\n          <TrendsFilters\n            filters={filters}\n            onFiltersChange={handleFiltersChange}\n            availableAssetTypes={[\n              { id: 'residential', name: 'Residential', category: 'property' },\n              { id: 'commercial', name: 'Commercial', category: 'property' },\n              { id: 'land', name: 'Land', category: 'property' },\n            ]}\n            availableRegions={[\n              { id: 'north-america', name: 'North America', code: 'NA', continent: 'Americas' },\n              { id: 'europe', name: 'Europe', code: 'EU', continent: 'Europe' },\n              { id: 'asia', name: 'Asia', code: 'AS', continent: 'Asia' },\n            ]}\n            presets={[\n              { id: 'hot-markets', name: 'Hot Markets', filters: { assetTypes: ['residential'], timePeriod: '7D' } },\n              { id: 'new-listings', name: 'New Listings', filters: { timePeriod: '1D' } },\n            ]}\n          />\n        </div>\n\n        {/* Metrics */}\n        <div className=\"mb-6\">\n          <TrendsMetrics\n            metrics={trendsData?.metrics || {\n              totalVolume: 0,\n              avgPrice: 0,\n              avgHoldingDuration: 0,\n              volumeChange: 0,\n              priceChange: 0,\n              durationChange: 0,\n            }}\n            loading={isLoading}\n            timeframe={filters.timePeriod}\n          />\n        </div>\n\n        {/* Charts */}\n        <div className=\"mb-6\">\n          <TrendsCharts\n            volumeData={trendsData?.chartData || []}\n            priceData={trendsData?.chartData || []}\n            yieldData={[]}\n            onChartInteraction={handleChartInteraction}\n            selectedTimeRange={selectedTimeRange}\n          />\n        </div>\n\n        {/* Heatmap */}\n        <div className=\"mb-6\">\n          <TrendsHeatmap\n            data={trendsData?.heatmapData || []}\n            metric=\"volume\"\n            onRegionClick={handleRegionClick}\n            onMetricChange={handleMetricChange}\n            colorScale={{\n              min: '#f3f4f6',\n              mid: '#ff385c',\n              max: '#991b1b',\n            }}\n          />\n        </div>\n\n        {/* Forecasts */}\n        <div className=\"mb-6\">\n          <TrendsForecasts\n            forecasts={trendsData?.forecasts || {\n              priceForecasts: [],\n              yieldForecasts: [],\n              sentimentIndex: { value: 0, trend: 'stable', confidence: 0, factors: [] },\n            }}\n            commentary={trendsData?.commentary || {\n              content: '',\n              sources: [],\n              confidence: 0,\n              lastUpdated: new Date(),\n              validatedSchema: false,\n            }}\n            confidenceLevel={75}\n            onCommentaryRefresh={handleCommentaryRefresh}\n          />\n        </div>\n\n        {/* Actions */}\n        <TrendsActions\n          onExport={handleExport}\n          onSetAlert={handleConfigureAlerts}\n          onShare={() => console.log('Share requested')}\n          onConfigureAPI={() => console.log('API config requested')}\n        />\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAaA,MAAM,iBAAoC;IACxC,YAAY;QAAC;QAAe;KAAa;IACzC,SAAS;QAAC;QAAiB;KAAS;IACpC,YAAY;AACd;AAEO,MAAM,aAAuB;;IAClC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC1D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACvE,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;QAC1C,IAAI;KACL;IAED,MAAM,EACJ,MAAM,UAAU,EAChB,SAAS,EACT,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD,EAAE;IAElB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACvC;+DAAW,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,GAAG,UAAU;oBAAC,CAAC;;QAChD;sDAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAC1C,QAAQ,GAAG,CAAC,sBAAsB;QAClC,gDAAgD;QAClD;yDAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAChC,QAAQ,GAAG,CAAC,qBAAqB;QACjC,2CAA2C;QAC7C;+CAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAChC;QACF;gDAAG;QAAC;KAAQ;IAEZ,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACxC,QAAQ,GAAG,CAAC;QACZ,0CAA0C;QAC5C;wDAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACrC,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,0BAA0B;QAC5B;oDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACtC,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,+BAA+B;QACjC;qDAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YAC1C,QAAQ,GAAG,CAAC;QACZ,wBAAwB;QAC1B;0DAAG,EAAE;IAEL,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAsB,MAAM,OAAO;;;;;;kCAChD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,+IAAA,CAAA,eAAY;oBACX,aAAa,YAAY,YAAY,eAAe,IAAI;oBACxD,WAAW;oBACX,WAAW;oBACX,UAAU;oBACV,mBAAmB;;;;;;8BAIrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gJAAA,CAAA,gBAAa;wBACZ,SAAS;wBACT,iBAAiB;wBACjB,qBAAqB;4BACnB;gCAAE,IAAI;gCAAe,MAAM;gCAAe,UAAU;4BAAW;4BAC/D;gCAAE,IAAI;gCAAc,MAAM;gCAAc,UAAU;4BAAW;4BAC7D;gCAAE,IAAI;gCAAQ,MAAM;gCAAQ,UAAU;4BAAW;yBAClD;wBACD,kBAAkB;4BAChB;gCAAE,IAAI;gCAAiB,MAAM;gCAAiB,MAAM;gCAAM,WAAW;4BAAW;4BAChF;gCAAE,IAAI;gCAAU,MAAM;gCAAU,MAAM;gCAAM,WAAW;4BAAS;4BAChE;gCAAE,IAAI;gCAAQ,MAAM;gCAAQ,MAAM;gCAAM,WAAW;4BAAO;yBAC3D;wBACD,SAAS;4BACP;gCAAE,IAAI;gCAAe,MAAM;gCAAe,SAAS;oCAAE,YAAY;wCAAC;qCAAc;oCAAE,YAAY;gCAAK;4BAAE;4BACrG;gCAAE,IAAI;gCAAgB,MAAM;gCAAgB,SAAS;oCAAE,YAAY;gCAAK;4BAAE;yBAC3E;;;;;;;;;;;8BAKL,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gJAAA,CAAA,gBAAa;wBACZ,SAAS,YAAY,WAAW;4BAC9B,aAAa;4BACb,UAAU;4BACV,oBAAoB;4BACpB,cAAc;4BACd,aAAa;4BACb,gBAAgB;wBAClB;wBACA,SAAS;wBACT,WAAW,QAAQ,UAAU;;;;;;;;;;;8BAKjC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;wBACX,YAAY,YAAY,aAAa,EAAE;wBACvC,WAAW,YAAY,aAAa,EAAE;wBACtC,WAAW,EAAE;wBACb,oBAAoB;wBACpB,mBAAmB;;;;;;;;;;;8BAKvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gJAAA,CAAA,gBAAa;wBACZ,MAAM,YAAY,eAAe,EAAE;wBACnC,QAAO;wBACP,eAAe;wBACf,gBAAgB;wBAChB,YAAY;4BACV,KAAK;4BACL,KAAK;4BACL,KAAK;wBACP;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,kJAAA,CAAA,kBAAe;wBACd,WAAW,YAAY,aAAa;4BAClC,gBAAgB,EAAE;4BAClB,gBAAgB,EAAE;4BAClB,gBAAgB;gCAAE,OAAO;gCAAG,OAAO;gCAAU,YAAY;gCAAG,SAAS,EAAE;4BAAC;wBAC1E;wBACA,YAAY,YAAY,cAAc;4BACpC,SAAS;4BACT,SAAS,EAAE;4BACX,YAAY;4BACZ,aAAa,IAAI;4BACjB,iBAAiB;wBACnB;wBACA,iBAAiB;wBACjB,qBAAqB;;;;;;;;;;;8BAKzB,6LAAC,gJAAA,CAAA,gBAAa;oBACZ,UAAU;oBACV,YAAY;oBACZ,SAAS,IAAM,QAAQ,GAAG,CAAC;oBAC3B,gBAAgB,IAAM,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;AAK5C;GAjLa;;QAYP,uIAAA,CAAA,gBAAa;;;KAZN", "debugId": null}}, {"offset": {"line": 2952, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/TrendsErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { AlertTriangle, RefreshCw, Home } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n}\n\nexport class TrendsErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Trends page error:', error, errorInfo)\n    // Send to monitoring service in production\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <div className=\"max-w-md w-full mx-4\">\n            <div className=\"bg-white rounded-lg border border-red-200 p-8 text-center\">\n              <div className=\"flex justify-center mb-4\">\n                <div className=\"p-3 bg-red-100 rounded-full\">\n                  <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n                </div>\n              </div>\n              \n              <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                Unable to load trends data\n              </h2>\n              \n              <p className=\"text-gray-600 mb-6\">\n                {this.state.error?.message || 'An unexpected error occurred while loading the trends page.'}\n              </p>\n              \n              <div className=\"space-y-3\">\n                <Button \n                  onClick={() => {\n                    this.setState({ hasError: false })\n                    window.location.reload()\n                  }}\n                  className=\"w-full flex items-center justify-center\"\n                >\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  Try Again\n                </Button>\n                \n                <Button \n                  variant=\"outline\"\n                  onClick={() => window.location.href = '/dashboard'}\n                  className=\"w-full flex items-center justify-center\"\n                >\n                  <Home className=\"h-4 w-4 mr-2\" />\n                  Back to Dashboard\n                </Button>\n              </div>\n              \n              {process.env.NODE_ENV === 'development' && this.state.error && (\n                <details className=\"mt-6 text-left\">\n                  <summary className=\"text-sm text-gray-500 cursor-pointer\">\n                    Error Details (Development)\n                  </summary>\n                  <pre className=\"mt-2 text-xs text-gray-700 bg-gray-100 p-3 rounded overflow-auto\">\n                    {this.state.error.stack}\n                  </pre>\n                </details>\n              )}\n            </div>\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}"], "names": [], "mappings": ";;;AAwEe;;AAtEf;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAeO,MAAM,4BAA4B,6JAAA,CAAA,UAAK,CAAC,SAAS;IACtD,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,sBAAsB,OAAO;IAC3C,2CAA2C;IAC7C;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAI7B,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAIzD,6LAAC;gCAAE,WAAU;0CACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW;;;;;;0CAGhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;4CACP,IAAI,CAAC,QAAQ,CAAC;gDAAE,UAAU;4CAAM;4CAChC,OAAO,QAAQ,CAAC,MAAM;wCACxB;wCACA,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIxC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACtC,WAAU;;0DAEV,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAKpC,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,6LAAC;gCAAQ,WAAU;;kDACjB,6LAAC;wCAAQ,WAAU;kDAAuC;;;;;;kDAG1D,6LAAC;wCAAI,WAAU;kDACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQvC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/LoadingSkeleton.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ninterface LoadingSkeletonProps {\n  type: 'page' | 'metrics' | 'chart' | 'heatmap'\n}\n\nconst MetricsSkeleton: React.FC = () => (\n  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n    {[1, 2, 3].map(i => (\n      <div key={i} className=\"bg-white p-6 rounded-lg border\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-4\"></div>\n          <div className=\"h-8 bg-gray-200 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-1/3\"></div>\n        </div>\n      </div>\n    ))}\n  </div>\n)\n\nconst ChartSkeleton: React.FC = () => (\n  <div className=\"bg-white p-6 rounded-lg border\">\n    <div className=\"animate-pulse\">\n      <div className=\"h-4 bg-gray-200 rounded w-1/4 mb-4\"></div>\n      <div className=\"h-64 bg-gray-200 rounded\"></div>\n    </div>\n  </div>\n)\n\nconst HeatmapSkeleton: React.FC = () => (\n  <div className=\"bg-white p-6 rounded-lg border\">\n    <div className=\"animate-pulse\">\n      <div className=\"h-4 bg-gray-200 rounded w-1/3 mb-4\"></div>\n      <div className=\"h-96 bg-gray-200 rounded\"></div>\n    </div>\n  </div>\n)\n\nconst PageSkeleton: React.FC = () => (\n  <div className=\"min-h-screen bg-gray-50\">\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header Skeleton */}\n      <div className=\"bg-white rounded-lg border p-6 mb-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex justify-between items-start\">\n            <div>\n              <div className=\"h-6 bg-gray-200 rounded w-48 mb-2\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-32\"></div>\n            </div>\n            <div className=\"flex space-x-2\">\n              <div className=\"h-8 bg-gray-200 rounded w-20\"></div>\n              <div className=\"h-8 bg-gray-200 rounded w-20\"></div>\n              <div className=\"h-8 bg-gray-200 rounded w-20\"></div>\n              <div className=\"h-8 bg-gray-200 rounded w-20\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters Skeleton */}\n      <div className=\"bg-white rounded-lg border p-4 mb-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-16 mb-4\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            {[1, 2, 3, 4].map(i => (\n              <div key={i}>\n                <div className=\"h-3 bg-gray-200 rounded w-20 mb-2\"></div>\n                <div className=\"h-8 bg-gray-200 rounded\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Metrics Skeleton */}\n      <div className=\"mb-6\">\n        <div className=\"mb-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-5 bg-gray-200 rounded w-32 mb-2\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-48\"></div>\n          </div>\n        </div>\n        <MetricsSkeleton />\n      </div>\n\n      {/* Charts Skeleton */}\n      <div className=\"mb-6\">\n        <div className=\"mb-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-5 bg-gray-200 rounded w-32 mb-2\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n          </div>\n        </div>\n        <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n          <ChartSkeleton />\n          <ChartSkeleton />\n          <div className=\"xl:col-span-2\">\n            <ChartSkeleton />\n          </div>\n        </div>\n      </div>\n\n      {/* Heatmap Skeleton */}\n      <div className=\"mb-6\">\n        <HeatmapSkeleton />\n      </div>\n\n      {/* Forecasts Skeleton */}\n      <div className=\"mb-6\">\n        <div className=\"mb-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-5 bg-gray-200 rounded w-32 mb-2\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n          </div>\n        </div>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n          {[1, 2, 3].map(i => (\n            <div key={i} className=\"bg-white p-6 rounded-lg border\">\n              <div className=\"animate-pulse\">\n                <div className=\"h-4 bg-gray-200 rounded w-20 mb-4 mx-auto\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-16 mb-2 mx-auto\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-24 mx-auto\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n        <div className=\"bg-white p-6 rounded-lg border\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-4 bg-gray-200 rounded w-28 mb-4\"></div>\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-gray-200 rounded\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-4/6\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Actions Skeleton */}\n      <div className=\"bg-white p-6 rounded-lg border\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <div className=\"h-4 bg-gray-200 rounded w-16 mb-1\"></div>\n              <div className=\"h-3 bg-gray-200 rounded w-48\"></div>\n            </div>\n            <div className=\"flex space-x-2\">\n              {[1, 2, 3, 4, 5, 6].map(i => (\n                <div key={i} className=\"h-8 bg-gray-200 rounded w-20\"></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n)\n\nexport const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ type }) => {\n  switch (type) {\n    case 'metrics':\n      return <MetricsSkeleton />\n    case 'chart':\n      return <ChartSkeleton />\n    case 'heatmap':\n      return <HeatmapSkeleton />\n    case 'page':\n    default:\n      return <PageSkeleton />\n  }\n}"], "names": [], "mappings": ";;;;AAAA;;AAQA,MAAM,kBAA4B,kBAChC,6LAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAA,kBACb,6LAAC;gBAAY,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;eAJT;;;;;;;;;;KAHV;AAcN,MAAM,gBAA0B,kBAC9B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;MAJf;AASN,MAAM,kBAA4B,kBAChC,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;MAJf;AASN,MAAM,eAAyB,kBAC7B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAA,kBAChB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;uCAFP;;;;;;;;;;;;;;;;;;;;;8BAUlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;;;;;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;;;;8CACD,6LAAC;;;;;8CACD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;;8BAMP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;;;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAA,kBACb,6LAAC;oCAAY,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;mCAJT;;;;;;;;;;sCASd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAA,kBACtB,6LAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA9GpB;AAwHC,MAAM,kBAAkD,CAAC,EAAE,IAAI,EAAE;IACtE,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC;;;;;QACV,KAAK;YACH,qBAAO,6LAAC;;;;;QACV,KAAK;YACH,qBAAO,6LAAC;;;;;QACV,KAAK;QACL;YACE,qBAAO,6LAAC;;;;;IACZ;AACF;MAZa", "debugId": null}}, {"offset": {"line": 3785, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/index.ts"], "sourcesContent": ["// Trends Components Barrel Export\n\nexport { TrendsPage } from './TrendsPage'\nexport { TrendsHeader } from './TrendsHeader'\nexport { TrendsFilters } from './TrendsFilters'\nexport { TrendsMetrics } from './TrendsMetrics'\nexport { TrendsCharts } from './TrendsCharts'\nexport { TrendsHeatmap } from './TrendsHeatmap'\nexport { TrendsForecasts } from './TrendsForecasts'\nexport { TrendsActions } from './TrendsActions'\nexport { TrendsErrorBoundary } from './TrendsErrorBoundary'\nexport { LoadingSkeleton } from './LoadingSkeleton'"], "names": [], "mappings": "AAAA,kCAAkC;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3834, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/trends/page.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense } from 'react'\nimport { TrendsPage } from '@/components/trends'\nimport { TrendsErrorBoundary } from '@/components/trends/TrendsErrorBoundary'\nimport { LoadingSkeleton } from '@/components/trends/LoadingSkeleton'\n\nexport default function TrendsPageRoute() {\n  return (\n    <TrendsErrorBoundary>\n      <Suspense fallback={<LoadingSkeleton type=\"page\" />}>\n        <TrendsPage />\n      </Suspense>\n    </TrendsErrorBoundary>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,qBACE,6LAAC,sJAAA,CAAA,sBAAmB;kBAClB,cAAA,6LAAC,6JAAA,CAAA,WAAQ;YAAC,wBAAU,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,MAAK;;;;;;sBACxC,cAAA,6LAAC,6IAAA,CAAA,aAAU;;;;;;;;;;;;;;;AAInB;KARwB", "debugId": null}}]}