{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/trends/LoadingSkeleton.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ninterface LoadingSkeletonProps {\n  type: 'page' | 'metrics' | 'chart' | 'heatmap'\n}\n\nconst MetricsSkeleton: React.FC = () => (\n  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n    {[1, 2, 3].map(i => (\n      <div key={i} className=\"bg-white p-6 rounded-lg border\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-4\"></div>\n          <div className=\"h-8 bg-gray-200 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-1/3\"></div>\n        </div>\n      </div>\n    ))}\n  </div>\n)\n\nconst ChartSkeleton: React.FC = () => (\n  <div className=\"bg-white p-6 rounded-lg border\">\n    <div className=\"animate-pulse\">\n      <div className=\"h-4 bg-gray-200 rounded w-1/4 mb-4\"></div>\n      <div className=\"h-64 bg-gray-200 rounded\"></div>\n    </div>\n  </div>\n)\n\nconst HeatmapSkeleton: React.FC = () => (\n  <div className=\"bg-white p-6 rounded-lg border\">\n    <div className=\"animate-pulse\">\n      <div className=\"h-4 bg-gray-200 rounded w-1/3 mb-4\"></div>\n      <div className=\"h-96 bg-gray-200 rounded\"></div>\n    </div>\n  </div>\n)\n\nconst PageSkeleton: React.FC = () => (\n  <div className=\"min-h-screen bg-gray-50\">\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header Skeleton */}\n      <div className=\"bg-white rounded-lg border p-6 mb-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex justify-between items-start\">\n            <div>\n              <div className=\"h-6 bg-gray-200 rounded w-48 mb-2\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-32\"></div>\n            </div>\n            <div className=\"flex space-x-2\">\n              <div className=\"h-8 bg-gray-200 rounded w-20\"></div>\n              <div className=\"h-8 bg-gray-200 rounded w-20\"></div>\n              <div className=\"h-8 bg-gray-200 rounded w-20\"></div>\n              <div className=\"h-8 bg-gray-200 rounded w-20\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters Skeleton */}\n      <div className=\"bg-white rounded-lg border p-4 mb-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-16 mb-4\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            {[1, 2, 3, 4].map(i => (\n              <div key={i}>\n                <div className=\"h-3 bg-gray-200 rounded w-20 mb-2\"></div>\n                <div className=\"h-8 bg-gray-200 rounded\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Metrics Skeleton */}\n      <div className=\"mb-6\">\n        <div className=\"mb-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-5 bg-gray-200 rounded w-32 mb-2\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-48\"></div>\n          </div>\n        </div>\n        <MetricsSkeleton />\n      </div>\n\n      {/* Charts Skeleton */}\n      <div className=\"mb-6\">\n        <div className=\"mb-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-5 bg-gray-200 rounded w-32 mb-2\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n          </div>\n        </div>\n        <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n          <ChartSkeleton />\n          <ChartSkeleton />\n          <div className=\"xl:col-span-2\">\n            <ChartSkeleton />\n          </div>\n        </div>\n      </div>\n\n      {/* Heatmap Skeleton */}\n      <div className=\"mb-6\">\n        <HeatmapSkeleton />\n      </div>\n\n      {/* Forecasts Skeleton */}\n      <div className=\"mb-6\">\n        <div className=\"mb-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-5 bg-gray-200 rounded w-32 mb-2\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-64\"></div>\n          </div>\n        </div>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n          {[1, 2, 3].map(i => (\n            <div key={i} className=\"bg-white p-6 rounded-lg border\">\n              <div className=\"animate-pulse\">\n                <div className=\"h-4 bg-gray-200 rounded w-20 mb-4 mx-auto\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-16 mb-2 mx-auto\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-24 mx-auto\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n        <div className=\"bg-white p-6 rounded-lg border\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-4 bg-gray-200 rounded w-28 mb-4\"></div>\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-gray-200 rounded\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-4/6\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Actions Skeleton */}\n      <div className=\"bg-white p-6 rounded-lg border\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <div className=\"h-4 bg-gray-200 rounded w-16 mb-1\"></div>\n              <div className=\"h-3 bg-gray-200 rounded w-48\"></div>\n            </div>\n            <div className=\"flex space-x-2\">\n              {[1, 2, 3, 4, 5, 6].map(i => (\n                <div key={i} className=\"h-8 bg-gray-200 rounded w-20\"></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n)\n\nexport const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ type }) => {\n  switch (type) {\n    case 'metrics':\n      return <MetricsSkeleton />\n    case 'chart':\n      return <ChartSkeleton />\n    case 'heatmap':\n      return <HeatmapSkeleton />\n    case 'page':\n    default:\n      return <PageSkeleton />\n  }\n}"], "names": [], "mappings": ";;;;AAAA;;AAQA,MAAM,kBAA4B,kBAChC,6LAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAA,kBACb,6LAAC;gBAAY,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;eAJT;;;;;;;;;;KAHV;AAcN,MAAM,gBAA0B,kBAC9B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;MAJf;AASN,MAAM,kBAA4B,kBAChC,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;MAJf;AASN,MAAM,eAAyB,kBAC7B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAA,kBAChB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;uCAFP;;;;;;;;;;;;;;;;;;;;;8BAUlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;;;;;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;;;;8CACD,6LAAC;;;;;8CACD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;;8BAMP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;;;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAA,kBACb,6LAAC;oCAAY,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;mCAJT;;;;;;;;;;sCASd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAA,kBACtB,6LAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA9GpB;AAwHC,MAAM,kBAAkD,CAAC,EAAE,IAAI,EAAE;IACtE,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC;;;;;QACV,KAAK;YACH,qBAAO,6LAAC;;;;;QACV,KAAK;YACH,qBAAO,6LAAC;;;;;QACV,KAAK;QACL;YACE,qBAAO,6LAAC;;;;;IACZ;AACF;MAZa", "debugId": null}}]}