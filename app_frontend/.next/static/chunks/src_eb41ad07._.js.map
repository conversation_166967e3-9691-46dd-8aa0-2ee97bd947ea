{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-white hover:bg-primary-600 active:bg-primary-700 shadow-sm hover:shadow-md\",\n        secondary: \"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300 border border-secondary-300\",\n        outline: \"border border-border bg-background hover:bg-surface-200 hover:border-secondary-400 text-foreground\",\n        ghost: \"hover:bg-surface-200 hover:text-accent text-secondary-700\",\n        link: \"text-primary hover:text-primary-600 underline-offset-4 hover:underline p-0 h-auto\",\n        destructive: \"bg-error text-white hover:bg-red-600 active:bg-red-700 shadow-sm\",\n        success: \"bg-success text-white hover:bg-green-600 active:bg-green-700 shadow-sm\",\n      },\n      size: {\n        sm: \"h-8 px-3 py-1.5 text-xs rounded-md\",\n        default: \"h-10 px-4 py-2 text-sm\",\n        lg: \"h-12 px-6 py-3 text-base rounded-xl\",\n        xl: \"h-14 px-8 py-4 text-lg rounded-xl\",\n        icon: \"h-10 w-10 p-0\",\n        \"icon-sm\": \"h-8 w-8 p-0\",\n        \"icon-lg\": \"h-12 w-12 p-0\",\n      },\n      loading: {\n        true: \"cursor-not-allowed\",\n        false: \"\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      loading: false,\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  loadingText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant, \n    size, \n    loading = false,\n    loadingText,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled,\n    asChild = false, \n    ...props \n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    const isDisabled = disabled || loading\n    \n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, loading, className }))}\n        disabled={isDisabled}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            aria-hidden=\"true\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {!loading && leftIcon && <span className=\"mr-1\">{leftIcon}</span>}\n        {loading ? loadingText || children : children}\n        {!loading && rightIcon && <span className=\"ml-1\">{rightIcon}</span>}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,qYACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAaF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,WAAW,EACX,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,aAAa,YAAY;IAE/B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAS;QAAU;QACjE,UAAU;QACV,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,eAAY;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,WAAW,0BAAY,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD,UAAU,eAAe,WAAW;YACpC,CAAC,WAAW,2BAAa,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosError, AxiosRequestConfig } from \"axios\";\nimport { toast } from \"react-hot-toast\";\nimport { getSession } from \"next-auth/react\";\n\nexport interface ApiError {\n  message: string;\n  status?: number;\n  code?: string;\n  details?: any;\n}\n\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  status: number;\n}\n\nexport interface LoadingState {\n  isLoading: boolean;\n  error: ApiError | null;\n}\n\n// Cache configuration\ninterface CacheConfig {\n  ttl: number; // Time to live in milliseconds\n  maxSize: number;\n}\n\ninterface CacheEntry<T> {\n  data: T;\n  timestamp: number;\n  ttl: number;\n}\n\nclass ApiCache {\n  private cache = new Map<string, CacheEntry<any>>();\n  private config: CacheConfig = {\n    ttl: 5 * 60 * 1000, // 5 minutes default\n    maxSize: 100,\n  };\n\n  set<T>(key: string, data: T, ttl?: number): void {\n    // Remove oldest entries if cache is full\n    if (this.cache.size >= this.config.maxSize) {\n      const firstKey = this.cache.keys().next().value;\n      if (!firstKey) return;\n      this.cache.delete(firstKey);\n    }\n\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl: ttl || this.config.ttl,\n    });\n  }\n\n  get<T>(key: string): T | null {\n    const entry = this.cache.get(key);\n    if (!entry) return null;\n\n    // Check if expired\n    if (Date.now() - entry.timestamp > entry.ttl) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return entry.data;\n  }\n\n  clear(): void {\n    this.cache.clear();\n  }\n\n  delete(key: string): void {\n    this.cache.delete(key);\n  }\n}\n\n// Retry configuration\ninterface RetryConfig {\n  retries: number;\n  retryDelay: number;\n  retryCondition?: (error: AxiosError) => boolean;\n}\n\nclass ApiClient {\n  private client: AxiosInstance;\n  private cache = new ApiCache();\n  private loadingStates = new Map<string, boolean>();\n  private retryConfig: RetryConfig = {\n    retries: 3,\n    retryDelay: 1000,\n    retryCondition: (error: AxiosError) => {\n      // Retry on network errors and 5xx status codes\n      return (\n        !error.response ||\n        (error.response.status >= 500 && error.response.status < 600)\n      );\n    },\n  };\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      timeout: 5000, // Reduced timeout for faster failure detection\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor\n    this.client.interceptors.request.use(\n      async (config) => {\n        // Add auth token if available - use NextAuth session instead of localStorage\n        if (typeof window !== \"undefined\") {\n          try {\n            const session = await getSession();\n            \n            // Check for NextAuth session first\n            if (session?.accessToken) {\n              config.headers.Authorization = `Bearer ${session.accessToken}`;\n            } else {\n              // Fallback to localStorage for backward compatibility\n              const token = localStorage.getItem(\"auth_token\");\n              if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n              }\n            }\n          } catch (error) {\n            // If getSession fails, try localStorage as fallback\n            const token = localStorage.getItem(\"auth_token\");\n            if (token) {\n              config.headers.Authorization = `Bearer ${token}`;\n            }\n          }\n        }\n\n        // Add request ID for tracking\n        config.headers[\"X-Request-ID\"] = crypto.randomUUID();\n\n        // Set loading state\n        const requestKey = this.getRequestKey(config);\n        this.setLoading(requestKey, true);\n\n        // Security: Never log requests containing sensitive data\n        if (process.env.NODE_ENV === \"development\") {\n          const hasCredentials =\n            config.url?.includes(\"/auth/\") ||\n            config.data?.password ||\n            config.data?.token;\n          if (!hasCredentials) {\n            // Only log non-sensitive requests in development\n          }\n        }\n\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor with retry logic\n    this.client.interceptors.response.use(\n      (response) => {\n        // Clear loading state\n        const requestKey = this.getRequestKey(response.config);\n        this.setLoading(requestKey, false);\n        return response;\n      },\n      async (error: AxiosError) => {\n        const config = error.config as AxiosRequestConfig & {\n          _retryCount?: number;\n        };\n        const requestKey = this.getRequestKey(config);\n        this.setLoading(requestKey, false);\n\n        // Retry logic\n        if (this.shouldRetry(error, config)) {\n          config._retryCount = (config._retryCount || 0) + 1;\n\n          // Exponential backoff\n          const delay =\n            this.retryConfig.retryDelay * Math.pow(2, config._retryCount - 1);\n          await new Promise((resolve) => setTimeout(resolve, delay));\n\n          return this.client(config);\n        }\n\n        const apiError: ApiError = {\n          message: error.message || \"An unexpected error occurred\",\n          status: error.response?.status,\n          details: error.response?.data,\n        };\n\n        if (error.response?.data) {\n          const errorData = error.response.data as any;\n          apiError.message =\n            errorData.detail || errorData.message || apiError.message;\n          apiError.code = errorData.code;\n        }\n\n        // Handle different error types\n        this.handleError(apiError);\n\n        return Promise.reject(apiError);\n      }\n    );\n  }\n\n  private shouldRetry(\n    error: AxiosError,\n    config: AxiosRequestConfig & { _retryCount?: number }\n  ): boolean {\n    const retryCount = config._retryCount || 0;\n    return (\n      retryCount < this.retryConfig.retries &&\n      config.method?.toLowerCase() === \"get\" && // Only retry GET requests\n      this.retryConfig.retryCondition?.(error) === true\n    );\n  }\n\n  private handleError(error: ApiError): void {\n    // Handle connection errors (backend not running)\n    if (\n      !error.status ||\n      error.message.includes(\"Network Error\") ||\n      error.message.includes(\"ECONNREFUSED\")\n    ) {\n      // Don't show error toast for connection issues when backend is expected to be down\n      return;\n    }\n\n    // Handle auth errors\n    if (error.status === 401) {\n      if (typeof window !== \"undefined\") {\n        localStorage.removeItem(\"auth_token\");\n        toast.error(\"Session expired. Please login again.\");\n        window.location.href = \"/auth/login\";\n      }\n      return;\n    }\n\n    // Handle other errors with toast notifications\n    if (error.status && error.status >= 400) {\n      if (error.status >= 500) {\n        toast.error(\"Server error. Please try again later.\");\n      } else if (error.status === 404) {\n        toast.error(\"Resource not found\");\n      } else {\n        toast.error(error.message);\n      }\n    }\n  }\n\n  private getRequestKey(config: AxiosRequestConfig): string {\n    return `${config.method?.toUpperCase()}_${config.url}`;\n  }\n\n  private setLoading(key: string, loading: boolean): void {\n    if (loading) {\n      this.loadingStates.set(key, true);\n    } else {\n      this.loadingStates.delete(key);\n    }\n  }\n\n  // Generic request methods with caching\n  async get<T>(url: string, params?: any, useCache = true): Promise<T> {\n    const cacheKey = `GET_${url}_${JSON.stringify(params || {})}`;\n\n    // Check cache first\n    if (useCache) {\n      const cached = this.cache.get<T>(cacheKey);\n      if (cached) {\n        return cached;\n      }\n    }\n\n    const response = await this.client.get(url, { params });\n    const data = response.data;\n\n    // Cache successful GET requests\n    if (useCache && response.status === 200) {\n      this.cache.set(cacheKey, data);\n    }\n\n    return data;\n  }\n\n  async post<T>(url: string, data?: any): Promise<T> {\n    // If no API URL is set, return mock response\n    console.log(\"API URL: \", process.env.NEXT_PUBLIC_API_URL);\n    if (!process.env.NEXT_PUBLIC_API_URL) {\n      throw new Error(\"Backend not available\");\n    }\n\n    const response = await this.client.post(url, data);\n\n    // Invalidate related cache entries\n    this.invalidateCache(url);\n\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: any): Promise<T> {\n    const response = await this.client.put(url, data);\n\n    // Invalidate related cache entries\n    this.invalidateCache(url);\n\n    return response.data;\n  }\n\n  async patch<T>(url: string, data?: any): Promise<T> {\n    const response = await this.client.patch(url, data);\n\n    // Invalidate related cache entries\n    this.invalidateCache(url);\n\n    return response.data;\n  }\n\n  async delete<T>(url: string): Promise<T> {\n    const response = await this.client.delete(url);\n\n    // Invalidate related cache entries\n    this.invalidateCache(url);\n\n    return response.data;\n  }\n\n  // File upload with progress\n  async uploadFile<T>(\n    url: string,\n    file: File | Blob,\n    onProgress?: (progress: number) => void\n  ): Promise<T> {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n\n    const response = await this.client.post(url, formData, {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round(\n            (progressEvent.loaded * 100) / progressEvent.total\n          );\n          onProgress(progress);\n        }\n      },\n    });\n\n    return response.data;\n  }\n\n  // Optimistic updates\n  async optimisticUpdate<T>(\n    url: string,\n    data: any,\n    optimisticData: T,\n    cacheKey?: string\n  ): Promise<T> {\n    // Set optimistic data immediately\n    if (cacheKey) {\n      this.cache.set(cacheKey, optimisticData);\n    }\n\n    try {\n      const result = await this.put<T>(url, data);\n\n      // Update cache with real data\n      if (cacheKey) {\n        this.cache.set(cacheKey, result);\n      }\n\n      return result;\n    } catch (error) {\n      // Revert optimistic update on error\n      if (cacheKey) {\n        this.cache.delete(cacheKey);\n      }\n      throw error;\n    }\n  }\n\n  // Cache management\n  private invalidateCache(url: string): void {\n    // Remove cache entries that might be affected by this change\n    const keysToDelete: string[] = [];\n\n    // Simple pattern matching - could be more sophisticated\n    const baseUrl = url.split(\"/\").slice(0, -1).join(\"/\");\n\n    for (const key of Array.from(this.cache[\"cache\"].keys())) {\n      if (key.includes(baseUrl) || key.includes(url)) {\n        keysToDelete.push(key);\n      }\n    }\n\n    keysToDelete.forEach((key) => this.cache.delete(key));\n  }\n\n  // Loading state management\n  isLoading(url: string, method = \"GET\"): boolean {\n    const key = `${method.toUpperCase()}_${url}`;\n    return this.loadingStates.has(key);\n  }\n\n  // Utility methods\n  setAuthToken(token: string) {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(\"auth_token\", token);\n    }\n  }\n\n  removeAuthToken() {\n    if (typeof window !== \"undefined\") {\n      localStorage.removeItem(\"auth_token\");\n    }\n  }\n\n  // Cache utilities\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  getCacheStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache[\"cache\"].size,\n      keys: Array.from(this.cache[\"cache\"].keys()),\n    };\n  }\n\n  // Health check\n  async healthCheck(): Promise<boolean> {\n    try {\n      await this.get(\"/health\", {}, false); // Don't cache health checks\n      return true;\n    } catch {\n      return false;\n    }\n  }\n}\n\nexport const apiClient = new ApiClient();\nexport default apiClient;\n"], "names": [], "mappings": ";;;;AAuGe;AAvGf;AACA;AACA;;;;AAgCA,MAAM;IACI,QAAQ,IAAI,MAA+B;IAC3C,SAAsB;QAC5B,KAAK,IAAI,KAAK;QACd,SAAS;IACX,EAAE;IAEF,IAAO,GAAW,EAAE,IAAO,EAAE,GAAY,EAAQ;QAC/C,yCAAyC;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YAC1C,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK;YAC/C,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACpB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG;QAC7B;IACF;IAEA,IAAO,GAAW,EAAY;QAC5B,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,OAAO,OAAO;QAEnB,mBAAmB;QACnB,IAAI,KAAK,GAAG,KAAK,MAAM,SAAS,GAAG,MAAM,GAAG,EAAE;YAC5C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,MAAM,IAAI;IACnB;IAEA,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,OAAO,GAAW,EAAQ;QACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACpB;AACF;AASA,MAAM;IACI,OAAsB;IACtB,QAAQ,IAAI,WAAW;IACvB,gBAAgB,IAAI,MAAuB;IAC3C,cAA2B;QACjC,SAAS;QACT,YAAY;QACZ,gBAAgB,CAAC;YACf,+CAA+C;YAC/C,OACE,CAAC,MAAM,QAAQ,IACd,MAAM,QAAQ,CAAC,MAAM,IAAI,OAAO,MAAM,QAAQ,CAAC,MAAM,GAAG;QAE7D;IACF,EAAE;IAEF,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACzB,SAAS,6DAAmC;YAC5C,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,OAAO;YACL,6EAA6E;YAC7E,wCAAmC;gBACjC,IAAI;oBACF,MAAM,UAAU,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;oBAE/B,mCAAmC;oBACnC,IAAI,SAAS,aAAa;wBACxB,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ,WAAW,EAAE;oBAChE,OAAO;wBACL,sDAAsD;wBACtD,MAAM,QAAQ,aAAa,OAAO,CAAC;wBACnC,IAAI,OAAO;4BACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;wBAClD;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,oDAAoD;oBACpD,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,OAAO;wBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;oBAClD;gBACF;YACF;YAEA,8BAA8B;YAC9B,OAAO,OAAO,CAAC,eAAe,GAAG,OAAO,UAAU;YAElD,oBAAoB;YACpB,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,YAAY;YAE5B,yDAAyD;YACzD,wCAA4C;gBAC1C,MAAM,iBACJ,OAAO,GAAG,EAAE,SAAS,aACrB,OAAO,IAAI,EAAE,YACb,OAAO,IAAI,EAAE;gBACf,IAAI,CAAC,gBAAgB;gBACnB,iDAAiD;gBACnD;YACF;YAEA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,wCAAwC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC;YACC,sBAAsB;YACtB,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,MAAM;YACrD,IAAI,CAAC,UAAU,CAAC,YAAY;YAC5B,OAAO;QACT,GACA,OAAO;YACL,MAAM,SAAS,MAAM,MAAM;YAG3B,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,YAAY;YAE5B,cAAc;YACd,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,SAAS;gBACnC,OAAO,WAAW,GAAG,CAAC,OAAO,WAAW,IAAI,CAAC,IAAI;gBAEjD,sBAAsB;gBACtB,MAAM,QACJ,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG;gBACjE,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBAEnD,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB;YAEA,MAAM,WAAqB;gBACzB,SAAS,MAAM,OAAO,IAAI;gBAC1B,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS,MAAM,QAAQ,EAAE;YAC3B;YAEA,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,MAAM,YAAY,MAAM,QAAQ,CAAC,IAAI;gBACrC,SAAS,OAAO,GACd,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,SAAS,OAAO;gBAC3D,SAAS,IAAI,GAAG,UAAU,IAAI;YAChC;YAEA,+BAA+B;YAC/B,IAAI,CAAC,WAAW,CAAC;YAEjB,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEQ,YACN,KAAiB,EACjB,MAAqD,EAC5C;QACT,MAAM,aAAa,OAAO,WAAW,IAAI;QACzC,OACE,aAAa,IAAI,CAAC,WAAW,CAAC,OAAO,IACrC,OAAO,MAAM,EAAE,kBAAkB,SAAS,0BAA0B;QACpE,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,WAAW;IAEjD;IAEQ,YAAY,KAAe,EAAQ;QACzC,iDAAiD;QACjD,IACE,CAAC,MAAM,MAAM,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,iBACvB;YACA,mFAAmF;YACnF;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM,MAAM,KAAK,KAAK;YACxB,wCAAmC;gBACjC,aAAa,UAAU,CAAC;gBACxB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;YACA;QACF;QAEA,+CAA+C;QAC/C,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,KAAK;YACvC,IAAI,MAAM,MAAM,IAAI,KAAK;gBACvB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK;gBAC/B,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;YAC3B;QACF;IACF;IAEQ,cAAc,MAA0B,EAAU;QACxD,OAAO,GAAG,OAAO,MAAM,EAAE,cAAc,CAAC,EAAE,OAAO,GAAG,EAAE;IACxD;IAEQ,WAAW,GAAW,EAAE,OAAgB,EAAQ;QACtD,IAAI,SAAS;YACX,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;YACL,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5B;IACF;IAEA,uCAAuC;IACvC,MAAM,IAAO,GAAW,EAAE,MAAY,EAAE,WAAW,IAAI,EAAc;QACnE,MAAM,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;QAE7D,oBAAoB;QACpB,IAAI,UAAU;YACZ,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAI;YACjC,IAAI,QAAQ;gBACV,OAAO;YACT;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK;YAAE;QAAO;QACrD,MAAM,OAAO,SAAS,IAAI;QAE1B,gCAAgC;QAChC,IAAI,YAAY,SAAS,MAAM,KAAK,KAAK;YACvC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;QAC3B;QAEA,OAAO;IACT;IAEA,MAAM,KAAQ,GAAW,EAAE,IAAU,EAAc;QACjD,6CAA6C;QAC7C,QAAQ,GAAG,CAAC;QACZ,uCAAsC;;QAEtC;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;QAE7C,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC;QAErB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,IAAO,GAAW,EAAE,IAAU,EAAc;QAChD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK;QAE5C,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC;QAErB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAS,GAAW,EAAE,IAAU,EAAc;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK;QAE9C,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC;QAErB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAU,GAAW,EAAc;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAE1C,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC;QAErB,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,WACJ,GAAW,EACX,IAAiB,EACjB,UAAuC,EAC3B;QACZ,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,UAAU;YACrD,SAAS;gBACP,gBAAgB;YAClB;YACA,kBAAkB,CAAC;gBACjB,IAAI,cAAc,cAAc,KAAK,EAAE;oBACrC,MAAM,WAAW,KAAK,KAAK,CACzB,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBAEpD,WAAW;gBACb;YACF;QACF;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,iBACJ,GAAW,EACX,IAAS,EACT,cAAiB,EACjB,QAAiB,EACL;QACZ,kCAAkC;QAClC,IAAI,UAAU;YACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;QAC3B;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,GAAG,CAAI,KAAK;YAEtC,8BAA8B;YAC9B,IAAI,UAAU;gBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;YAC3B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,oCAAoC;YACpC,IAAI,UAAU;gBACZ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;YACA,MAAM;QACR;IACF;IAEA,mBAAmB;IACX,gBAAgB,GAAW,EAAQ;QACzC,6DAA6D;QAC7D,MAAM,eAAyB,EAAE;QAEjC,wDAAwD;QACxD,MAAM,UAAU,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;QAEjD,KAAK,MAAM,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAK;YACxD,IAAI,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,MAAM;gBAC9C,aAAa,IAAI,CAAC;YACpB;QACF;QAEA,aAAa,OAAO,CAAC,CAAC,MAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAClD;IAEA,2BAA2B;IAC3B,UAAU,GAAW,EAAE,SAAS,KAAK,EAAW;QAC9C,MAAM,MAAM,GAAG,OAAO,WAAW,GAAG,CAAC,EAAE,KAAK;QAC5C,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;IAChC;IAEA,kBAAkB;IAClB,aAAa,KAAa,EAAE;QAC1B,wCAAmC;YACjC,aAAa,OAAO,CAAC,cAAc;QACrC;IACF;IAEA,kBAAkB;QAChB,wCAAmC;YACjC,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,kBAAkB;IAClB,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,gBAAkD;QAChD,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI;YAC9B,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI;QAC3C;IACF;IAEA,eAAe;IACf,MAAM,cAAgC;QACpC,IAAI;YACF,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,QAAQ,4BAA4B;YAClE,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/hooks/api/use-auth.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { useSession, signIn, signOut } from 'next-auth/react'\nimport { apiClient } from '@/lib/api'\nimport { queryKeys } from '@/lib/react-query'\nimport { toast } from 'react-hot-toast'\n\n// Types - Updated to match database schema\nexport interface User {\n  id: string;\n  email: string;\n  full_name: string;\n  avatar_url?: string;\n  subscription_tier: \"free\" | \"pro\" | \"enterprise\";\n  \n  // Publishing preferences from database\n  preferred_ai_provider: string;\n  default_writing_style: string;\n  default_target_audience: string;\n  \n  // KDP Integration\n  kdp_email?: string;\n  kdp_settings: Record<string, any>;\n  \n  // User preferences and settings from database\n  notification_preferences: {\n    email: boolean;\n    in_app: boolean;\n  };\n  content_preferences: {\n    theme: 'light' | 'dark' | 'system';\n    notifications: {\n      email: boolean;\n      push: boolean;\n      marketing: boolean;\n    };\n    publishing: {\n      autoPublish: boolean;\n      defaultCategory: string;\n      targetAudience: string;\n    };\n  };\n  privacy_settings: {\n    data_sharing: boolean;\n    analytics: boolean;\n  };\n  \n  // Analytics and performance metrics\n  total_books_generated: number;\n  total_books_published: number;\n  total_revenue: number;\n  avg_quality_score: number;\n  \n  // Security and compliance\n  last_password_change: string;\n  two_factor_enabled: boolean;\n  data_retention_consent: boolean;\n  marketing_consent: boolean;\n  \n  // Timestamps\n  created_at: string;\n  updated_at: string;\n  last_login?: string;\n}\n\n// Legacy interface for backward compatibility\nexport interface UserPreferences {\n  theme: 'light' | 'dark' | 'system'\n  notifications: {\n    email: boolean\n    push: boolean\n    marketing: boolean\n  }\n  publishing: {\n    autoPublish: boolean\n    defaultCategory: string\n    targetAudience: string\n  }\n}\n\nexport interface LoginRequest {\n  email: string\n  password: string\n}\n\nexport interface RegisterRequest {\n  name: string\n  email: string\n  password: string\n}\n\nexport interface UpdateProfileRequest {\n  name?: string\n  avatar_url?: string\n}\n\nexport interface ChangePasswordRequest {\n  currentPassword: string\n  newPassword: string\n}\n\n// Helper function to transform database user to legacy format\nfunction transformUserForLegacyComponents(dbUser: User): TransformedUser {\n  return {\n    ...dbUser,\n    name: dbUser.full_name, // Map full_name to name for backward compatibility\n    role: 'user' as const, // Default role\n    preferences: {\n      theme: dbUser.content_preferences?.theme || 'system',\n      notifications: dbUser.content_preferences?.notifications || {\n        email: true,\n        push: false,\n        marketing: false,\n      },\n      publishing: dbUser.content_preferences?.publishing || {\n        autoPublish: false,\n        defaultCategory: 'general',\n        targetAudience: 'adults',\n      },\n    },\n  };\n}\n\n// Type for the transformed user that includes legacy fields\nexport type TransformedUser = User & { \n  name: string; \n  role: 'user' | 'admin'; \n  preferences: UserPreferences;\n};\n\n// Hooks\nexport function useUser(): {\n  data: TransformedUser | undefined;\n  isLoading: boolean;\n  error: any;\n  refetch: () => Promise<any>;\n} {\n  const { data: session, status } = useSession();\n\n  // Query to fetch real user data from backend\n  const userQuery = useQuery({\n    queryKey: queryKeys.auth.user(),\n    queryFn: async () => {\n      const response = await apiClient.get<User>('/api/auth/me');\n      return response;\n    },\n    enabled: status === 'authenticated' && !!session?.user,\n    staleTime: 5 * 60 * 1000, // Cache for 5 minutes\n    retry: (failureCount, error: any) => {\n      // Don't retry on 401/403 errors\n      if (error?.status === 401 || error?.status === 403) {\n        return false;\n      }\n      return failureCount < 3;\n    },\n  });\n\n  // Fallback to session data if backend query fails or is loading\n  const fallbackUser = session?.user ? {\n    id: session.user.id || \"71e41718-8f74-443b-a68a-f140977fc84a\",\n    email: session.user.email || \"\",\n    full_name: session.user.name || \"\",\n    avatar_url: session.user.image || undefined,\n    subscription_tier: \"free\" as const,\n    preferred_ai_provider: \"openai\",\n    default_writing_style: \"professional\",\n    default_target_audience: \"general adults\",\n    kdp_settings: {},\n    notification_preferences: { email: true, in_app: true },\n    content_preferences: {\n      theme: \"system\" as const,\n      notifications: { email: true, push: false, marketing: false },\n      publishing: { autoPublish: false, defaultCategory: \"general\", targetAudience: \"adults\" },\n    },\n    privacy_settings: { data_sharing: false, analytics: true },\n    total_books_generated: 0,\n    total_books_published: 0,\n    total_revenue: 0,\n    avg_quality_score: 0,\n    last_password_change: new Date().toISOString(),\n    two_factor_enabled: false,\n    data_retention_consent: true,\n    marketing_consent: false,\n    created_at: \"2024-01-01T00:00:00Z\",\n    updated_at: new Date().toISOString(),\n  } as User : undefined;\n\n  // Use real data if available, otherwise fallback\n  const userData = userQuery.data || fallbackUser;\n  const transformedUser = userData ? transformUserForLegacyComponents(userData) : undefined;\n\n  return {\n    data: transformedUser,\n    isLoading: status === \"loading\" || (status === 'authenticated' && userQuery.isLoading),\n    error: status === \"unauthenticated\" ? new Error(\"Not authenticated\") : userQuery.error,\n    refetch: userQuery.refetch,\n  };\n}\n\nexport function useLogin() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: async (credentials: LoginRequest) => {\n      const result = await signIn('credentials', {\n        ...credentials,\n        redirect: false,\n      })\n      \n      if (result?.error) {\n        throw new Error(result.error)\n      }\n      \n      return result\n    },\n    onSuccess: () => {\n      // Invalidate auth queries to fetch fresh user data\n      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() })\n      toast.success('Logged in successfully!')\n    },\n    onError: (error: Error) => {\n      toast.error(error.message || 'Login failed')\n    },\n  })\n}\n\nexport function useRegister() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: (data: RegisterRequest) => apiClient.post<User>('/api/auth/register', data),\n    onSuccess: async (user, variables) => {\n      // Auto-login after successful registration\n      const result = await signIn('credentials', {\n        email: variables.email,\n        password: variables.password,\n        redirect: false,\n      })\n      \n      if (!result?.error) {\n        queryClient.setQueryData(queryKeys.auth.user(), user)\n        toast.success('Account created successfully!')\n      }\n    },\n    onError: (error: any) => {\n      toast.error(error.message || 'Registration failed')\n    },\n  })\n}\n\nexport function useLogout() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: async () => {\n      await signOut({ redirect: false })\n      apiClient.removeAuthToken()\n    },\n    onSuccess: () => {\n      // Clear all cached data\n      queryClient.clear()\n      toast.success('Logged out successfully!')\n    },\n  })\n}\n\nexport function useUpdateProfile() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: (data: UpdateProfileRequest) => \n      apiClient.patch<User>('/api/auth/profile', data),\n    onMutate: async (data) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: queryKeys.auth.user() })\n      \n      // Snapshot the previous value\n      const previousUser = queryClient.getQueryData(queryKeys.auth.user())\n      \n      // Optimistically update\n      queryClient.setQueryData(queryKeys.auth.user(), (old: User) => ({\n        ...old,\n        ...data,\n        updated_at: new Date().toISOString(),\n      }))\n      \n      return { previousUser }\n    },\n    onError: (err, data, context) => {\n      // Revert the optimistic update\n      if (context?.previousUser) {\n        queryClient.setQueryData(queryKeys.auth.user(), context.previousUser)\n      }\n      toast.error('Failed to update profile')\n    },\n    onSuccess: () => {\n      toast.success('Profile updated successfully!')\n    },\n    onSettled: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() })\n    },\n  })\n}\n\nexport function useChangePassword() {\n  return useMutation({\n    mutationFn: (data: ChangePasswordRequest) => \n      apiClient.post('/api/auth/change-password', data),\n    onSuccess: () => {\n      toast.success('Password changed successfully!')\n    },\n    onError: (error: any) => {\n      toast.error(error.message || 'Failed to change password')\n    },\n  })\n}\n\nexport function useUpdatePreferences() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: (preferences: Partial<UserPreferences>) =>\n      apiClient.patch<User>('/api/auth/preferences', preferences),\n    onMutate: async (preferences) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: queryKeys.auth.user() })\n      \n      // Snapshot the previous value\n      const previousUser = queryClient.getQueryData(queryKeys.auth.user())\n      \n      // Optimistically update the content_preferences in the database format\n      queryClient.setQueryData(queryKeys.auth.user(), (old: User | undefined) => {\n        if (!old) return old;\n        \n        return {\n          ...old,\n          content_preferences: {\n            ...old.content_preferences,\n            theme: preferences.theme ?? old.content_preferences.theme,\n            notifications: preferences.notifications ? {\n              ...old.content_preferences.notifications,\n              ...preferences.notifications,\n            } : old.content_preferences.notifications,\n            publishing: preferences.publishing ? {\n              ...old.content_preferences.publishing,\n              ...preferences.publishing,\n            } : old.content_preferences.publishing,\n          },\n          updated_at: new Date().toISOString(),\n        };\n      })\n      \n      return { previousUser }\n    },\n    onError: (err, preferences, context) => {\n      // Revert the optimistic update\n      if (context?.previousUser) {\n        queryClient.setQueryData(queryKeys.auth.user(), context.previousUser)\n      }\n      toast.error('Failed to update preferences')\n    },\n    onSuccess: () => {\n      toast.success('Preferences updated successfully!')\n    },\n    onSettled: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() })\n    },\n  })\n}\n\nexport function useForgotPassword() {\n  return useMutation({\n    mutationFn: (email: string) => \n      apiClient.post('/api/auth/forgot-password', { email }),\n    onSuccess: () => {\n      toast.success('Password reset email sent!')\n    },\n    onError: (error: any) => {\n      toast.error(error.message || 'Failed to send reset email')\n    },\n  })\n}\n\nexport function useResetPassword() {\n  return useMutation({\n    mutationFn: ({ token, password }: { token: string; password: string }) =>\n      apiClient.post('/api/auth/reset-password', { token, password }),\n    onSuccess: () => {\n      toast.success('Password reset successfully!')\n    },\n    onError: (error: any) => {\n      toast.error(error.message || 'Failed to reset password')\n    },\n  })\n}\n\nexport function useVerifyEmail() {\n  return useMutation({\n    mutationFn: (token: string) => \n      apiClient.post('/api/auth/verify-email', { token }),\n    onSuccess: () => {\n      toast.success('Email verified successfully!')\n    },\n    onError: (error: any) => {\n      toast.error(error.message || 'Email verification failed')\n    },\n  })\n}\n\nexport function useResendVerification() {\n  return useMutation({\n    mutationFn: () => apiClient.post('/api/auth/resend-verification'),\n    onSuccess: () => {\n      toast.success('Verification email sent!')\n    },\n    onError: (error: any) => {\n      toast.error(error.message || 'Failed to send verification email')\n    },\n  })\n}\n\n// OAuth providers\nexport function useOAuthLogin() {\n  const loginWithProvider = (provider: string) => \n    signIn(provider, { callbackUrl: '/dashboard' })\n\n  return {\n    loginWithProvider,\n    loginWithGoogle: () => loginWithProvider('google'),\n    loginWithGithub: () => loginWithProvider('github'),\n    loginWithFacebook: () => loginWithProvider('facebook'),\n    loginWithTwitter: () => loginWithProvider('twitter'),\n    loginWithDiscord: () => loginWithProvider('discord'),\n    loginWithLinkedIn: () => loginWithProvider('linkedin'),\n    loginWithSpotify: () => loginWithProvider('spotify'),\n    loginWithSlack: () => loginWithProvider('slack'),\n    loginWithMicrosoft: () => loginWithProvider('azure-ad'),\n    loginWithApple: () => loginWithProvider('apple'),\n  }\n}\n\n// SSO login\nexport function useSSOLogin() {\n  return useMutation({\n    mutationFn: async (data: { domain?: string; providerId?: string }) => {\n      const response = await apiClient.post<{ url: string }>('/api/auth/sso/signin', {\n        domain: data.domain,\n        provider_id: data.providerId,\n      })\n      \n      if (response.url) {\n        window.location.href = response.url\n      }\n      \n      return response\n    },\n    onError: (error: any) => {\n      toast.error(error.message || 'SSO sign-in failed')\n    },\n  })\n}\n\n// Session management\nexport function useSessionStatus() {\n  const { status } = useSession()\n  \n  return {\n    isLoading: status === 'loading',\n    isAuthenticated: status === 'authenticated',\n    isUnauthenticated: status === 'unauthenticated',\n  }\n}\n\n// User permissions\nexport function useUserPermissions() {\n  const { data: user } = useUser()\n  \n  return {\n    isAdmin: user?.role === 'admin',\n    canCreateBooks: !!user,\n    canPublishBooks: !!user,\n    canViewAnalytics: !!user,\n    canManageSettings: !!user,\n  }\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAgGA,8DAA8D;AAC9D,SAAS,iCAAiC,MAAY;IACpD,OAAO;QACL,GAAG,MAAM;QACT,MAAM,OAAO,SAAS;QACtB,MAAM;QACN,aAAa;YACX,OAAO,OAAO,mBAAmB,EAAE,SAAS;YAC5C,eAAe,OAAO,mBAAmB,EAAE,iBAAiB;gBAC1D,OAAO;gBACP,MAAM;gBACN,WAAW;YACb;YACA,YAAY,OAAO,mBAAmB,EAAE,cAAc;gBACpD,aAAa;gBACb,iBAAiB;gBACjB,gBAAgB;YAClB;QACF;IACF;AACF;AAUO,SAAS;;IAMd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAE3C,6CAA6C;IAC7C,MAAM,YAAY,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACzB,UAAU,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;QAC7B,OAAO;2CAAE;gBACP,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAO;gBAC3C,OAAO;YACT;;QACA,SAAS,WAAW,mBAAmB,CAAC,CAAC,SAAS;QAClD,WAAW,IAAI,KAAK;QACpB,KAAK;2CAAE,CAAC,cAAc;gBACpB,gCAAgC;gBAChC,IAAI,OAAO,WAAW,OAAO,OAAO,WAAW,KAAK;oBAClD,OAAO;gBACT;gBACA,OAAO,eAAe;YACxB;;IACF;IAEA,gEAAgE;IAChE,MAAM,eAAe,SAAS,OAAO;QACnC,IAAI,QAAQ,IAAI,CAAC,EAAE,IAAI;QACvB,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;QAC7B,WAAW,QAAQ,IAAI,CAAC,IAAI,IAAI;QAChC,YAAY,QAAQ,IAAI,CAAC,KAAK,IAAI;QAClC,mBAAmB;QACnB,uBAAuB;QACvB,uBAAuB;QACvB,yBAAyB;QACzB,cAAc,CAAC;QACf,0BAA0B;YAAE,OAAO;YAAM,QAAQ;QAAK;QACtD,qBAAqB;YACnB,OAAO;YACP,eAAe;gBAAE,OAAO;gBAAM,MAAM;gBAAO,WAAW;YAAM;YAC5D,YAAY;gBAAE,aAAa;gBAAO,iBAAiB;gBAAW,gBAAgB;YAAS;QACzF;QACA,kBAAkB;YAAE,cAAc;YAAO,WAAW;QAAK;QACzD,uBAAuB;QACvB,uBAAuB;QACvB,eAAe;QACf,mBAAmB;QACnB,sBAAsB,IAAI,OAAO,WAAW;QAC5C,oBAAoB;QACpB,wBAAwB;QACxB,mBAAmB;QACnB,YAAY;QACZ,YAAY,IAAI,OAAO,WAAW;IACpC,IAAY;IAEZ,iDAAiD;IACjD,MAAM,WAAW,UAAU,IAAI,IAAI;IACnC,MAAM,kBAAkB,WAAW,iCAAiC,YAAY;IAEhF,OAAO;QACL,MAAM;QACN,WAAW,WAAW,aAAc,WAAW,mBAAmB,UAAU,SAAS;QACrF,OAAO,WAAW,oBAAoB,IAAI,MAAM,uBAAuB,UAAU,KAAK;QACtF,SAAS,UAAU,OAAO;IAC5B;AACF;GAlEgB;;QAMoB,iJAAA,CAAA,aAAU;QAG1B,8KAAA,CAAA,WAAQ;;;AA2DrB,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;oCAAE,OAAO;gBACjB,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;oBACzC,GAAG,WAAW;oBACd,UAAU;gBACZ;gBAEA,IAAI,QAAQ,OAAO;oBACjB,MAAM,IAAI,MAAM,OAAO,KAAK;gBAC9B;gBAEA,OAAO;YACT;;QACA,SAAS;oCAAE;gBACT,mDAAmD;gBACnD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;gBAAG;gBAChE,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;oCAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAzBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAwBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;uCAAE,CAAC,OAA0B,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAO,sBAAsB;;QAClF,SAAS;uCAAE,OAAO,MAAM;gBACtB,2CAA2C;gBAC3C,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;oBACzC,OAAO,UAAU,KAAK;oBACtB,UAAU,UAAU,QAAQ;oBAC5B,UAAU;gBACZ;gBAEA,IAAI,CAAC,QAAQ,OAAO;oBAClB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,IAAI;oBAChD,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF;;QACA,OAAO;uCAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAtBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAqBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qCAAE;gBACV,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;oBAAE,UAAU;gBAAM;gBAChC,oHAAA,CAAA,YAAS,CAAC,eAAe;YAC3B;;QACA,SAAS;qCAAE;gBACT,wBAAwB;gBACxB,YAAY,KAAK;gBACjB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,OACX,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAO,qBAAqB;;QAC7C,QAAQ;4CAAE,OAAO;gBACf,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;gBAAG;gBAElE,8BAA8B;gBAC9B,MAAM,eAAe,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;gBAEjE,wBAAwB;gBACxB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;oDAAI,CAAC,MAAc,CAAC;4BAC9D,GAAG,GAAG;4BACN,GAAG,IAAI;4BACP,YAAY,IAAI,OAAO,WAAW;wBACpC,CAAC;;gBAED,OAAO;oBAAE;gBAAa;YACxB;;QACA,OAAO;4CAAE,CAAC,KAAK,MAAM;gBACnB,+BAA+B;gBAC/B,IAAI,SAAS,cAAc;oBACzB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,YAAY;gBACtE;gBACA,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;QACA,SAAS;4CAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;gBAAG;YAClE;;IACF;AACF;IApCgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmCb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,OACX,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,6BAA6B;;QAC9C,SAAS;6CAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;6CAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAXgB;;QACP,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;gDAAE,CAAC,cACX,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAO,yBAAyB;;QACjD,QAAQ;gDAAE,OAAO;gBACf,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;gBAAG;gBAElE,8BAA8B;gBAC9B,MAAM,eAAe,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;gBAEjE,uEAAuE;gBACvE,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;wDAAI,CAAC;wBAC/C,IAAI,CAAC,KAAK,OAAO;wBAEjB,OAAO;4BACL,GAAG,GAAG;4BACN,qBAAqB;gCACnB,GAAG,IAAI,mBAAmB;gCAC1B,OAAO,YAAY,KAAK,IAAI,IAAI,mBAAmB,CAAC,KAAK;gCACzD,eAAe,YAAY,aAAa,GAAG;oCACzC,GAAG,IAAI,mBAAmB,CAAC,aAAa;oCACxC,GAAG,YAAY,aAAa;gCAC9B,IAAI,IAAI,mBAAmB,CAAC,aAAa;gCACzC,YAAY,YAAY,UAAU,GAAG;oCACnC,GAAG,IAAI,mBAAmB,CAAC,UAAU;oCACrC,GAAG,YAAY,UAAU;gCAC3B,IAAI,IAAI,mBAAmB,CAAC,UAAU;4BACxC;4BACA,YAAY,IAAI,OAAO,WAAW;wBACpC;oBACF;;gBAEA,OAAO;oBAAE;gBAAa;YACxB;;QACA,OAAO;gDAAE,CAAC,KAAK,aAAa;gBAC1B,+BAA+B;gBAC/B,IAAI,SAAS,cAAc;oBACzB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,YAAY;gBACtE;gBACA,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;QACA,SAAS;gDAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,SAAS;gDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI;gBAAG;YAClE;;IACF;AACF;IAnDgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkDb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,QACX,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,6BAA6B;oBAAE;gBAAM;;QACtD,SAAS;6CAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;6CAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAXgB;;QACP,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAuC,GACnE,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,4BAA4B;oBAAE;oBAAO;gBAAS;;QAC/D,SAAS;4CAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;4CAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAXgB;;QACP,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;0CAAE,CAAC,QACX,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,0BAA0B;oBAAE;gBAAM;;QACnD,SAAS;0CAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;0CAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAXgB;;QACP,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;;QACjC,SAAS;iDAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;iDAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;KAVgB;;QACP,iLAAA,CAAA,cAAW;;;AAYb,SAAS;IACd,MAAM,oBAAoB,CAAC,WACzB,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;YAAE,aAAa;QAAa;IAE/C,OAAO;QACL;QACA,iBAAiB,IAAM,kBAAkB;QACzC,iBAAiB,IAAM,kBAAkB;QACzC,mBAAmB,IAAM,kBAAkB;QAC3C,kBAAkB,IAAM,kBAAkB;QAC1C,kBAAkB,IAAM,kBAAkB;QAC1C,mBAAmB,IAAM,kBAAkB;QAC3C,kBAAkB,IAAM,kBAAkB;QAC1C,gBAAgB,IAAM,kBAAkB;QACxC,oBAAoB,IAAM,kBAAkB;QAC5C,gBAAgB,IAAM,kBAAkB;IAC1C;AACF;AAGO,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;uCAAE,OAAO;gBACjB,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAkB,wBAAwB;oBAC7E,QAAQ,KAAK,MAAM;oBACnB,aAAa,KAAK,UAAU;gBAC9B;gBAEA,IAAI,SAAS,GAAG,EAAE;oBAChB,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAS,GAAG;gBACrC;gBAEA,OAAO;YACT;;QACA,OAAO;uCAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;KAlBgB;;QACP,iLAAA,CAAA,cAAW;;;AAoBb,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAE5B,OAAO;QACL,WAAW,WAAW;QACtB,iBAAiB,WAAW;QAC5B,mBAAmB,WAAW;IAChC;AACF;KARgB;;QACK,iJAAA,CAAA,aAAU;;;AAUxB,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG;IAEvB,OAAO;QACL,SAAS,MAAM,SAAS;QACxB,gBAAgB,CAAC,CAAC;QAClB,iBAAiB,CAAC,CAAC;QACnB,kBAAkB,CAAC,CAAC;QACpB,mBAAmB,CAAC,CAAC;IACvB;AACF;KAVgB;;QACS", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport { useUser } from \"@/hooks/api/use-auth\";\nimport {\n  LayoutDashboard,\n  BookOpen,\n  BarChart3,\n  TrendingUp,\n  Upload,\n  Settings,\n  HelpCircle,\n  X,\n  PlusCircle,\n  FileText,\n  DollarSign,\n} from \"lucide-react\";\n\ninterface SidebarProps {\n  isOpen?: boolean;\n  onClose?: () => void;\n}\n\nconst sidebarItems = [\n  {\n    title: \"Main\",\n    items: [\n      { name: \"Dashboard\", href: \"/dashboard\", icon: LayoutDashboard },\n      { name: \"My Books\", href: \"/dashboard/books\", icon: BookOpen },\n      { name: \"Analytics\", href: \"/dashboard/analytics\", icon: BarChart3 },\n      { name: \"Trends\", href: \"/dashboard/trends\", icon: TrendingUp },\n    ],\n  },\n  {\n    title: \"Publishing\",\n    items: [\n      { name: \"New Book\", href: \"/dashboard/books/new\", icon: PlusCircle },\n      { name: \"Manuscripts\", href: \"/dashboard/manuscripts\", icon: FileText },\n      { name: \"Publications\", href: \"/dashboard/publications\", icon: Upload },\n      { name: \"Revenue\", href: \"/dashboard/revenue\", icon: DollarSign },\n    ],\n  },\n  {\n    title: \"Support\",\n    items: [\n      { name: \"Settings\", href: \"/dashboard/settings\", icon: Settings },\n      { name: \"Help & Docs\", href: \"/dashboard/help\", icon: HelpCircle },\n    ],\n  },\n];\n\nexport function Sidebar({ isOpen = true, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { data: user } = useUser();\n\n  // Get user's subscription tier with fallback to 'free'\n  const subscriptionTier = user?.subscription_tier || \"free\";\n\n  return (\n    <>\n      {/* Mobile backdrop */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black/50 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <aside\n        className={cn(\n          \"fixed inset-y-0 left-0 z-50 w-64 bg-surface transition-transform duration-300 md:relative md:translate-x-0\",\n          isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Mobile header */}\n          <div className=\"flex h-16 items-center justify-between border-b px-4 md:hidden\">\n            <span className=\"font-display text-lg font-semibold\">Menu</span>\n            <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n              <X className=\"h-5 w-5\" />\n              <span className=\"sr-only\">Close sidebar</span>\n            </Button>\n          </div>\n\n          {/* Navigation */}\n          <ScrollArea className=\"flex-1 px-3 py-4\">\n            <nav className=\"space-y-6\">\n              {sidebarItems.map((section) => (\n                <div key={section.title}>\n                  <h3 className=\"mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-muted-foreground\">\n                    {section.title}\n                  </h3>\n                  <div className=\"space-y-1\">\n                    {section.items.map((item) => {\n                      const isActive = pathname === item.href;\n                      const Icon = item.icon;\n                      return (\n                        <Link\n                          key={item.name}\n                          href={item.href}\n                          className={cn(\n                            \"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors\",\n                            isActive\n                              ? \"bg-primary/10 text-primary\"\n                              : \"text-muted-foreground hover:bg-muted hover:text-foreground\"\n                          )}\n                        >\n                          <Icon className=\"h-5 w-5\" />\n                          {item.name}\n                        </Link>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </nav>\n          </ScrollArea>\n\n          {/* Footer - Upgrade prompt */}\n          {subscriptionTier !== \"enterprise\" && (\n            <div className=\"border-t p-4\">\n              <div className=\"rounded-lg bg-primary/10 p-3\">\n                {subscriptionTier === \"free\" ? (\n                  <>\n                    <h4 className=\"text-sm font-semibold\">Upgrade to Pro</h4>\n                    <p className=\"mt-1 text-xs text-muted-foreground\">\n                      Unlock advanced features and analytics\n                    </p>\n                    <Button size=\"sm\" className=\"mt-3 w-full\">\n                      Upgrade Now\n                    </Button>\n                  </>\n                ) : subscriptionTier === \"pro\" ? (\n                  <>\n                    <h4 className=\"text-sm font-semibold\">\n                      Upgrade to Enterprise\n                    </h4>\n                    <p className=\"mt-1 text-xs text-muted-foreground\">\n                      Get premium support and enterprise features\n                    </p>\n                    <Button size=\"sm\" className=\"mt-3 w-full\">\n                      Go Enterprise\n                    </Button>\n                  </>\n                ) : null}\n              </div>\n            </div>\n          )}\n        </div>\n      </aside>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AA2BA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;gBAAc,MAAM,+NAAA,CAAA,kBAAe;YAAC;YAC/D;gBAAE,MAAM;gBAAY,MAAM;gBAAoB,MAAM,iNAAA,CAAA,WAAQ;YAAC;YAC7D;gBAAE,MAAM;gBAAa,MAAM;gBAAwB,MAAM,qNAAA,CAAA,YAAS;YAAC;YACnE;gBAAE,MAAM;gBAAU,MAAM;gBAAqB,MAAM,qNAAA,CAAA,aAAU;YAAC;SAC/D;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAwB,MAAM,qNAAA,CAAA,aAAU;YAAC;YACnE;gBAAE,MAAM;gBAAe,MAAM;gBAA0B,MAAM,iNAAA,CAAA,WAAQ;YAAC;YACtE;gBAAE,MAAM;gBAAgB,MAAM;gBAA2B,MAAM,yMAAA,CAAA,SAAM;YAAC;YACtE;gBAAE,MAAM;gBAAW,MAAM;gBAAsB,MAAM,qNAAA,CAAA,aAAU;YAAC;SACjE;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAuB,MAAM,6MAAA,CAAA,WAAQ;YAAC;YAChE;gBAAE,MAAM;gBAAe,MAAM;gBAAmB,MAAM,iOAAA,CAAA,aAAU;YAAC;SAClE;IACH;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,IAAI,EAAE,OAAO,EAAgB;;IAC9D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAE7B,uDAAuD;IACvD,MAAM,mBAAmB,MAAM,qBAAqB;IAEpD,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8GACA,SAAS,kBAAkB;0BAG7B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS;;sDAC3C,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAK9B,6LAAC,6IAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;oDAClB,MAAM,WAAW,aAAa,KAAK,IAAI;oDACvC,MAAM,OAAO,KAAK,IAAI;oDACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,+BACA;;0EAGN,6LAAC;gEAAK,WAAU;;;;;;4DACf,KAAK,IAAI;;uDAVL,KAAK,IAAI;;;;;gDAapB;;;;;;;uCAvBM,QAAQ,KAAK;;;;;;;;;;;;;;;wBA+B5B,qBAAqB,8BACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,qBAAqB,uBACpB;;sDACE,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAGlD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAc;;;;;;;mDAI1C,qBAAqB,sBACvB;;sDACE,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDAGtC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAGlD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAc;;;;;;;mDAI1C;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GAtGgB;;QACG,qIAAA,CAAA,cAAW;QACL,qIAAA,CAAA,UAAO;;;KAFhB", "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/container.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nexport interface ContainerProps extends HTMLAttributes<HTMLDivElement> {\n  maxWidth?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"full\"\n}\n\nconst Container = forwardRef<HTMLDivElement, ContainerProps>(\n  ({ className, maxWidth = \"2xl\", children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"mx-auto w-full px-4 sm:px-6 lg:px-8\",\n          {\n            \"max-w-screen-sm\": maxWidth === \"sm\",\n            \"max-w-screen-md\": maxWidth === \"md\",\n            \"max-w-screen-lg\": maxWidth === \"lg\",\n            \"max-w-screen-xl\": maxWidth === \"xl\",\n            \"max-w-screen-2xl\": maxWidth === \"2xl\",\n            \"max-w-full\": maxWidth === \"full\",\n          },\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\nContainer.displayName = \"Container\"\n\nexport { Container }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACzB,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uCACA;YACE,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,oBAAoB,aAAa;YACjC,cAAc,aAAa;QAC7B,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEF,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Container } from \"@/components/ui/container\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { BookOpen, Github, Twitter, Linkedin } from \"lucide-react\"\n\nconst footerLinks = {\n  product: [\n    { name: \"Features\", href: \"#features\" },\n    { name: \"Pricing\", href: \"#pricing\" },\n    { name: \"API\", href: \"/docs/api\" },\n    { name: \"Integrations\", href: \"#integrations\" },\n  ],\n  company: [\n    { name: \"About\", href: \"/about\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Careers\", href: \"/careers\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  resources: [\n    { name: \"Documentation\", href: \"/docs\" },\n    { name: \"Guides\", href: \"/guides\" },\n    { name: \"Support\", href: \"/support\" },\n    { name: \"Status\", href: \"/status\" },\n  ],\n  legal: [\n    { name: \"Privacy\", href: \"/privacy\" },\n    { name: \"Terms\", href: \"/terms\" },\n    { name: \"Cookie Policy\", href: \"/cookies\" },\n    { name: \"License\", href: \"/license\" },\n  ],\n}\n\nconst socialLinks = [\n  { name: \"GitHub\", icon: Github, href: \"https://github.com\" },\n  { name: \"Twitter\", icon: Twitter, href: \"https://twitter.com\" },\n  { name: \"LinkedIn\", icon: Linkedin, href: \"https://linkedin.com\" },\n]\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-surface border-t\">\n      <Container>\n        <div className=\"py-12 md:py-16\">\n          {/* Main footer content */}\n          <div className=\"grid grid-cols-2 gap-8 md:grid-cols-5\">\n            {/* Brand section */}\n            <div className=\"col-span-2 md:col-span-1\">\n              <Link href=\"/\" className=\"flex items-center gap-2 mb-4\">\n                <BookOpen className=\"h-6 w-6 text-primary\" />\n                <span className=\"font-display text-lg font-semibold\">\n                  Publish AI\n                </span>\n              </Link>\n              <p className=\"text-sm text-muted-foreground\">\n                AI-powered book publishing platform for modern authors.\n              </p>\n              {/* Social links */}\n              <div className=\"flex gap-4 mt-6\">\n                {socialLinks.map((link) => {\n                  const Icon = link.icon\n                  return (\n                    <a\n                      key={link.name}\n                      href={link.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      <Icon className=\"h-5 w-5\" />\n                      <span className=\"sr-only\">{link.name}</span>\n                    </a>\n                  )\n                })}\n              </div>\n            </div>\n\n            {/* Links sections */}\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          <Separator className=\"my-8\" />\n\n          {/* Bottom section */}\n          <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\n            <p className=\"text-sm text-muted-foreground\">\n              © {new Date().getFullYear()} Publish AI. All rights reserved.\n            </p>\n            <p className=\"text-sm text-muted-foreground\">\n              Made with ❤️ by the Publish AI team\n            </p>\n          </div>\n        </div>\n      </Container>\n    </footer>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAO,MAAM;QAAY;QACjC;YAAE,MAAM;YAAgB,MAAM;QAAgB;KAC/C;IACD,SAAS;QACP;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,WAAW;QACT;YAAE,MAAM;YAAiB,MAAM;QAAQ;QACvC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAU,MAAM;QAAU;KACnC;IACD,OAAO;QACL;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAU,MAAM,yMAAA,CAAA,SAAM;QAAE,MAAM;IAAqB;IAC3D;QAAE,MAAM;QAAW,MAAM,2MAAA,CAAA,UAAO;QAAE,MAAM;IAAsB;IAC9D;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;QAAE,MAAM;IAAuB;CAClE;AAEM,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC,wIAAA,CAAA,YAAS;sBACR,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAIvD,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,OAAO,KAAK,IAAI;4CACtB,qBACE,6LAAC;gDAEC,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAW,KAAK,IAAI;;;;;;;+CAP/B,KAAK,IAAI;;;;;wCAUpB;;;;;;;;;;;;0CAKJ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAa1B,6LAAC,wIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCAGrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAgC;oCACxC,IAAI,OAAO,WAAW;oCAAG;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD;KAvHgB", "debugId": null}}, {"offset": {"line": 1955, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+jBACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2247, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/user-header.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { useSession, signOut } from \"next-auth/react\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Bell, User, LogOut, Settings, Menu } from \"lucide-react\"\nimport { useUser } from \"@/hooks/api/use-auth\"\n\ninterface UserHeaderProps {\n  onMenuClick?: () => void\n}\n\nexport function UserHeader({ onMenuClick }: UserHeaderProps) {\n  const { data: user } = useUser()\n\n  const handleLogout = async () => {\n    try {\n      await signOut({ \n        callbackUrl: \"/auth/login\",\n        redirect: true \n      })\n    } catch (error) {\n      console.error(\"Logout error:\", error)\n    }\n  }\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"flex h-16 items-center justify-between px-6\">\n        {/* Left section - Mobile menu button only */}\n        <div className=\"flex items-center\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={onMenuClick}\n          >\n            <Menu className=\"h-5 w-5\" />\n            <span className=\"sr-only\">Toggle menu</span>\n          </Button>\n        </div>\n\n        {/* Right section - User menu */}\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n            <Bell className=\"h-5 w-5\" />\n            <span className=\"absolute top-0 right-0 h-2 w-2 rounded-full bg-primary\" />\n            <span className=\"sr-only\">Notifications</span>\n          </Button>\n\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                <Avatar className=\"h-8 w-8\">\n                  <AvatarImage src={user?.avatar_url || undefined} alt={user?.name || user?.full_name} />\n                  <AvatarFallback>\n                    {(user?.name || user?.full_name)?.split(' ').map(n => n[0]).join('').toUpperCase() || \"U\"}\n                  </AvatarFallback>\n                </Avatar>\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent className=\"w-56 bg-background border shadow-lg\" align=\"end\" forceMount>\n              <DropdownMenuLabel className=\"font-normal\">\n                <div className=\"flex flex-col space-y-1\">\n                  <p className=\"text-sm font-medium leading-none\">{user?.name || user?.full_name || \"Guest\"}</p>\n                  <p className=\"text-xs leading-none text-muted-foreground\">\n                    {user?.email || \"<EMAIL>\"}\n                  </p>\n                </div>\n              </DropdownMenuLabel>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem asChild>\n                <Link href=\"/dashboard/profile\" className=\"cursor-pointer\">\n                  <User className=\"mr-2 h-4 w-4\" />\n                  <span>Profile</span>\n                </Link>\n              </DropdownMenuItem>\n              <DropdownMenuItem asChild>\n                <Link href=\"/dashboard/settings\" className=\"cursor-pointer\">\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  <span>Settings</span>\n                </Link>\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem \n                className=\"cursor-pointer text-destructive\"\n                onClick={handleLogout}\n              >\n                <LogOut className=\"mr-2 h-4 w-4\" />\n                <span>Log out</span>\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </div>\n    </header>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAfA;;;;;;;;AAqBO,SAAS,WAAW,EAAE,WAAW,EAAmB;;IACzD,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAE7B,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gBACZ,aAAa;gBACb,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;;0CAET,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAK9B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;;8CAC5C,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAG5B,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAK,MAAM,cAAc;oDAAW,KAAK,MAAM,QAAQ,MAAM;;;;;;8DAC1E,6LAAC,qIAAA,CAAA,iBAAc;8DACZ,CAAC,MAAM,QAAQ,MAAM,SAAS,GAAG,MAAM,KAAK,IAAI,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,KAAK,IAAI,iBAAiB;;;;;;;;;;;;;;;;;;;;;;8CAK9F,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAsC,OAAM;oCAAM,UAAU;;sDACzF,6LAAC,+IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC,MAAM,QAAQ,MAAM,aAAa;;;;;;kEAClF,6LAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;kEACxC,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;;kEACzC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,mBAAgB;4CACf,WAAU;4CACV,SAAS;;8DAET,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GArFgB;;QACS,qIAAA,CAAA,UAAO;;;KADhB", "debugId": null}}, {"offset": {"line": 2581, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border shadow-sm hover:shadow-md\",\n        elevated: \"border-border shadow-md hover:shadow-lg\",\n        outlined: \"border-border shadow-none hover:shadow-sm\",\n        ghost: \"border-transparent shadow-none hover:bg-surface-100\",\n        success: \"border-success bg-green-50 shadow-sm\",\n        warning: \"border-warning bg-yellow-50 shadow-sm\",\n        error: \"border-error bg-red-50 shadow-sm\",\n        info: \"border-info bg-blue-50 shadow-sm\",\n      },\n      size: {\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n      hoverable: {\n        true: \"cursor-pointer hover:scale-[1.02] active:scale-[0.98]\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      hoverable: false,\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, size, hoverable, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, size, hoverable }), className)}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-2\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-tight tracking-tight text-accent\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground leading-relaxed\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"pt-4\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center pt-4\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,8EACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;YACP,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,WAAW;YACT,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,WAAW;IACb;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClD,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAM;QAAU,IAAI;QACzD,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;;AAE5D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2718, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/error-boundary.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport { <PERSON>ert<PERSON>riangle } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from \"@/components/ui/card\"\n\ninterface Props {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; reset: () => void }>\n}\n\ninterface State {\n  hasError: boolean\n  error: Error | null\n}\n\nexport class ErrorBoundary extends React.Component<Props, State> {\n  constructor(props: Props) {\n    super(props)\n    this.state = { hasError: false, error: null }\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error(\"ErrorBoundary caught an error:\", error, errorInfo)\n    \n    // Log to monitoring service in production\n    if (process.env.NODE_ENV === 'production') {\n      this.logError(error, errorInfo)\n    }\n  }\n\n  private logError = async (error: Error, errorInfo: React.ErrorInfo) => {\n    try {\n      const errorReport = {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        errorId: crypto.randomUUID(),\n        timestamp: new Date().toISOString(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n      }\n      \n      // Send to backend error reporting endpoint\n      await fetch('/api/errors/report', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(errorReport),\n      })\n    } catch (reportingError) {\n      console.error('Failed to report error:', reportingError)\n    }\n  }\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: null })\n  }\n\n  render() {\n    if (this.state.hasError && this.state.error) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return (\n          <FallbackComponent\n            error={this.state.error}\n            reset={this.handleReset}\n          />\n        )\n      }\n\n      return <DefaultErrorFallback error={this.state.error} reset={this.handleReset} />\n    }\n\n    return this.props.children\n  }\n}\n\ninterface ErrorFallbackProps {\n  error: Error\n  reset: () => void\n}\n\nexport function DefaultErrorFallback({ error, reset }: ErrorFallbackProps) {\n  return (\n    <div className=\"flex items-center justify-center min-h-[400px] p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader>\n          <div className=\"flex items-center gap-2\">\n            <AlertTriangle className=\"h-5 w-5 text-destructive\" />\n            <CardTitle>Something went wrong</CardTitle>\n          </div>\n          <CardDescription>\n            An unexpected error occurred. Please try again.\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <details className=\"rounded-lg bg-muted p-3\">\n            <summary className=\"cursor-pointer text-sm font-medium\">\n              Error details\n            </summary>\n            <pre className=\"mt-2 whitespace-pre-wrap text-xs text-muted-foreground\">\n              {error.message}\n            </pre>\n          </details>\n        </CardContent>\n        <CardFooter className=\"flex gap-2\">\n          <Button onClick={reset}>Try again</Button>\n          <Button\n            variant=\"outline\"\n            onClick={() => window.location.href = \"/dashboard\"}\n          >\n            Go to Dashboard\n          </Button>\n        </CardFooter>\n      </Card>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AA+BQ;;AA7BR;AACA;AACA;AACA;AALA;;;;;;AAiBO,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;YAAO,OAAO;QAAK;IAC9C;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,0CAA0C;QAC1C,uCAA2C;;QAE3C;IACF;IAEQ,WAAW,OAAO,OAAc;QACtC,IAAI;YACF,MAAM,cAAc;gBAClB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;gBAClB,gBAAgB,UAAU,cAAc;gBACxC,SAAS,OAAO,UAAU;gBAC1B,WAAW,IAAI,OAAO,WAAW;gBACjC,KAAK,OAAO,QAAQ,CAAC,IAAI;gBACzB,WAAW,UAAU,SAAS;YAChC;YAEA,2CAA2C;YAC3C,MAAM,MAAM,sBAAsB;gBAChC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;QACF,EAAE,OAAO,gBAAgB;YACvB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF,EAAC;IAED,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAK;IAC/C,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBACE,6LAAC;oBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBACvB,OAAO,IAAI,CAAC,WAAW;;;;;;YAG7B;YAEA,qBAAO,6LAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC,WAAW;;;;;;QAC/E;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAOO,SAAS,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAsB;IACvE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAQ,WAAU;0CAAqC;;;;;;0CAGxD,6LAAC;gCAAI,WAAU;0CACZ,MAAM,OAAO;;;;;;;;;;;;;;;;;8BAIpB,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;sCAAO;;;;;;sCACxB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sCACvC;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAnCgB", "debugId": null}}, {"offset": {"line": 2934, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/contexts/layout-context.tsx"], "sourcesContent": ["\"use client\"\n\nimport { createContext, useContext, useState, ReactNode } from 'react'\n\ninterface LayoutContextType {\n  sidebarOpen: boolean\n  setSidebarOpen: (open: boolean) => void\n  toggleSidebar: () => void\n}\n\nconst LayoutContext = createContext<LayoutContextType | undefined>(undefined)\n\nexport function LayoutProvider({ children }: { children: ReactNode }) {\n  // Default to open on desktop, closed on mobile\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  \n  const toggleSidebar = () => setSidebarOpen(!sidebarOpen)\n  \n  return (\n    <LayoutContext.Provider value={{ sidebarOpen, setSidebarOpen, toggleSidebar }}>\n      {children}\n    </LayoutContext.Provider>\n  )\n}\n\nexport function useLayout() {\n  const context = useContext(LayoutContext)\n  if (context === undefined) {\n    throw new Error('useLayout must be used within a LayoutProvider')\n  }\n  return context\n}"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUA,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;;IAClE,+CAA+C;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,IAAM,eAAe,CAAC;IAE5C,qBACE,6LAAC,cAAc,QAAQ;QAAC,OAAO;YAAE;YAAa;YAAgB;QAAc;kBACzE;;;;;;AAGP;GAXgB;KAAA;AAaT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 2985, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Sidebar } from \"./sidebar\"\nimport { Footer } from \"./footer\"\nimport { UserHeader } from \"./user-header\"\nimport { ErrorBoundary } from \"./error-boundary\"\nimport { LayoutProvider, useLayout } from \"@/contexts/layout-context\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  className?: string\n  showFooter?: boolean\n}\n\nfunction DashboardLayoutInner({ \n  children, \n  className,\n  showFooter = false\n}: DashboardLayoutProps) {\n  const { sidebarOpen, setSidebarOpen } = useLayout()\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <div className=\"flex h-screen\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"flex flex-1 flex-col\">\n          <UserHeader onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className={cn(\n            \"flex-1 overflow-y-auto\",\n            className\n          )}>\n            <ErrorBoundary>\n              {children}\n            </ErrorBoundary>\n          </main>\n        </div>\n      </div>\n      \n      {showFooter && <Footer />}\n    </div>\n  )\n}\n\nexport function DashboardLayout(props: DashboardLayoutProps) {\n  return (\n    <LayoutProvider>\n      <DashboardLayoutInner {...props} />\n    </LayoutProvider>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAeA,SAAS,qBAAqB,EAC5B,QAAQ,EACR,SAAS,EACT,aAAa,KAAK,EACG;;IACrB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD;IAEhD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0IAAA,CAAA,UAAO;wBACN,QAAQ;wBACR,SAAS,IAAM,eAAe;;;;;;kCAGhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,aAAU;gCAAC,aAAa,IAAM,eAAe;;;;;;0CAE9C,6LAAC;gCAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,0BACA;0CAEA,cAAA,6LAAC,oJAAA,CAAA,gBAAa;8CACX;;;;;;;;;;;;;;;;;;;;;;;YAMR,4BAAc,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAG5B;GAhCS;;QAKiC,wIAAA,CAAA,YAAS;;;KAL1C;AAkCF,SAAS,gBAAgB,KAA2B;IACzD,qBACE,6LAAC,wIAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC;YAAsB,GAAG,KAAK;;;;;;;;;;;AAGrC;MANgB", "debugId": null}}, {"offset": {"line": 3103, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/breadcrumb.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { ChevronRight, Home } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BreadcrumbItem {\n  label: string\n  href?: string\n}\n\ninterface BreadcrumbProps {\n  items?: BreadcrumbItem[]\n  className?: string\n}\n\nexport function Breadcrumb({ items, className }: BreadcrumbProps) {\n  const pathname = usePathname()\n  \n  // Auto-generate breadcrumbs from pathname if items not provided\n  const breadcrumbItems = items || generateBreadcrumbs(pathname)\n\n  if (breadcrumbItems.length === 0) return null\n\n  return (\n    <nav \n      aria-label=\"Breadcrumb\"\n      className={cn(\"flex items-center space-x-1 text-sm\", className)}\n    >\n      <Link\n        href=\"/dashboard\"\n        className=\"flex items-center text-muted-foreground hover:text-foreground transition-colors\"\n      >\n        <Home className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Home</span>\n      </Link>\n      \n      {breadcrumbItems.map((item, index) => {\n        const isLast = index === breadcrumbItems.length - 1\n        \n        return (\n          <div key={index} className=\"flex items-center\">\n            <ChevronRight className=\"h-4 w-4 text-muted-foreground mx-1\" />\n            {isLast || !item.href ? (\n              <span className=\"font-medium text-foreground\">\n                {item.label}\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"text-muted-foreground hover:text-foreground transition-colors\"\n              >\n                {item.label}\n              </Link>\n            )}\n          </div>\n        )\n      })}\n    </nav>\n  )\n}\n\nfunction generateBreadcrumbs(pathname: string): BreadcrumbItem[] {\n  const segments = pathname.split(\"/\").filter(Boolean)\n  const breadcrumbs: BreadcrumbItem[] = []\n  \n  // Skip the first segment if it's \"dashboard\"\n  const startIndex = segments[0] === \"dashboard\" ? 1 : 0\n  \n  segments.slice(startIndex).forEach((segment, index) => {\n    const href = \"/\" + segments.slice(0, startIndex + index + 1).join(\"/\")\n    const label = segment\n      .split(\"-\")\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(\" \")\n    \n    breadcrumbs.push({ label, href })\n  })\n  \n  return breadcrumbs\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAiBO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;;IAC9D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,kBAAkB,SAAS,oBAAoB;IAErD,IAAI,gBAAgB,MAAM,KAAK,GAAG,OAAO;IAEzC,qBACE,6LAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;;0BAErD,6LAAC,+JAAA,CAAA,UAAI;gBACH,MAAK;gBACL,WAAU;;kCAEV,6LAAC,sMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;YAG3B,gBAAgB,GAAG,CAAC,CAAC,MAAM;gBAC1B,MAAM,SAAS,UAAU,gBAAgB,MAAM,GAAG;gBAElD,qBACE,6LAAC;oBAAgB,WAAU;;sCACzB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBACvB,UAAU,CAAC,KAAK,IAAI,iBACnB,6LAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;iDAGb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,KAAK;;;;;;;mBAXP;;;;;YAgBd;;;;;;;AAGN;GA5CgB;;QACG,qIAAA,CAAA,cAAW;;;KADd;AA8ChB,SAAS,oBAAoB,QAAgB;IAC3C,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAgC,EAAE;IAExC,6CAA6C;IAC7C,MAAM,aAAa,QAAQ,CAAC,EAAE,KAAK,cAAc,IAAI;IAErD,SAAS,KAAK,CAAC,YAAY,OAAO,CAAC,CAAC,SAAS;QAC3C,MAAM,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,aAAa,QAAQ,GAAG,IAAI,CAAC;QAClE,MAAM,QAAQ,QACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAER,YAAY,IAAI,CAAC;YAAE;YAAO;QAAK;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3228, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/typography.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nconst headingVariants = cva(\n  \"font-semibold text-accent tracking-tight\",\n  {\n    variants: {\n      variant: {\n        h1: \"text-4xl md:text-5xl lg:text-6xl\",\n        h2: \"text-3xl md:text-4xl lg:text-5xl\",\n        h3: \"text-2xl md:text-3xl lg:text-4xl\",\n        h4: \"text-xl md:text-2xl lg:text-3xl\",\n        h5: \"text-lg md:text-xl lg:text-2xl\",\n        h6: \"text-base md:text-lg lg:text-xl\",\n      },\n    },\n    defaultVariants: {\n      variant: \"h1\",\n    },\n  }\n)\n\nconst textVariants = cva(\n  \"\",\n  {\n    variants: {\n      variant: {\n        body: \"text-base text-foreground\",\n        lead: \"text-lg text-muted-foreground\",\n        small: \"text-sm text-foreground\",\n        muted: \"text-sm text-muted-foreground\",\n        caption: \"text-xs text-muted-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"body\",\n    },\n  }\n)\n\nexport interface HeadingProps\n  extends HTMLAttributes<HTMLHeadingElement>,\n    VariantProps<typeof headingVariants> {\n  as?: \"h1\" | \"h2\" | \"h3\" | \"h4\" | \"h5\" | \"h6\"\n}\n\nexport const Heading = forwardRef<HTMLHeadingElement, HeadingProps>(\n  ({ className, variant, as, ...props }, ref) => {\n    const Comp = as || variant || \"h1\"\n    return (\n      <Comp\n        className={cn(headingVariants({ variant: variant || as }), className)}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nHeading.displayName = \"Heading\"\n\nexport interface TextProps\n  extends HTMLAttributes<HTMLParagraphElement>,\n    VariantProps<typeof textVariants> {\n  as?: \"p\" | \"span\" | \"div\"\n}\n\nexport const Text = forwardRef<HTMLParagraphElement, TextProps>(\n  ({ className, variant, as: Comp = \"p\", ...props }, ref) => {\n    return (\n      <Comp\n        className={cn(textVariants({ variant }), className)}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nText.displayName = \"Text\""], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,kBAAkB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACxB,4CACA;IACE,UAAU;QACR,SAAS;YACP,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,IACA;IACE,UAAU;QACR,SAAS;YACP,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AASK,MAAM,wBAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IACrC,MAAM,OAAO,MAAM,WAAW;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;YAAE,SAAS,WAAW;QAAG,IAAI;QAC3D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,QAAQ,WAAW,GAAG;AAQf,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE;IACjD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;QAAQ,IAAI;QACzC,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,KAAK,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3314, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/page-header.tsx"], "sourcesContent": ["import { ReactNode } from \"react\"\nimport { Breadcrumb } from \"./breadcrumb\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { useLayout } from \"@/contexts/layout-context\"\nimport { cn } from \"@/lib/utils\"\nimport { Menu } from \"lucide-react\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  breadcrumb?: boolean\n  actions?: ReactNode\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  breadcrumb = true,\n  actions,\n  className,\n}: PageHeaderProps) {\n  const { toggleSidebar } = useLayout()\n  \n  return (\n    <div className={cn(\"space-y-4 pb-6\", className)}>\n      {breadcrumb && <Breadcrumb />}\n      \n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between\">\n        <div className=\"flex items-center gap-3\">\n          {/* Mobile menu button */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={toggleSidebar}\n          >\n            <Menu className=\"h-5 w-5\" />\n            <span className=\"sr-only\">Toggle menu</span>\n          </Button>\n          \n          <div className=\"space-y-1\">\n            <Heading as=\"h1\" variant=\"h3\">\n              {title}\n            </Heading>\n            {description && (\n              <Text variant=\"muted\" className=\"max-w-2xl\">\n                {description}\n              </Text>\n            )}\n          </div>\n        </div>\n        \n        {actions && (\n          <div className=\"flex items-center gap-2 flex-shrink-0\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAUO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,aAAa,IAAI,EACjB,OAAO,EACP,SAAS,EACO;;IAChB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD;IAElC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAClC,4BAAc,6LAAC,6IAAA,CAAA,aAAU;;;;;0BAE1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yIAAA,CAAA,UAAO;wCAAC,IAAG;wCAAK,SAAQ;kDACtB;;;;;;oCAEF,6BACC,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAQ,WAAU;kDAC7B;;;;;;;;;;;;;;;;;;oBAMR,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;GA9CgB;;QAOY,wIAAA,CAAA,YAAS;;;KAPrB", "debugId": null}}, {"offset": {"line": 3447, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { Loader2 } from \"lucide-react\"\n\nexport interface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n}\n\nconst sizeClasses = {\n  sm: \"h-4 w-4\",\n  md: \"h-6 w-6\",\n  lg: \"h-8 w-8\",\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  return (\n    <Loader2\n      className={cn(\n        \"animate-spin text-primary\",\n        sizeClasses[size],\n        className\n      )}\n    />\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,6LAAC,oNAAA,CAAA,UAAO;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;KAVgB", "debugId": null}}, {"offset": {"line": 3482, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/loading.tsx"], "sourcesContent": ["import { LoadingSpinner } from \"@/components/ui/loading-spinner\"\n\ninterface LoadingProps {\n  fullScreen?: boolean\n  message?: string\n}\n\nexport function Loading({ fullScreen = false, message }: LoadingProps) {\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50\">\n        <div className=\"flex flex-col items-center gap-4\">\n          <LoadingSpinner size=\"lg\" />\n          {message && (\n            <p className=\"text-sm text-muted-foreground animate-pulse\">\n              {message}\n            </p>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center justify-center py-12\">\n      <div className=\"flex flex-col items-center gap-4\">\n        <LoadingSpinner size=\"md\" />\n        {message && (\n          <p className=\"text-sm text-muted-foreground animate-pulse\">\n            {message}\n          </p>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,QAAQ,EAAE,aAAa,KAAK,EAAE,OAAO,EAAgB;IACnE,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,iBAAc;wBAAC,MAAK;;;;;;oBACpB,yBACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;IAMb;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,iJAAA,CAAA,iBAAc;oBAAC,MAAK;;;;;;gBACpB,yBACC,6LAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;;;;;;;AAMb;KA5BgB", "debugId": null}}, {"offset": {"line": 3567, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/auth/protected-route.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession } from \"next-auth/react\"\nimport { useRouter } from \"next/navigation\"\nimport { useEffect } from \"react\"\nimport { Loading } from \"@/components/layout/loading\"\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({ \n  children, \n  redirectTo = \"/auth/login\" \n}: ProtectedRouteProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(redirectTo)\n    }\n  }, [status, router, redirectTo])\n\n  if (status === \"loading\") {\n    return <Loading fullScreen message=\"Checking authentication...\" />\n  }\n\n  if (status === \"unauthenticated\") {\n    return null\n  }\n\n  return <>{children}</>\n}\n\nexport function PublicRoute({ \n  children, \n  redirectTo = \"/dashboard\" \n}: ProtectedRouteProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      router.push(redirectTo)\n    }\n  }, [status, router, redirectTo])\n\n  if (status === \"loading\") {\n    return <Loading fullScreen message=\"Checking authentication...\" />\n  }\n\n  if (status === \"authenticated\") {\n    return null\n  }\n\n  return <>{children}</>\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,eAAe,EAC7B,QAAQ,EACR,aAAa,aAAa,EACN;;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW,mBAAmB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAQ;QAAQ;KAAW;IAE/B,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC,0IAAA,CAAA,UAAO;YAAC,UAAU;YAAC,SAAQ;;;;;;IACrC;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GAtBgB;;QAIoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;KALV;AAwBT,SAAS,YAAY,EAC1B,QAAQ,EACR,aAAa,YAAY,EACL;;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW,iBAAiB;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF;gCAAG;QAAC;QAAQ;QAAQ;KAAW;IAE/B,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC,0IAAA,CAAA,UAAO;YAAC,UAAU;YAAC,SAAQ;;;;;;IACrC;IAEA,IAAI,WAAW,iBAAiB;QAC9B,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;IAtBgB;;QAIoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;MALV", "debugId": null}}, {"offset": {"line": 3673, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/stats-card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { TrendingUp, TrendingDown, Minus } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst statsCardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border hover:shadow-md\",\n        success: \"border-green-200 bg-green-50\",\n        warning: \"border-yellow-200 bg-yellow-50\",\n        error: \"border-red-200 bg-red-50\",\n        info: \"border-blue-200 bg-blue-50\",\n      },\n      size: {\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface StatsCardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof statsCardVariants> {\n  /** Main statistic value */\n  value: string | number\n  /** Label/title for the statistic */\n  label: string\n  /** Optional description */\n  description?: string\n  /** Icon to display */\n  icon?: React.ReactNode\n  /** Trend direction and percentage */\n  trend?: {\n    direction: \"up\" | \"down\" | \"neutral\"\n    value: number\n    label?: string\n  }\n  /** Show loading state */\n  loading?: boolean\n  /** Click handler */\n  onClick?: () => void\n}\n\nconst StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(\n  ({ \n    className, \n    variant, \n    size, \n    value, \n    label, \n    description, \n    icon, \n    trend, \n    loading = false,\n    onClick,\n    ...props \n  }, ref) => {\n    const getTrendIcon = () => {\n      if (!trend) return null\n      \n      switch (trend.direction) {\n        case \"up\":\n          return <TrendingUp className=\"h-4 w-4 text-green-600\" />\n        case \"down\":\n          return <TrendingDown className=\"h-4 w-4 text-red-600\" />\n        case \"neutral\":\n          return <Minus className=\"h-4 w-4 text-muted-foreground\" />\n        default:\n          return null\n      }\n    }\n\n    const getTrendColor = () => {\n      if (!trend) return \"\"\n      \n      switch (trend.direction) {\n        case \"up\":\n          return \"text-green-600\"\n        case \"down\":\n          return \"text-red-600\"\n        case \"neutral\":\n          return \"text-muted-foreground\"\n        default:\n          return \"\"\n      }\n    }\n\n    if (loading) {\n      return (\n        <div\n          ref={ref}\n          className={cn(statsCardVariants({ variant, size }), className)}\n          {...props}\n        >\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"h-4 w-20 bg-muted animate-pulse rounded\" />\n              {icon && <div className=\"h-5 w-5 bg-muted animate-pulse rounded\" />}\n            </div>\n            <div className=\"h-8 w-24 bg-muted animate-pulse rounded\" />\n            {description && <div className=\"h-3 w-32 bg-muted animate-pulse rounded\" />}\n            {trend && <div className=\"h-4 w-16 bg-muted animate-pulse rounded\" />}\n          </div>\n        </div>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          statsCardVariants({ variant, size }),\n          onClick && \"cursor-pointer hover:scale-[1.02] active:scale-[0.98]\",\n          className\n        )}\n        onClick={onClick}\n        {...props}\n      >\n        <div className=\"space-y-3\">\n          {/* Header with label and icon */}\n          <div className=\"flex items-center justify-between\">\n            <p className=\"text-sm font-medium text-muted-foreground\">{label}</p>\n            {icon && (\n              <div className=\"flex items-center justify-center h-8 w-8 rounded-lg bg-primary/10 text-primary\">\n                {icon}\n              </div>\n            )}\n          </div>\n\n          {/* Main value */}\n          <div className=\"space-y-1\">\n            <p className=\"text-2xl font-bold text-foreground\">{value}</p>\n            {description && (\n              <p className=\"text-xs text-muted-foreground\">{description}</p>\n            )}\n          </div>\n\n          {/* Trend indicator */}\n          {trend && (\n            <div className=\"flex items-center gap-1\">\n              {getTrendIcon()}\n              <span className={cn(\"text-sm font-medium\", getTrendColor())}>\n                {trend.value}%\n              </span>\n              {trend.label && (\n                <span className=\"text-xs text-muted-foreground\">\n                  {trend.label}\n                </span>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    )\n  }\n)\nStatsCard.displayName = \"StatsCard\"\n\nexport { StatsCard, statsCardVariants }"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,oBAAoB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC1B,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AA0BF,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC/B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,KAAK,EACL,WAAW,EACX,IAAI,EACJ,KAAK,EACL,UAAU,KAAK,EACf,OAAO,EACP,GAAG,OACJ,EAAE;IACD,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,OAAO;QAEnB,OAAQ,MAAM,SAAS;YACrB,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,OAAO,OAAO;QAEnB,OAAQ,MAAM,SAAS;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;gBAAE;gBAAS;YAAK,IAAI;YACnD,GAAG,KAAK;sBAET,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;4BACd,sBAAQ,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;;;;;;oBACd,6BAAe,6LAAC;wBAAI,WAAU;;;;;;oBAC9B,uBAAS,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kBAAkB;YAAE;YAAS;QAAK,IAClC,WAAW,yDACX;QAEF,SAAS;QACR,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;wBACzD,sBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;8BAMP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAClD,6BACC,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;gBAKjD,uBACC,6LAAC;oBAAI,WAAU;;wBACZ;sCACD,6LAAC;4BAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;;gCACxC,MAAM,KAAK;gCAAC;;;;;;;wBAEd,MAAM,KAAK,kBACV,6LAAC;4BAAK,WAAU;sCACb,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;AAQ5B;;AAEF,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3938, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/quick-actions.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst quickActionsVariants = cva(\n  \"grid gap-4\",\n  {\n    variants: {\n      columns: {\n        1: \"grid-cols-1\",\n        2: \"grid-cols-1 sm:grid-cols-2\",\n        3: \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3\",\n        4: \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-4\",\n        auto: \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n      },\n      size: {\n        sm: \"gap-3\",\n        default: \"gap-4\",\n        lg: \"gap-6\",\n      }\n    },\n    defaultVariants: {\n      columns: \"auto\",\n      size: \"default\",\n    },\n  }\n)\n\nconst quickActionItemVariants = cva(\n  \"group relative flex items-center gap-3 rounded-lg border bg-card p-4 text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border hover:border-primary/20 hover:bg-primary/5\",\n        primary: \"border-primary/20 bg-primary/5 hover:bg-primary/10\",\n        secondary: \"border-secondary/20 bg-secondary/5 hover:bg-secondary/10\",\n        success: \"border-green-200 bg-green-50 hover:bg-green-100\",\n        warning: \"border-yellow-200 bg-yellow-50 hover:bg-yellow-100\",\n        error: \"border-red-200 bg-red-50 hover:bg-red-100\",\n      },\n      size: {\n        sm: \"p-3 text-sm\",\n        default: \"p-4\",\n        lg: \"p-6 text-lg\",\n      },\n      orientation: {\n        horizontal: \"flex-row\",\n        vertical: \"flex-col text-center\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      orientation: \"horizontal\",\n    },\n  }\n)\n\nexport interface QuickActionItem {\n  id: string\n  title: string\n  description?: string\n  icon: React.ReactNode\n  href?: string\n  onClick?: () => void\n  variant?: \"default\" | \"primary\" | \"secondary\" | \"success\" | \"warning\" | \"error\"\n  disabled?: boolean\n  badge?: string | number\n  loading?: boolean\n}\n\nexport interface QuickActionsProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof quickActionsVariants> {\n  /** Quick action items */\n  items: QuickActionItem[]\n  /** Orientation of action items */\n  orientation?: \"horizontal\" | \"vertical\"\n  /** Size of action items */\n  itemSize?: \"sm\" | \"default\" | \"lg\"\n  /** Loading state */\n  loading?: boolean\n  /** Number of skeleton items when loading */\n  skeletonCount?: number\n}\n\nconst QuickActions = React.forwardRef<HTMLDivElement, QuickActionsProps>(\n  ({ \n    className, \n    columns, \n    size, \n    items, \n    orientation = \"horizontal\",\n    itemSize = \"default\",\n    loading = false,\n    skeletonCount = 6,\n    ...props \n  }, ref) => {\n    if (loading) {\n      return (\n        <div \n          ref={ref} \n          className={cn(quickActionsVariants({ columns, size }), className)} \n          {...props}\n        >\n          {Array.from({ length: skeletonCount }).map((_, index) => (\n            <div\n              key={index}\n              className={cn(quickActionItemVariants({ \n                variant: \"default\", \n                size: itemSize, \n                orientation \n              }))}\n            >\n              <div className=\"h-6 w-6 bg-muted animate-pulse rounded\" />\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 w-24 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-32 bg-muted animate-pulse rounded\" />\n              </div>\n            </div>\n          ))}\n        </div>\n      )\n    }\n\n    return (\n      <div \n        ref={ref} \n        className={cn(quickActionsVariants({ columns, size }), className)} \n        {...props}\n      >\n        {items.map((item) => {\n          const Component = item.href ? \"a\" : \"button\"\n          \n          return (\n            <Component\n              key={item.id}\n              className={cn(\n                quickActionItemVariants({ \n                  variant: item.variant || \"default\", \n                  size: itemSize, \n                  orientation \n                }),\n                item.disabled && \"opacity-50 cursor-not-allowed\",\n                !item.disabled && (item.href || item.onClick) && \"cursor-pointer hover:scale-[1.02] active:scale-[0.98]\"\n              )}\n              onClick={item.disabled ? undefined : item.onClick}\n              disabled={item.disabled}\n              {...(item.href ? { href: item.href } : {})}\n            >\n              {/* Icon with loading state */}\n              <div className=\"relative flex-shrink-0\">\n                {item.loading ? (\n                  <div className=\"h-6 w-6 border-2 border-primary border-t-transparent rounded-full animate-spin\" />\n                ) : (\n                  <div className={cn(\n                    \"flex items-center justify-center h-6 w-6\",\n                    orientation === \"vertical\" && \"h-8 w-8\"\n                  )}>\n                    {item.icon}\n                  </div>\n                )}\n                \n                {/* Badge */}\n                {item.badge && !item.loading && (\n                  <span className=\"absolute -top-1 -right-1 flex items-center justify-center h-4 w-4 text-xs font-bold text-white bg-primary rounded-full\">\n                    {item.badge}\n                  </span>\n                )}\n              </div>\n\n              {/* Content */}\n              <div className={cn(\n                \"flex-1 text-left\",\n                orientation === \"vertical\" && \"text-center space-y-1\"\n              )}>\n                <h3 className=\"font-medium text-foreground group-hover:text-primary transition-colors\">\n                  {item.title}\n                </h3>\n                {item.description && (\n                  <p className=\"text-sm text-muted-foreground\">{item.description}</p>\n                )}\n              </div>\n\n              {/* Arrow indicator for links */}\n              {(item.href || item.onClick) && !item.disabled && orientation === \"horizontal\" && (\n                <svg \n                  className=\"h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors\" \n                  fill=\"none\" \n                  viewBox=\"0 0 24 24\" \n                  stroke=\"currentColor\"\n                >\n                  <path \n                    strokeLinecap=\"round\" \n                    strokeLinejoin=\"round\" \n                    strokeWidth={2} \n                    d=\"M9 5l7 7-7 7\" \n                  />\n                </svg>\n              )}\n            </Component>\n          )\n        })}\n      </div>\n    )\n  }\n)\nQuickActions.displayName = \"QuickActions\"\n\nexport { QuickActions, quickActionsVariants, quickActionItemVariants }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAuB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC7B,cACA;IACE,UAAU;QACR,SAAS;YACP,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,0BAA0B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAChC,mJACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,aAAa;YACX,YAAY;YACZ,UAAU;QACZ;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,aAAa;IACf;AACF;AA+BF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAClC,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,cAAc,YAAY,EAC1B,WAAW,SAAS,EACpB,UAAU,KAAK,EACf,gBAAgB,CAAC,EACjB,GAAG,OACJ,EAAE;IACD,IAAI,SAAS;QACX,qBACE,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;gBAAE;gBAAS;YAAK,IAAI;YACtD,GAAG,KAAK;sBAER,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAc,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC7C,6LAAC;oBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;wBACpC,SAAS;wBACT,MAAM;wBACN;oBACF;;sCAEA,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;mBAVZ;;;;;;;;;;IAgBf;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;YAAE;YAAS;QAAK,IAAI;QACtD,GAAG,KAAK;kBAER,MAAM,GAAG,CAAC,CAAC;YACV,MAAM,YAAY,KAAK,IAAI,GAAG,MAAM;YAEpC,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wBAAwB;oBACtB,SAAS,KAAK,OAAO,IAAI;oBACzB,MAAM;oBACN;gBACF,IACA,KAAK,QAAQ,IAAI,iCACjB,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,OAAO,KAAK;gBAEnD,SAAS,KAAK,QAAQ,GAAG,YAAY,KAAK,OAAO;gBACjD,UAAU,KAAK,QAAQ;gBACtB,GAAI,KAAK,IAAI,GAAG;oBAAE,MAAM,KAAK,IAAI;gBAAC,IAAI,CAAC,CAAC;;kCAGzC,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,OAAO,iBACX,6LAAC;gCAAI,WAAU;;;;;qDAEf,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,4CACA,gBAAgB,cAAc;0CAE7B,KAAK,IAAI;;;;;;4BAKb,KAAK,KAAK,IAAI,CAAC,KAAK,OAAO,kBAC1B,6LAAC;gCAAK,WAAU;0CACb,KAAK,KAAK;;;;;;;;;;;;kCAMjB,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oBACA,gBAAgB,cAAc;;0CAE9B,6LAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;4BAEZ,KAAK,WAAW,kBACf,6LAAC;gCAAE,WAAU;0CAAiC,KAAK,WAAW;;;;;;;;;;;;oBAKjE,CAAC,KAAK,IAAI,IAAI,KAAK,OAAO,KAAK,CAAC,KAAK,QAAQ,IAAI,gBAAgB,8BAChE,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,SAAQ;wBACR,QAAO;kCAEP,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;;eA5DH,KAAK,EAAE;;;;;QAkElB;;;;;;AAGN;;AAEF,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4182, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/activity-timeline.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { Check, Clock, AlertCircle, X, Info } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst timelineVariants = cva(\"relative\", {\n  variants: {\n    size: {\n      sm: \"space-y-4\",\n      default: \"space-y-6\",\n      lg: \"space-y-8\",\n    }\n  },\n  defaultVariants: {\n    size: \"default\",\n  },\n})\n\nconst timelineItemVariants = cva(\n  \"relative flex gap-4 pb-6 last:pb-0\",\n  {\n    variants: {\n      variant: {\n        default: \"\",\n        compact: \"gap-3 pb-4\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface TimelineItem {\n  id: string\n  title: string\n  description?: string\n  timestamp: string\n  status: \"completed\" | \"in-progress\" | \"pending\" | \"failed\" | \"info\"\n  icon?: React.ReactNode\n  metadata?: Record<string, any>\n  href?: string\n}\n\nexport interface ActivityTimelineProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof timelineVariants> {\n  /** Timeline items */\n  items: TimelineItem[]\n  /** Show connecting lines */\n  showLines?: boolean\n  /** Compact variant */\n  compact?: boolean\n  /** Loading state */\n  loading?: boolean\n  /** Number of skeleton items to show when loading */\n  skeletonCount?: number\n}\n\nconst ActivityTimeline = React.forwardRef<HTMLDivElement, ActivityTimelineProps>(\n  ({ \n    className, \n    size, \n    items, \n    showLines = true, \n    compact = false,\n    loading = false,\n    skeletonCount = 5,\n    ...props \n  }, ref) => {\n    const getStatusIcon = (status: TimelineItem[\"status\"], customIcon?: React.ReactNode) => {\n      if (customIcon) return customIcon\n      \n      switch (status) {\n        case \"completed\":\n          return <Check className=\"h-4 w-4\" />\n        case \"in-progress\":\n          return <Clock className=\"h-4 w-4\" />\n        case \"failed\":\n          return <X className=\"h-4 w-4\" />\n        case \"info\":\n          return <Info className=\"h-4 w-4\" />\n        case \"pending\":\n        default:\n          return <AlertCircle className=\"h-4 w-4\" />\n      }\n    }\n\n    const getStatusColor = (status: TimelineItem[\"status\"]) => {\n      switch (status) {\n        case \"completed\":\n          return \"bg-green-100 text-green-600 border-green-200\"\n        case \"in-progress\":\n          return \"bg-blue-100 text-blue-600 border-blue-200\"\n        case \"failed\":\n          return \"bg-red-100 text-red-600 border-red-200\"\n        case \"info\":\n          return \"bg-blue-100 text-blue-600 border-blue-200\"\n        case \"pending\":\n        default:\n          return \"bg-muted text-muted-foreground border-border\"\n      }\n    }\n\n    const getLineColor = (status: TimelineItem[\"status\"]) => {\n      switch (status) {\n        case \"completed\":\n          return \"bg-green-200\"\n        case \"in-progress\":\n          return \"bg-blue-200\"\n        case \"failed\":\n          return \"bg-red-200\"\n        case \"info\":\n          return \"bg-blue-200\"\n        case \"pending\":\n        default:\n          return \"bg-border\"\n      }\n    }\n\n    if (loading) {\n      return (\n        <div ref={ref} className={cn(timelineVariants({ size }), className)} {...props}>\n          {Array.from({ length: skeletonCount }).map((_, index) => (\n            <div key={index} className={cn(timelineItemVariants({ variant: compact ? \"compact\" : \"default\" }))}>\n              {/* Icon skeleton */}\n              <div className=\"flex-shrink-0 relative\">\n                <div className=\"h-8 w-8 rounded-full bg-muted animate-pulse\" />\n                {showLines && index < skeletonCount - 1 && (\n                  <div className=\"absolute top-8 left-1/2 w-0.5 h-6 bg-muted animate-pulse transform -translate-x-1/2\" />\n                )}\n              </div>\n              \n              {/* Content skeleton */}\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 w-32 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-48 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-20 bg-muted animate-pulse rounded\" />\n              </div>\n            </div>\n          ))}\n        </div>\n      )\n    }\n\n    return (\n      <div ref={ref} className={cn(timelineVariants({ size }), className)} {...props}>\n        {items.map((item, index) => {\n          const isLast = index === items.length - 1\n          const ItemComponent = item.href ? \"a\" : \"div\"\n          \n          return (\n            <div key={item.id} className={cn(timelineItemVariants({ variant: compact ? \"compact\" : \"default\" }))}>\n              {/* Timeline icon and line */}\n              <div className=\"flex-shrink-0 relative\">\n                <div\n                  className={cn(\n                    \"flex items-center justify-center h-8 w-8 rounded-full border-2\",\n                    getStatusColor(item.status)\n                  )}\n                >\n                  {getStatusIcon(item.status, item.icon)}\n                </div>\n                \n                {/* Connecting line */}\n                {showLines && !isLast && (\n                  <div\n                    className={cn(\n                      \"absolute top-8 left-1/2 w-0.5 h-full transform -translate-x-1/2\",\n                      getLineColor(item.status)\n                    )}\n                  />\n                )}\n              </div>\n\n              {/* Content */}\n              <ItemComponent\n                className={cn(\n                  \"flex-1 space-y-1\",\n                  item.href && \"hover:bg-surface-50 -m-2 p-2 rounded-lg transition-colors\"\n                )}\n                {...(item.href ? { href: item.href } : {})}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"text-sm font-medium text-foreground\">{item.title}</h4>\n                  <time className=\"text-xs text-muted-foreground\">{item.timestamp}</time>\n                </div>\n                \n                {item.description && (\n                  <p className=\"text-sm text-muted-foreground\">{item.description}</p>\n                )}\n                \n                {/* Metadata */}\n                {item.metadata && Object.keys(item.metadata).length > 0 && (\n                  <div className=\"flex flex-wrap gap-2 mt-2\">\n                    {Object.entries(item.metadata).map(([key, value]) => (\n                      <span\n                        key={key}\n                        className=\"inline-flex items-center px-2 py-1 rounded-md text-xs bg-surface-100 text-muted-foreground\"\n                      >\n                        <span className=\"font-medium mr-1\">{key}:</span>\n                        <span>{String(value)}</span>\n                      </span>\n                    ))}\n                  </div>\n                )}\n              </ItemComponent>\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nActivityTimeline.displayName = \"ActivityTimeline\"\n\nexport { ActivityTimeline, timelineVariants, timelineItemVariants }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,mBAAmB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAAE,YAAY;IACvC,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAEA,MAAM,uBAAuB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC7B,sCACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AA6BF,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OACtC,CAAC,EACC,SAAS,EACT,IAAI,EACJ,KAAK,EACL,YAAY,IAAI,EAChB,UAAU,KAAK,EACf,UAAU,KAAK,EACf,gBAAgB,CAAC,EACjB,GAAG,OACJ,EAAE;IACD,MAAM,gBAAgB,CAAC,QAAgC;QACrD,IAAI,YAAY,OAAO;QAEvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;YACtB,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;YACL;gBACE,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;gBAAE;YAAK,IAAI;YAAa,GAAG,KAAK;sBAC3E,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAc,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC7C,6LAAC;oBAAgB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;wBAAE,SAAS,UAAU,YAAY;oBAAU;;sCAE7F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;gCACd,aAAa,QAAQ,gBAAgB,mBACpC,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;mBAbT;;;;;;;;;;IAmBlB;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;YAAE;QAAK,IAAI;QAAa,GAAG,KAAK;kBAC3E,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,SAAS,UAAU,MAAM,MAAM,GAAG;YACxC,MAAM,gBAAgB,KAAK,IAAI,GAAG,MAAM;YAExC,qBACE,6LAAC;gBAAkB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;oBAAE,SAAS,UAAU,YAAY;gBAAU;;kCAE/F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,eAAe,KAAK,MAAM;0CAG3B,cAAc,KAAK,MAAM,EAAE,KAAK,IAAI;;;;;;4BAItC,aAAa,CAAC,wBACb,6LAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mEACA,aAAa,KAAK,MAAM;;;;;;;;;;;;kCAOhC,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oBACA,KAAK,IAAI,IAAI;wBAEd,GAAI,KAAK,IAAI,GAAG;4BAAE,MAAM,KAAK,IAAI;wBAAC,IAAI,CAAC,CAAC;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC,KAAK,KAAK;;;;;;kDAC/D,6LAAC;wCAAK,WAAU;kDAAiC,KAAK,SAAS;;;;;;;;;;;;4BAGhE,KAAK,WAAW,kBACf,6LAAC;gCAAE,WAAU;0CAAiC,KAAK,WAAW;;;;;;4BAI/D,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,QAAQ,EAAE,MAAM,GAAG,mBACpD,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC9C,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;;oDAAoB;oDAAI;;;;;;;0DACxC,6LAAC;0DAAM,OAAO;;;;;;;uCAJT;;;;;;;;;;;;;;;;;eA7CP,KAAK,EAAE;;;;;QAyDrB;;;;;;AAGN;;AAEF,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4525, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center gap-1 rounded-full border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-surface-100 text-accent hover:bg-surface-200\",\n        secondary: \"border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n        info: \"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200\",\n        primary: \"border-transparent bg-primary-100 text-primary-900 hover:bg-primary-200\",\n        outline: \"border-border text-accent hover:bg-surface-50\",\n        ghost: \"border-transparent hover:bg-surface-100 text-accent\",\n      },\n      size: {\n        sm: \"px-2 py-0.5 text-xs\",\n        default: \"px-2.5 py-1 text-sm\",\n        lg: \"px-3 py-1.5 text-base\",\n      },\n      dot: {\n        true: \"\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      dot: false,\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {\n  /** Show a colored dot indicator */\n  dot?: boolean\n  /** Icon to display before the text */\n  icon?: React.ReactNode\n  /** Make the badge clickable */\n  onClick?: () => void\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, dot, icon, onClick, children, ...props }, ref) => {\n    const isClickable = !!onClick\n    \n    const getDotColor = () => {\n      switch (variant) {\n        case 'success': return 'bg-green-500'\n        case 'warning': return 'bg-yellow-500'\n        case 'error': return 'bg-red-500'\n        case 'info': return 'bg-blue-500'\n        case 'primary': return 'bg-primary'\n        case 'secondary': return 'bg-secondary'\n        default: return 'bg-accent'\n      }\n    }\n\n    if (isClickable) {\n      return (\n        <button\n          ref={ref as any}\n          type=\"button\"\n          className={cn(\n            badgeVariants({ variant, size }),\n            \"cursor-pointer hover:scale-105 active:scale-95\",\n            className\n          )}\n          onClick={onClick}\n          {...(props as React.ButtonHTMLAttributes<HTMLButtonElement>)}\n        >\n          {dot && (\n            <span\n              className={cn(\n                \"h-1.5 w-1.5 rounded-full\",\n                getDotColor()\n              )}\n            />\n          )}\n          \n          {icon && (\n            <span className=\"shrink-0\">\n              {icon}\n            </span>\n          )}\n          \n          {children}\n        </button>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...(props as React.HTMLAttributes<HTMLDivElement>)}\n      >\n        {dot && (\n          <span\n            className={cn(\n              \"h-1.5 w-1.5 rounded-full\",\n              getDotColor()\n            )}\n          />\n        )}\n        \n        {icon && (\n          <span className=\"shrink-0\">\n            {icon}\n          </span>\n        )}\n        \n        {children}\n      </div>\n    )\n  }\n)\nBadge.displayName = \"Badge\"\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,iKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,KAAK;YACH,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,KAAK;IACP;AACF;AAcF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrE,MAAM,cAAc,CAAC,CAAC;IAEtB,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YACC,KAAK;YACL,MAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;gBAAE;gBAAS;YAAK,IAC9B,kDACA;YAEF,SAAS;YACR,GAAI,KAAK;;gBAET,qBACC,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;gBAKL,sBACC,6LAAC;oBAAK,WAAU;8BACb;;;;;;gBAIJ;;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAI,KAAK;;YAET,qBACC,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;YAKL,sBACC,6LAAC;gBAAK,WAAU;0BACb;;;;;;YAIJ;;;;;;;AAGP;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4666, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/book-card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { MoreHorizontal, Eye, Edit, Trash2, Calendar, DollarSign } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { Badge } from \"./badge\"\nimport { <PERSON><PERSON> } from \"./button\"\n\nconst bookCardVariants = cva(\n  \"group relative flex flex-col rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border hover:shadow-md hover:border-primary/20\",\n        compact: \"border-border hover:shadow-sm\",\n        featured: \"border-primary/20 bg-primary/5 shadow-md hover:shadow-lg\",\n      },\n      size: {\n        sm: \"p-3\",\n        default: \"p-4\",\n        lg: \"p-6\",\n      },\n      layout: {\n        vertical: \"flex-col\",\n        horizontal: \"flex-row\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      layout: \"vertical\",\n    },\n  }\n)\n\nexport interface BookData {\n  id: string;\n  title: string;\n  description?: string;\n  coverUrl?: string;\n  status:\n    | \"draft\"\n    | \"published\"\n    | \"generating\"\n    | \"failed\"\n    | \"in-review\"\n    | \"archived\";\n  genre?: string;\n  author?: string;\n  publishedDate?: string;\n  lastModified?: string;\n  salesCount?: number;\n  revenue?: number;\n  rating?: number;\n  pages?: number;\n}\n\nexport interface BookCardProps\n  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onSelect'>,\n    VariantProps<typeof bookCardVariants> {\n  /** Book data */\n  book: BookData\n  /** Show actions menu */\n  showActions?: boolean\n  /** Action handlers */\n  onView?: (book: BookData) => void\n  onEdit?: (book: BookData) => void\n  onDelete?: (book: BookData) => void\n  /** Loading state */\n  loading?: boolean\n  /** Selected state */\n  selected?: boolean\n  /** Selection handler */\n  onSelect?: (book: BookData, selected: boolean) => void\n}\n\nconst BookCard = React.forwardRef<HTMLDivElement, BookCardProps>(\n  ({ \n    className, \n    variant, \n    size, \n    layout, \n    book, \n    showActions = true,\n    onView,\n    onEdit,\n    onDelete,\n    loading = false,\n    selected = false,\n    onSelect,\n    ...props \n  }, ref) => {\n    const getStatusVariant = (status: BookData[\"status\"]) => {\n      switch (status) {\n        case \"published\":\n          return \"success\"\n        case \"in-review\":\n          return \"warning\"\n        case \"failed\":\n          return \"error\"\n        case \"archived\":\n          return \"secondary\"\n        case \"draft\":\n        default:\n          return \"default\"\n      }\n    }\n\n    const getStatusText = (status: BookData[\"status\"]) => {\n      switch (status) {\n        case \"published\":\n          return \"Published\"\n        case \"in-review\":\n          return \"In Review\"\n        case \"failed\":\n          return \"Failed\"\n        case \"archived\":\n          return \"Archived\"\n        case \"draft\":\n        default:\n          return \"Draft\"\n      }\n    }\n\n    if (loading) {\n      return (\n        <div \n          ref={ref} \n          className={cn(bookCardVariants({ variant, size, layout }), className)} \n          {...props}\n        >\n          {layout === \"vertical\" ? (\n            <>\n              {/* Cover skeleton */}\n              <div className=\"w-full aspect-[3/4] bg-muted animate-pulse rounded-lg mb-4\" />\n              \n              {/* Content skeleton */}\n              <div className=\"space-y-3 flex-1\">\n                <div className=\"h-4 w-3/4 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-1/2 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-full bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-2/3 bg-muted animate-pulse rounded\" />\n              </div>\n            </>\n          ) : (\n            <div className=\"flex gap-4\">\n              {/* Cover skeleton */}\n              <div className=\"w-20 h-28 bg-muted animate-pulse rounded-lg flex-shrink-0\" />\n              \n              {/* Content skeleton */}\n              <div className=\"space-y-3 flex-1\">\n                <div className=\"h-4 w-3/4 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-1/2 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-full bg-muted animate-pulse rounded\" />\n              </div>\n            </div>\n          )}\n        </div>\n      )\n    }\n\n    return (\n      <div \n        ref={ref} \n        className={cn(\n          bookCardVariants({ variant, size, layout }),\n          selected && \"ring-2 ring-primary ring-offset-2\",\n          onSelect && \"cursor-pointer\",\n          className\n        )} \n        onClick={onSelect ? () => onSelect(book, !selected) : undefined}\n        {...props}\n      >\n        {/* Selection checkbox */}\n        {onSelect && (\n          <div className=\"absolute top-2 left-2 z-10\">\n            <input\n              type=\"checkbox\"\n              checked={selected}\n              onChange={(e) => {\n                e.stopPropagation()\n                onSelect(book, e.target.checked)\n              }}\n              className=\"h-4 w-4 rounded border-border text-primary focus:ring-primary\"\n            />\n          </div>\n        )}\n\n        {/* Actions menu */}\n        {showActions && (\n          <div className=\"absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n              onClick={(e) => {\n                e.stopPropagation()\n                // Handle dropdown menu\n              }}\n            >\n              <MoreHorizontal className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        )}\n\n        {layout === \"vertical\" ? (\n          <>\n            {/* Book cover */}\n            <div className=\"relative mb-4\">\n              {book.coverUrl ? (\n                <>\n                  {/* eslint-disable-next-line @next/next/no-img-element */}\n                  <img\n                    src={book.coverUrl}\n                    alt={`Cover of ${book.title}`}\n                    className=\"w-full aspect-[3/4] object-cover rounded-lg\"\n                  />\n                </>\n              ) : (\n                <div className=\"w-full aspect-[3/4] bg-surface-100 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-muted-foreground text-sm\">No Cover</span>\n                </div>\n              )}\n              \n              {/* Status badge */}\n              <div className=\"absolute top-2 left-2\">\n                <Badge variant={getStatusVariant(book.status)} size=\"sm\">\n                  {getStatusText(book.status)}\n                </Badge>\n              </div>\n            </div>\n\n            {/* Book info */}\n            <div className=\"space-y-3 flex-1\">\n              <div>\n                <h3 className=\"font-semibold text-foreground line-clamp-2 group-hover:text-primary transition-colors\">\n                  {book.title}\n                </h3>\n                {book.author && (\n                  <p className=\"text-sm text-muted-foreground\">{book.author}</p>\n                )}\n              </div>\n\n              {book.description && (\n                <p className=\"text-sm text-muted-foreground line-clamp-2\">\n                  {book.description}\n                </p>\n              )}\n\n              {/* Metadata */}\n              <div className=\"space-y-2\">\n                {book.genre && (\n                  <Badge variant=\"outline\" size=\"sm\">{book.genre}</Badge>\n                )}\n                \n                <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n                  {book.lastModified && (\n                    <span className=\"flex items-center gap-1\">\n                      <Calendar className=\"h-3 w-3\" />\n                      {book.lastModified}\n                    </span>\n                  )}\n                  {book.pages && (\n                    <span>{book.pages} pages</span>\n                  )}\n                </div>\n              </div>\n\n              {/* Stats */}\n              {book.status === \"published\" && (book.salesCount || book.revenue) && (\n                <div className=\"pt-2 border-t border-border space-y-1\">\n                  {book.salesCount !== undefined && (\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-muted-foreground\">Sales</span>\n                      <span className=\"font-medium\">{book.salesCount}</span>\n                    </div>\n                  )}\n                  {book.revenue !== undefined && (\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-muted-foreground\">Revenue</span>\n                      <span className=\"font-medium text-green-600\">${book.revenue}</span>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Action buttons */}\n            {(onView || onEdit || onDelete) && (\n              <div className=\"pt-4 flex gap-2\">\n                {onView && (\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"flex-1\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      onView(book)\n                    }}\n                  >\n                    <Eye className=\"h-4 w-4 mr-1\" />\n                    View\n                  </Button>\n                )}\n                {onEdit && (\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"flex-1\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      onEdit(book)\n                    }}\n                  >\n                    <Edit className=\"h-4 w-4 mr-1\" />\n                    Edit\n                  </Button>\n                )}\n                {onDelete && (\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      onDelete(book)\n                    }}\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </Button>\n                )}\n              </div>\n            )}\n          </>\n        ) : (\n          /* Horizontal layout */\n          <div className=\"flex gap-4\">\n            {/* Book cover */}\n            <div className=\"relative flex-shrink-0\">\n              {book.coverUrl ? (\n                <>\n                  {/* eslint-disable-next-line @next/next/no-img-element */}\n                  <img\n                    src={book.coverUrl}\n                    alt={`Cover of ${book.title}`}\n                    className=\"w-20 h-28 object-cover rounded-lg\"\n                  />\n                </>\n              ) : (\n                <div className=\"w-20 h-28 bg-surface-100 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-muted-foreground text-xs\">No Cover</span>\n                </div>\n              )}\n            </div>\n\n            {/* Book info */}\n            <div className=\"flex-1 space-y-2\">\n              <div className=\"flex items-start justify-between\">\n                <div>\n                  <h3 className=\"font-semibold text-foreground group-hover:text-primary transition-colors\">\n                    {book.title}\n                  </h3>\n                  {book.author && (\n                    <p className=\"text-sm text-muted-foreground\">{book.author}</p>\n                  )}\n                </div>\n                <Badge variant={getStatusVariant(book.status)} size=\"sm\">\n                  {getStatusText(book.status)}\n                </Badge>\n              </div>\n\n              {book.description && (\n                <p className=\"text-sm text-muted-foreground line-clamp-2\">\n                  {book.description}\n                </p>\n              )}\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  {book.genre && (\n                    <Badge variant=\"outline\" size=\"sm\">{book.genre}</Badge>\n                  )}\n                </div>\n                \n                {book.status === \"published\" && book.revenue && (\n                  <div className=\"flex items-center gap-1 text-sm text-green-600\">\n                    <DollarSign className=\"h-4 w-4\" />\n                    {book.revenue}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    )\n  }\n)\nBookCard.displayName = \"BookCard\"\n\nexport { BookCard, bookCardVariants }"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,mBAAmB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACzB,qHACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,UAAU;QACZ;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,QAAQ;YACN,UAAU;YACV,YAAY;QACd;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,QAAQ;IACV;AACF;AA4CF,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,cAAc,IAAI,EAClB,MAAM,EACN,MAAM,EACN,QAAQ,EACR,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;gBAAE;gBAAS;gBAAM;YAAO,IAAI;YAC1D,GAAG,KAAK;sBAER,WAAW,2BACV;;kCAEE,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;6CAInB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iBAAiB;YAAE;YAAS;YAAM;QAAO,IACzC,YAAY,qCACZ,YAAY,kBACZ;QAEF,SAAS,WAAW,IAAM,SAAS,MAAM,CAAC,YAAY;QACrD,GAAG,KAAK;;YAGR,0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,MAAK;oBACL,SAAS;oBACT,UAAU,CAAC;wBACT,EAAE,eAAe;wBACjB,SAAS,MAAM,EAAE,MAAM,CAAC,OAAO;oBACjC;oBACA,WAAU;;;;;;;;;;;YAMf,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;oBACjB,uBAAuB;oBACzB;8BAEA,cAAA,6LAAC,mNAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;;;;;;;;;;;YAK/B,WAAW,2BACV;;kCAEE,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,QAAQ,iBACZ;0CAEE,cAAA,6LAAC;oCACC,KAAK,KAAK,QAAQ;oCAClB,KAAK,CAAC,SAAS,EAAE,KAAK,KAAK,EAAE;oCAC7B,WAAU;;;;;;8DAId,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAKpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAS,iBAAiB,KAAK,MAAM;oCAAG,MAAK;8CACjD,cAAc,KAAK,MAAM;;;;;;;;;;;;;;;;;kCAMhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;oCAEZ,KAAK,MAAM,kBACV,6LAAC;wCAAE,WAAU;kDAAiC,KAAK,MAAM;;;;;;;;;;;;4BAI5D,KAAK,WAAW,kBACf,6LAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;0CAKrB,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,KAAK,kBACT,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,MAAK;kDAAM,KAAK,KAAK;;;;;;kDAGhD,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,YAAY,kBAChB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,KAAK,YAAY;;;;;;;4CAGrB,KAAK,KAAK,kBACT,6LAAC;;oDAAM,KAAK,KAAK;oDAAC;;;;;;;;;;;;;;;;;;;4BAMvB,KAAK,MAAM,KAAK,eAAe,CAAC,KAAK,UAAU,IAAI,KAAK,OAAO,mBAC9D,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,UAAU,KAAK,2BACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDAAK,WAAU;0DAAe,KAAK,UAAU;;;;;;;;;;;;oCAGjD,KAAK,OAAO,KAAK,2BAChB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDAAK,WAAU;;oDAA6B;oDAAE,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;oBAQpE,CAAC,UAAU,UAAU,QAAQ,mBAC5B,6LAAC;wBAAI,WAAU;;4BACZ,wBACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,OAAO;gCACT;;kDAEA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAInC,wBACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,OAAO;gCACT;;kDAEA,6LAAC,8MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAIpC,0BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,SAAS;gCACX;0CAEA,cAAA,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;+BAO5B,qBAAqB,iBACrB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,KAAK,QAAQ,iBACZ;sCAEE,cAAA,6LAAC;gCACC,KAAK,KAAK,QAAQ;gCAClB,KAAK,CAAC,SAAS,EAAE,KAAK,KAAK,EAAE;gCAC7B,WAAU;;;;;;0DAId,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;kCAMtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;4CAEZ,KAAK,MAAM,kBACV,6LAAC;gDAAE,WAAU;0DAAiC,KAAK,MAAM;;;;;;;;;;;;kDAG7D,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAS,iBAAiB,KAAK,MAAM;wCAAG,MAAK;kDACjD,cAAc,KAAK,MAAM;;;;;;;;;;;;4BAI7B,KAAK,WAAW,kBACf,6LAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;0CAIrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK,kBACT,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,MAAK;sDAAM,KAAK,KAAK;;;;;;;;;;;oCAIjD,KAAK,MAAM,KAAK,eAAe,KAAK,OAAO,kBAC1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CACrB,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5356, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/chart-wrapper.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst chartWrapperVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        outlined: \"border-2 border-border\",\n        elevated: \"border-border shadow-md\",\n        flush: \"border-0 shadow-none bg-transparent\",\n      },\n      size: {\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n      height: {\n        sm: \"h-64\",\n        default: \"h-80\",\n        lg: \"h-96\",\n        xl: \"h-[32rem]\",\n        auto: \"h-auto\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      height: \"default\",\n    },\n  }\n)\n\nexport interface ChartWrapperProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof chartWrapperVariants> {\n  /** Chart title */\n  title?: string\n  /** Chart description */\n  description?: string\n  /** Chart content */\n  children: React.ReactNode\n  /** Loading state */\n  loading?: boolean\n  /** Error state */\n  error?: string\n  /** Empty state */\n  empty?: boolean\n  /** Empty state message */\n  emptyMessage?: string\n  /** Header actions */\n  actions?: React.ReactNode\n  /** Footer content */\n  footer?: React.ReactNode\n}\n\nconst ChartWrapper = React.forwardRef<HTMLDivElement, ChartWrapperProps>(\n  ({ \n    className, \n    variant, \n    size, \n    height,\n    title, \n    description, \n    children, \n    loading = false,\n    error,\n    empty = false,\n    emptyMessage = \"No data available\",\n    actions,\n    footer,\n    ...props \n  }, ref) => {\n    const renderContent = () => {\n      if (loading) {\n        return (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"space-y-4 text-center\">\n              <div className=\"h-8 w-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto\" />\n              <p className=\"text-sm text-muted-foreground\">Loading chart data...</p>\n            </div>\n          </div>\n        )\n      }\n\n      if (error) {\n        return (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"space-y-2 text-center\">\n              <div className=\"h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mx-auto\">\n                <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <p className=\"text-sm font-medium text-red-900\">Failed to load chart</p>\n              <p className=\"text-xs text-red-600\">{error}</p>\n            </div>\n          </div>\n        )\n      }\n\n      if (empty) {\n        return (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"space-y-2 text-center\">\n              <div className=\"h-12 w-12 rounded-full bg-surface-100 flex items-center justify-center mx-auto\">\n                <svg className=\"h-6 w-6 text-muted-foreground\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">{emptyMessage}</p>\n            </div>\n          </div>\n        )\n      }\n\n      return children\n    }\n\n    return (\n      <div \n        ref={ref} \n        className={cn(chartWrapperVariants({ variant, size }), className)} \n        {...props}\n      >\n        {/* Header */}\n        {(title || description || actions) && (\n          <div className=\"flex items-start justify-between mb-4\">\n            <div className=\"space-y-1\">\n              {title && (\n                <h3 className=\"text-lg font-semibold text-foreground\">{title}</h3>\n              )}\n              {description && (\n                <p className=\"text-sm text-muted-foreground\">{description}</p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex items-center gap-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Chart content */}\n        <div className={cn(\n          \"relative\",\n          height && chartWrapperVariants({ height })\n        )}>\n          {renderContent()}\n        </div>\n\n        {/* Footer */}\n        {footer && (\n          <div className=\"mt-4 pt-4 border-t border-border\">\n            {footer}\n          </div>\n        )}\n      </div>\n    )\n  }\n)\nChartWrapper.displayName = \"ChartWrapper\"\n\n// Simple chart placeholder component for when no charting library is available\nexport interface ChartPlaceholderProps {\n  type: \"line\" | \"bar\" | \"pie\" | \"area\" | \"scatter\"\n  data?: any[]\n  className?: string\n}\n\nconst ChartPlaceholder = React.forwardRef<HTMLDivElement, ChartPlaceholderProps>(\n  ({ type, data, className }, ref) => {\n    const getChartIcon = () => {\n      switch (type) {\n        case \"line\":\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M3 3v18h18V3H3zm16 16H5V5h14v14z\"/>\n              <path fill=\"currentColor\" d=\"M7 14l2-2 2 2 4-4v2l-4 4-2-2-2 2z\"/>\n            </svg>\n          )\n        case \"bar\":\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M7 17h2v-4H7v4zm4 0h2V7h-2v10zm4 0h2v-7h-2v7z\"/>\n            </svg>\n          )\n        case \"pie\":\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 18c-4.41 0-8-3.59-8-8s3.59-8 8-8v8h8c0 4.41-3.59 8-8 8z\"/>\n            </svg>\n          )\n        case \"area\":\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M3 3v18h18V3H3zm16 16H5V5h14v14z\"/>\n              <path fill=\"currentColor\" d=\"M7 14l2-2 2 2 4-4v6H7v-2z\"/>\n            </svg>\n          )\n        default:\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M3 3v18h18V3H3zm16 16H5V5h14v14z\"/>\n            </svg>\n          )\n      }\n    }\n\n    return (\n      <div \n        ref={ref}\n        className={cn(\n          \"flex flex-col items-center justify-center h-full space-y-2 text-center\",\n          className\n        )}\n      >\n        {getChartIcon()}\n        <p className=\"text-sm font-medium text-muted-foreground capitalize\">{type} Chart</p>\n        {data && (\n          <p className=\"text-xs text-muted-foreground\">{data.length} data points</p>\n        )}\n      </div>\n    )\n  }\n)\nChartPlaceholder.displayName = \"ChartPlaceholder\"\n\nexport { ChartWrapper, ChartPlaceholder, chartWrapperVariants }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAuB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC7B,4DACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,QAAQ;YACN,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,QAAQ;IACV;AACF;AA0BF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAClC,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,MAAM,EACN,KAAK,EACL,WAAW,EACX,QAAQ,EACR,UAAU,KAAK,EACf,KAAK,EACL,QAAQ,KAAK,EACb,eAAe,mBAAmB,EAClC,OAAO,EACP,MAAM,EACN,GAAG,OACJ,EAAE;IACD,MAAM,gBAAgB;QACpB,IAAI,SAAS;YACX,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;QAIrD;QAEA,IAAI,OAAO;YACT,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAuB,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC3E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;sCAChD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;QAI7C;QAEA,IAAI,OAAO;YACT,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAgC,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACpF,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;;;;;;QAItD;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;YAAE;YAAS;QAAK,IAAI;QACtD,GAAG,KAAK;;YAGR,CAAC,SAAS,eAAe,OAAO,mBAC/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,uBACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;4BAExD,6BACC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;oBAGjD,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAOT,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,YACA,UAAU,qBAAqB;oBAAE;gBAAO;0BAEvC;;;;;;YAIF,wBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;;AAEF,aAAa,WAAW,GAAG;AAS3B,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QACtC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE;IAC1B,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;;sCACjC,6LAAC;4BAAK,MAAK;4BAAe,GAAE;;;;;;sCAC5B,6LAAC;4BAAK,MAAK;4BAAe,GAAE;;;;;;;;;;;;YAGlC,KAAK;gBACH,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;8BACjC,cAAA,6LAAC;wBAAK,MAAK;wBAAe,GAAE;;;;;;;;;;;YAGlC,KAAK;gBACH,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;8BACjC,cAAA,6LAAC;wBAAK,MAAK;wBAAe,GAAE;;;;;;;;;;;YAGlC,KAAK;gBACH,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;;sCACjC,6LAAC;4BAAK,MAAK;4BAAe,GAAE;;;;;;sCAC5B,6LAAC;4BAAK,MAAK;4BAAe,GAAE;;;;;;;;;;;;YAGlC;gBACE,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;8BACjC,cAAA,6LAAC;wBAAK,MAAK;wBAAe,GAAE;;;;;;;;;;;QAGpC;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA;;YAGD;0BACD,6LAAC;gBAAE,WAAU;;oBAAwD;oBAAK;;;;;;;YACzE,sBACC,6LAAC;gBAAE,WAAU;;oBAAiC,KAAK,MAAM;oBAAC;;;;;;;;;;;;;AAIlE;;AAEF,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5785, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/hooks/api/use-analytics.ts"], "sourcesContent": ["// Using mock data only - no API calls needed\n// import { useQuery } from '@tanstack/react-query'\n// import { apiClient } from '@/lib/api'\n// import { queryKeys } from '@/lib/react-query'\n\n// Types\nexport interface DateRange {\n  from: string\n  to: string\n}\n\nexport interface AnalyticsOverview {\n  totalRevenue: number\n  totalSales: number\n  activeBooks: number\n  averageRating: number\n  revenueChange: number\n  salesChange: number\n  booksChange: number\n  ratingChange: number\n}\n\nexport interface RevenueAnalytics {\n  totalRevenue: number\n  monthlyRevenue: Array<{\n    month: string\n    revenue: number\n    sales: number\n  }>\n  revenueByCategory: Array<{\n    category: string\n    revenue: number\n    percentage: number\n  }>\n  topPerformers: Array<{\n    id: string\n    title: string\n    revenue: number\n    sales: number\n  }>\n}\n\nexport interface BookAnalytics {\n  totalBooks: number\n  booksByStatus: Array<{\n    status: string\n    count: number\n    percentage: number\n  }>\n  booksByCategory: Array<{\n    category: string\n    count: number\n    averageRevenue: number\n  }>\n  performanceMetrics: {\n    averageWordCount: number\n    averageRating: number\n    successRate: number\n  }\n}\n\nexport interface AudienceAnalytics {\n  totalReaders: number\n  demographics: {\n    ageGroups: Array<{\n      range: string\n      percentage: number\n    }>\n    regions: Array<{\n      country: string\n      percentage: number\n      revenue: number\n    }>\n  }\n  engagement: {\n    averageReadTime: number\n    completionRate: number\n    returnReaderRate: number\n  }\n  preferences: {\n    topCategories: Array<{\n      category: string\n      popularity: number\n    }>\n    readingPatterns: Array<{\n      timeOfDay: string\n      activity: number\n    }>\n  }\n}\n\nexport interface BookPerformance {\n  id: string\n  title: string\n  views: number\n  sales: number\n  revenue: number\n  rating: number\n  conversionRate: number\n  dailyStats: Array<{\n    date: string\n    views: number\n    sales: number\n    revenue: number\n  }>\n}\n\n// Hooks\nexport function useAnalyticsOverview(dateRange?: DateRange) {\n  // Return realistic mock data to avoid API errors during development\n  return {\n    data: {\n      totalRevenue: 3420.75,\n      totalSales: 127,\n      activeBooks: 3,\n      averageRating: 4.2,\n      revenueChange: 15.4,\n      salesChange: 8.2,\n      booksChange: 2,\n      ratingChange: 0.3,\n    } as AnalyticsOverview,\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\nexport function useRevenueAnalytics(dateRange?: DateRange) {\n  // Return mock data to avoid API errors during development\n  return {\n    data: {\n      totalRevenue: 3420.75,\n      monthlyRevenue: [\n        { month: \"Jan 2024\", revenue: 1250.50, sales: 45 },\n        { month: \"Feb 2024\", revenue: 890.25, sales: 32 },\n        { month: \"Mar 2024\", revenue: 1280.00, sales: 50 },\n      ],\n      revenueByCategory: [\n        { category: \"Mystery\", revenue: 1250.50, percentage: 36.5 },\n        { category: \"Technology\", revenue: 1280.00, percentage: 37.4 },\n        { category: \"Cooking\", revenue: 890.25, percentage: 26.1 },\n      ],\n      topPerformers: [\n        { id: \"book-1\", title: \"AI-Generated Mystery Novel\", revenue: 1250.50, sales: 45 },\n        { id: \"book-3\", title: \"The Future of Technology\", revenue: 1280.00, sales: 50 },\n        { id: \"book-2\", title: \"Cooking with AI\", revenue: 890.25, sales: 32 },\n      ],\n    } as RevenueAnalytics,\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\nexport function useBookAnalytics(dateRange?: DateRange) {\n  // Return mock data to avoid API errors during development\n  return {\n    data: {\n      totalBooks: 3,\n      booksByStatus: [\n        { status: \"published\", count: 1, percentage: 33.3 },\n        { status: \"draft\", count: 1, percentage: 33.3 },\n        { status: \"generating\", count: 1, percentage: 33.3 },\n      ],\n      booksByCategory: [\n        { category: \"Mystery\", count: 1, averageRevenue: 1250.50 },\n        { category: \"Technology\", count: 1, averageRevenue: 0 },\n        { category: \"Cooking\", count: 1, averageRevenue: 0 },\n      ],\n      performanceMetrics: {\n        averageWordCount: 39000,\n        averageRating: 4.2,\n        successRate: 66.7,\n      },\n    } as BookAnalytics,\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\nexport function useAudienceAnalytics(dateRange?: DateRange) {\n  // Return mock data to avoid API errors during development\n  return {\n    data: {\n      totalReaders: 127,\n      demographics: {\n        ageGroups: [\n          { range: \"18-24\", percentage: 15.2 },\n          { range: \"25-34\", percentage: 35.8 },\n          { range: \"35-44\", percentage: 28.3 },\n          { range: \"45-54\", percentage: 15.7 },\n          { range: \"55+\", percentage: 5.0 },\n        ],\n        regions: [\n          { country: \"United States\", percentage: 45.0, revenue: 1539.34 },\n          { country: \"United Kingdom\", percentage: 20.0, revenue: 684.15 },\n          { country: \"Canada\", percentage: 15.0, revenue: 513.11 },\n          { country: \"Australia\", percentage: 12.0, revenue: 410.49 },\n          { country: \"Other\", percentage: 8.0, revenue: 273.66 },\n        ],\n      },\n      engagement: {\n        averageReadTime: 45.6,\n        completionRate: 78.5,\n        returnReaderRate: 42.3,\n      },\n      preferences: {\n        topCategories: [\n          { category: \"Mystery\", popularity: 85.2 },\n          { category: \"Technology\", popularity: 72.8 },\n          { category: \"Cooking\", popularity: 68.5 },\n        ],\n        readingPatterns: [\n          { timeOfDay: \"Morning\", activity: 25.3 },\n          { timeOfDay: \"Afternoon\", activity: 35.7 },\n          { timeOfDay: \"Evening\", activity: 39.0 },\n        ],\n      },\n    } as AudienceAnalytics,\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\nexport function useBookPerformance(bookId: string, dateRange?: DateRange) {\n  // Return mock data to avoid API errors during development\n  return {\n    data: {\n      id: bookId,\n      title: \"Sample Book\",\n      views: 2456,\n      sales: 45,\n      revenue: 1250.50,\n      rating: 4.2,\n      conversionRate: 1.8,\n      dailyStats: [\n        { date: \"2024-01-15\", views: 120, sales: 2, revenue: 55.80 },\n        { date: \"2024-01-16\", views: 98, sales: 1, revenue: 27.90 },\n        { date: \"2024-01-17\", views: 156, sales: 3, revenue: 83.70 },\n        { date: \"2024-01-18\", views: 134, sales: 2, revenue: 55.80 },\n        { date: \"2024-01-19\", views: 178, sales: 4, revenue: 111.60 },\n      ],\n    } as BookPerformance,\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\n// Export analytics data\nexport function useExportAnalytics() {\n  return {\n    exportOverview: async (dateRange?: DateRange, format: 'csv' | 'xlsx' = 'csv') => {\n      // Mock export functionality - in real implementation this would download a file\n      console.log('Mock export overview:', { dateRange, format })\n      return { success: true, message: 'Export functionality not available in demo mode' }\n    },\n    \n    exportRevenue: async (dateRange?: DateRange, format: 'csv' | 'xlsx' = 'csv') => {\n      console.log('Mock export revenue:', { dateRange, format })\n      return { success: true, message: 'Export functionality not available in demo mode' }\n    },\n    \n    exportBooks: async (dateRange?: DateRange, format: 'csv' | 'xlsx' = 'csv') => {\n      console.log('Mock export books:', { dateRange, format })\n      return { success: true, message: 'Export functionality not available in demo mode' }\n    },\n    \n    exportAudience: async (dateRange?: DateRange, format: 'csv' | 'xlsx' = 'csv') => {\n      console.log('Mock export audience:', { dateRange, format })\n      return { success: true, message: 'Export functionality not available in demo mode' }\n    },\n  }\n}\n\n// Real-time analytics (using polling)\nexport function useRealTimeAnalytics(enabled = false) {\n  // Return mock real-time data to avoid API errors during development\n  return {\n    data: {\n      currentViews: 23,\n      activeSessions: 8,\n      recentSales: 2,\n      lastUpdate: new Date().toISOString(),\n    },\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\n// Analytics comparisons\nexport function useAnalyticsComparison(\n  dateRange: DateRange,\n  comparisonRange: DateRange\n) {\n  // Return mock comparison data to avoid API errors during development\n  return {\n    data: {\n      current: {\n        revenue: 3420.75,\n        sales: 127,\n        views: 5432,\n      },\n      previous: {\n        revenue: 2967.23,\n        sales: 108,\n        views: 4821,\n      },\n      changes: {\n        revenue: 15.3,\n        sales: 17.6,\n        views: 12.7,\n      },\n    },\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\n// Analytics predictions\nexport function useAnalyticsPredictions(bookId?: string) {\n  // Return mock predictions data to avoid API errors during development\n  return {\n    data: {\n      nextMonth: {\n        predictedRevenue: 4250.80,\n        predictedSales: 156,\n        confidence: 78.5,\n      },\n      trends: [\n        { category: \"Mystery\", growth: 18.5, confidence: 82.3 },\n        { category: \"Technology\", growth: 12.1, confidence: 75.8 },\n        { category: \"Cooking\", growth: 8.7, confidence: 69.2 },\n      ],\n      recommendations: [\n        \"Focus on Mystery genre for highest growth potential\",\n        \"Consider expanding Technology content\",\n        \"Optimize pricing for better conversion rates\",\n      ],\n    },\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,mDAAmD;AACnD,wCAAwC;AACxC,gDAAgD;AAEhD,QAAQ;;;;;;;;;;;;AAuGD,SAAS,qBAAqB,SAAqB;IACxD,oEAAoE;IACpE,OAAO;QACL,MAAM;YACJ,cAAc;YACd,YAAY;YACZ,aAAa;YACb,eAAe;YACf,eAAe;YACf,aAAa;YACb,aAAa;YACb,cAAc;QAChB;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAEO,SAAS,oBAAoB,SAAqB;IACvD,0DAA0D;IAC1D,OAAO;QACL,MAAM;YACJ,cAAc;YACd,gBAAgB;gBACd;oBAAE,OAAO;oBAAY,SAAS;oBAAS,OAAO;gBAAG;gBACjD;oBAAE,OAAO;oBAAY,SAAS;oBAAQ,OAAO;gBAAG;gBAChD;oBAAE,OAAO;oBAAY,SAAS;oBAAS,OAAO;gBAAG;aAClD;YACD,mBAAmB;gBACjB;oBAAE,UAAU;oBAAW,SAAS;oBAAS,YAAY;gBAAK;gBAC1D;oBAAE,UAAU;oBAAc,SAAS;oBAAS,YAAY;gBAAK;gBAC7D;oBAAE,UAAU;oBAAW,SAAS;oBAAQ,YAAY;gBAAK;aAC1D;YACD,eAAe;gBACb;oBAAE,IAAI;oBAAU,OAAO;oBAA8B,SAAS;oBAAS,OAAO;gBAAG;gBACjF;oBAAE,IAAI;oBAAU,OAAO;oBAA4B,SAAS;oBAAS,OAAO;gBAAG;gBAC/E;oBAAE,IAAI;oBAAU,OAAO;oBAAmB,SAAS;oBAAQ,OAAO;gBAAG;aACtE;QACH;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAEO,SAAS,iBAAiB,SAAqB;IACpD,0DAA0D;IAC1D,OAAO;QACL,MAAM;YACJ,YAAY;YACZ,eAAe;gBACb;oBAAE,QAAQ;oBAAa,OAAO;oBAAG,YAAY;gBAAK;gBAClD;oBAAE,QAAQ;oBAAS,OAAO;oBAAG,YAAY;gBAAK;gBAC9C;oBAAE,QAAQ;oBAAc,OAAO;oBAAG,YAAY;gBAAK;aACpD;YACD,iBAAiB;gBACf;oBAAE,UAAU;oBAAW,OAAO;oBAAG,gBAAgB;gBAAQ;gBACzD;oBAAE,UAAU;oBAAc,OAAO;oBAAG,gBAAgB;gBAAE;gBACtD;oBAAE,UAAU;oBAAW,OAAO;oBAAG,gBAAgB;gBAAE;aACpD;YACD,oBAAoB;gBAClB,kBAAkB;gBAClB,eAAe;gBACf,aAAa;YACf;QACF;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAEO,SAAS,qBAAqB,SAAqB;IACxD,0DAA0D;IAC1D,OAAO;QACL,MAAM;YACJ,cAAc;YACd,cAAc;gBACZ,WAAW;oBACT;wBAAE,OAAO;wBAAS,YAAY;oBAAK;oBACnC;wBAAE,OAAO;wBAAS,YAAY;oBAAK;oBACnC;wBAAE,OAAO;wBAAS,YAAY;oBAAK;oBACnC;wBAAE,OAAO;wBAAS,YAAY;oBAAK;oBACnC;wBAAE,OAAO;wBAAO,YAAY;oBAAI;iBACjC;gBACD,SAAS;oBACP;wBAAE,SAAS;wBAAiB,YAAY;wBAAM,SAAS;oBAAQ;oBAC/D;wBAAE,SAAS;wBAAkB,YAAY;wBAAM,SAAS;oBAAO;oBAC/D;wBAAE,SAAS;wBAAU,YAAY;wBAAM,SAAS;oBAAO;oBACvD;wBAAE,SAAS;wBAAa,YAAY;wBAAM,SAAS;oBAAO;oBAC1D;wBAAE,SAAS;wBAAS,YAAY;wBAAK,SAAS;oBAAO;iBACtD;YACH;YACA,YAAY;gBACV,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;YACpB;YACA,aAAa;gBACX,eAAe;oBACb;wBAAE,UAAU;wBAAW,YAAY;oBAAK;oBACxC;wBAAE,UAAU;wBAAc,YAAY;oBAAK;oBAC3C;wBAAE,UAAU;wBAAW,YAAY;oBAAK;iBACzC;gBACD,iBAAiB;oBACf;wBAAE,WAAW;wBAAW,UAAU;oBAAK;oBACvC;wBAAE,WAAW;wBAAa,UAAU;oBAAK;oBACzC;wBAAE,WAAW;wBAAW,UAAU;oBAAK;iBACxC;YACH;QACF;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAEO,SAAS,mBAAmB,MAAc,EAAE,SAAqB;IACtE,0DAA0D;IAC1D,OAAO;QACL,MAAM;YACJ,IAAI;YACJ,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;YACT,QAAQ;YACR,gBAAgB;YAChB,YAAY;gBACV;oBAAE,MAAM;oBAAc,OAAO;oBAAK,OAAO;oBAAG,SAAS;gBAAM;gBAC3D;oBAAE,MAAM;oBAAc,OAAO;oBAAI,OAAO;oBAAG,SAAS;gBAAM;gBAC1D;oBAAE,MAAM;oBAAc,OAAO;oBAAK,OAAO;oBAAG,SAAS;gBAAM;gBAC3D;oBAAE,MAAM;oBAAc,OAAO;oBAAK,OAAO;oBAAG,SAAS;gBAAM;gBAC3D;oBAAE,MAAM;oBAAc,OAAO;oBAAK,OAAO;oBAAG,SAAS;gBAAO;aAC7D;QACH;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAGO,SAAS;IACd,OAAO;QACL,gBAAgB,OAAO,WAAuB,SAAyB,KAAK;YAC1E,gFAAgF;YAChF,QAAQ,GAAG,CAAC,yBAAyB;gBAAE;gBAAW;YAAO;YACzD,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAkD;QACrF;QAEA,eAAe,OAAO,WAAuB,SAAyB,KAAK;YACzE,QAAQ,GAAG,CAAC,wBAAwB;gBAAE;gBAAW;YAAO;YACxD,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAkD;QACrF;QAEA,aAAa,OAAO,WAAuB,SAAyB,KAAK;YACvE,QAAQ,GAAG,CAAC,sBAAsB;gBAAE;gBAAW;YAAO;YACtD,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAkD;QACrF;QAEA,gBAAgB,OAAO,WAAuB,SAAyB,KAAK;YAC1E,QAAQ,GAAG,CAAC,yBAAyB;gBAAE;gBAAW;YAAO;YACzD,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAkD;QACrF;IACF;AACF;AAGO,SAAS,qBAAqB,UAAU,KAAK;IAClD,oEAAoE;IACpE,OAAO;QACL,MAAM;YACJ,cAAc;YACd,gBAAgB;YAChB,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAGO,SAAS,uBACd,SAAoB,EACpB,eAA0B;IAE1B,qEAAqE;IACrE,OAAO;QACL,MAAM;YACJ,SAAS;gBACP,SAAS;gBACT,OAAO;gBACP,OAAO;YACT;YACA,UAAU;gBACR,SAAS;gBACT,OAAO;gBACP,OAAO;YACT;YACA,SAAS;gBACP,SAAS;gBACT,OAAO;gBACP,OAAO;YACT;QACF;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAGO,SAAS,wBAAwB,MAAe;IACrD,sEAAsE;IACtE,OAAO;QACL,MAAM;YACJ,WAAW;gBACT,kBAAkB;gBAClB,gBAAgB;gBAChB,YAAY;YACd;YACA,QAAQ;gBACN;oBAAE,UAAU;oBAAW,QAAQ;oBAAM,YAAY;gBAAK;gBACtD;oBAAE,UAAU;oBAAc,QAAQ;oBAAM,YAAY;gBAAK;gBACzD;oBAAE,UAAU;oBAAW,QAAQ;oBAAK,YAAY;gBAAK;aACtD;YACD,iBAAiB;gBACf;gBACA;gBACA;aACD;QACH;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF", "debugId": null}}, {"offset": {"line": 6210, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/hooks/api/use-books.ts"], "sourcesContent": ["// Using mock data for main hooks, keeping mutations for future use\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\nimport { apiClient } from '@/lib/api'\nimport { queryKeys } from '@/lib/react-query'\nimport { toast } from 'react-hot-toast'\n\n// Types\nexport interface Book {\n  id: string;\n  title: string;\n  description: string;\n  category: string;\n  status:\n    | \"draft\"\n    | \"published\"\n    | \"generating\"\n    | \"failed\"\n    | \"in-review\"\n    | \"archived\";\n  cover_url?: string;\n  created_at: string;\n  updated_at: string;\n  word_count: number;\n  revenue: number;\n  sales: number;\n  rating: number;\n}\n\nexport interface BookFilters {\n  status?: string\n  category?: string\n  search?: string\n  sort?: string\n  limit?: number\n  offset?: number\n}\n\nexport interface CreateBookRequest {\n  title: string\n  description: string\n  category: string\n  niche?: string\n  target_audience?: string\n  keywords?: string[]\n}\n\nexport interface UpdateBookRequest {\n  title?: string\n  description?: string\n  category?: string\n  status?: string\n}\n\n// Hooks\nexport function useBooks(filters?: BookFilters) {\n  // Return mock data to avoid API errors during development\n  const mockBooks: Book[] = [\n    {\n      id: \"book-1\",\n      title: \"AI-Generated Mystery Novel\",\n      description: \"A thrilling mystery novel generated with advanced AI\",\n      category: \"Mystery\",\n      status: \"published\",\n      cover_url: \"/images/mystery-book-cover.jpg\",\n      created_at: \"2024-01-15T10:00:00Z\",\n      updated_at: \"2024-01-20T10:00:00Z\",\n      word_count: 75000,\n      revenue: 1250.50,\n      sales: 45,\n      rating: 4.2,\n    },\n    {\n      id: \"book-2\", \n      title: \"The Future of Technology\",\n      description: \"An insightful guide to emerging technologies\",\n      category: \"Technology\",\n      status: \"draft\",\n      created_at: \"2024-01-10T10:00:00Z\",\n      updated_at: \"2024-01-18T10:00:00Z\",\n      word_count: 42000,\n      revenue: 0,\n      sales: 0,\n      rating: 0,\n    },\n    {\n      id: \"book-3\",\n      title: \"Cooking with AI\",\n      description: \"Innovative recipes created with artificial intelligence\",\n      category: \"Cooking\",\n      status: \"generating\",\n      created_at: \"2024-01-22T10:00:00Z\",\n      updated_at: \"2024-01-22T10:00:00Z\",\n      word_count: 0,\n      revenue: 0,\n      sales: 0,\n      rating: 0,\n    }\n  ];\n\n  // Apply filters if provided\n  let filteredBooks = mockBooks;\n  if (filters?.status) {\n    filteredBooks = filteredBooks.filter(book => book.status === filters.status);\n  }\n  if (filters?.category) {\n    filteredBooks = filteredBooks.filter(book => book.category === filters.category);\n  }\n  if (filters?.search) {\n    const searchLower = filters.search.toLowerCase();\n    filteredBooks = filteredBooks.filter(book => \n      book.title.toLowerCase().includes(searchLower) ||\n      book.description.toLowerCase().includes(searchLower)\n    );\n  }\n\n  return {\n    data: {\n      books: filteredBooks,\n      total: filteredBooks.length,\n    },\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\nexport function useBook(id: string) {\n  // Return mock data for specific book to avoid API errors during development\n  const mockBooks = [\n    {\n      id: \"book-1\",\n      title: \"AI-Generated Mystery Novel\",\n      description: \"A thrilling mystery novel generated with advanced AI\",\n      category: \"Mystery\",\n      status: \"published\" as const,\n      cover_url: \"/images/mystery-book-cover.jpg\",\n      created_at: \"2024-01-15T10:00:00Z\",\n      updated_at: \"2024-01-20T10:00:00Z\",\n      word_count: 75000,\n      revenue: 1250.50,\n      sales: 45,\n      rating: 4.2,\n    },\n    {\n      id: \"book-2\", \n      title: \"The Future of Technology\",\n      description: \"An insightful guide to emerging technologies\",\n      category: \"Technology\",\n      status: \"draft\" as const,\n      created_at: \"2024-01-10T10:00:00Z\",\n      updated_at: \"2024-01-18T10:00:00Z\",\n      word_count: 42000,\n      revenue: 0,\n      sales: 0,\n      rating: 0,\n    },\n    {\n      id: \"book-3\",\n      title: \"Cooking with AI\",\n      description: \"Innovative recipes created with artificial intelligence\",\n      category: \"Cooking\",\n      status: \"generating\" as const,\n      created_at: \"2024-01-22T10:00:00Z\",\n      updated_at: \"2024-01-22T10:00:00Z\",\n      word_count: 0,\n      revenue: 0,\n      sales: 0,\n      rating: 0,\n    }\n  ];\n\n  const book = mockBooks.find(b => b.id === id) || mockBooks[0];\n\n  return {\n    data: book,\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\nexport function useBookAnalytics(id: string) {\n  // Return mock analytics data to avoid API errors during development\n  return {\n    data: {\n      views: 2456,\n      downloads: 127,\n      revenue: 1250.50,\n      rating: 4.2,\n      conversionRate: 5.2,\n      dailyStats: [\n        { date: \"2024-01-15\", views: 120, downloads: 6, revenue: 167.40 },\n        { date: \"2024-01-16\", views: 98, downloads: 4, revenue: 111.60 },\n        { date: \"2024-01-17\", views: 156, downloads: 8, revenue: 223.20 },\n        { date: \"2024-01-18\", views: 134, downloads: 5, revenue: 139.50 },\n        { date: \"2024-01-19\", views: 178, downloads: 9, revenue: 251.10 },\n      ],\n    },\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve(),\n  }\n}\n\nexport function useCreateBook() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: (data: CreateBookRequest) => apiClient.post<Book>('/api/books', data),\n    onMutate: async (newBook) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: queryKeys.books.lists() })\n      \n      // Snapshot the previous value\n      const previousBooks = queryClient.getQueryData(queryKeys.books.lists())\n      \n      // Optimistically update to the new value\n      const optimisticBook: Book = {\n        id: crypto.randomUUID(),\n        ...newBook,\n        status: 'generating' as const,\n        cover_url: undefined,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n        word_count: 0,\n        revenue: 0,\n        sales: 0,\n        rating: 0,\n      }\n      \n      // Add to all book lists\n      queryClient.setQueriesData(\n        { queryKey: queryKeys.books.lists() },\n        (old: any) => {\n          if (old?.books) {\n            return {\n              ...old,\n              books: [optimisticBook, ...old.books],\n              total: old.total + 1,\n            }\n          }\n          return old\n        }\n      )\n      \n      return { previousBooks, optimisticBook }\n    },\n    onError: (err, newBook, context) => {\n      // Revert the optimistic update\n      if (context?.previousBooks) {\n        queryClient.setQueriesData(\n          { queryKey: queryKeys.books.lists() },\n          context.previousBooks\n        )\n      }\n      toast.error('Failed to create book')\n    },\n    onSuccess: (data, variables, context) => {\n      // Replace optimistic book with real data\n      queryClient.setQueriesData(\n        { queryKey: queryKeys.books.lists() },\n        (old: any) => {\n          if (old?.books && context?.optimisticBook) {\n            return {\n              ...old,\n              books: old.books.map((book: Book) =>\n                book.id === context.optimisticBook.id ? data : book\n              ),\n            }\n          }\n          return old\n        }\n      )\n      \n      // Set individual book data\n      queryClient.setQueryData(queryKeys.books.detail(data.id), data)\n      \n      toast.success('Book created successfully!')\n    },\n    onSettled: () => {\n      // Always refetch after error or success\n      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })\n    },\n  })\n}\n\nexport function useUpdateBook() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateBookRequest }) =>\n      apiClient.patch<Book>(`/api/books/${id}`, data),\n    onMutate: async ({ id, data }) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: queryKeys.books.detail(id) })\n      \n      // Snapshot the previous value\n      const previousBook = queryClient.getQueryData(queryKeys.books.detail(id))\n      \n      // Optimistically update\n      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({\n        ...old,\n        ...data,\n        updated_at: new Date().toISOString(),\n      }))\n      \n      // Update in lists as well\n      queryClient.setQueriesData(\n        { queryKey: queryKeys.books.lists() },\n        (old: any) => {\n          if (old?.books) {\n            return {\n              ...old,\n              books: old.books.map((book: Book) =>\n                book.id === id ? { ...book, ...data, updated_at: new Date().toISOString() } : book\n              ),\n            }\n          }\n          return old\n        }\n      )\n      \n      return { previousBook }\n    },\n    onError: (err, { id }, context) => {\n      // Revert the optimistic update\n      if (context?.previousBook) {\n        queryClient.setQueryData(queryKeys.books.detail(id), context.previousBook)\n      }\n      toast.error('Failed to update book')\n    },\n    onSuccess: (data) => {\n      toast.success('Book updated successfully!')\n    },\n    onSettled: (data, error, { id }) => {\n      // Always refetch after error or success\n      queryClient.invalidateQueries({ queryKey: queryKeys.books.detail(id) })\n      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })\n    },\n  })\n}\n\nexport function useDeleteBook() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: (id: string) => apiClient.delete(`/api/books/${id}`),\n    onMutate: async (id) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: queryKeys.books.lists() })\n      \n      // Snapshot the previous value\n      const previousBooks = queryClient.getQueryData(queryKeys.books.lists())\n      \n      // Optimistically remove from lists\n      queryClient.setQueriesData(\n        { queryKey: queryKeys.books.lists() },\n        (old: any) => {\n          if (old?.books) {\n            return {\n              ...old,\n              books: old.books.filter((book: Book) => book.id !== id),\n              total: Math.max(0, old.total - 1),\n            }\n          }\n          return old\n        }\n      )\n      \n      return { previousBooks }\n    },\n    onError: (err, id, context) => {\n      // Revert the optimistic update\n      if (context?.previousBooks) {\n        queryClient.setQueriesData(\n          { queryKey: queryKeys.books.lists() },\n          context.previousBooks\n        )\n      }\n      toast.error('Failed to delete book')\n    },\n    onSuccess: (data, id) => {\n      // Remove individual book data\n      queryClient.removeQueries({ queryKey: queryKeys.books.detail(id) })\n      queryClient.removeQueries({ queryKey: queryKeys.books.analytics(id) })\n      \n      toast.success('Book deleted successfully!')\n    },\n    onSettled: () => {\n      // Always refetch after error or success\n      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })\n    },\n  })\n}\n\nexport function usePublishBook() {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: (id: string) => apiClient.post(`/api/books/${id}/publish`),\n    onMutate: async (id) => {\n      // Optimistically update status\n      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({\n        ...old,\n        status: 'published' as const,\n      }))\n      \n      // Update in lists\n      queryClient.setQueriesData(\n        { queryKey: queryKeys.books.lists() },\n        (old: any) => {\n          if (old?.books) {\n            return {\n              ...old,\n              books: old.books.map((book: Book) =>\n                book.id === id ? { ...book, status: 'published' as const } : book\n              ),\n            }\n          }\n          return old\n        }\n      )\n    },\n    onError: (err, id) => {\n      // Revert status on error\n      queryClient.setQueryData(queryKeys.books.detail(id), (old: Book) => ({\n        ...old,\n        status: 'draft' as const,\n      }))\n      toast.error('Failed to publish book')\n    },\n    onSuccess: () => {\n      toast.success('Book published successfully!')\n    },\n    onSettled: (data, error, id) => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.books.detail(id) })\n      queryClient.invalidateQueries({ queryKey: queryKeys.books.lists() })\n    },\n  })\n}"], "names": [], "mappings": "AAAA,mEAAmE;;;;;;;;;;AACnE;AAAA;AACA;AACA;AACA;;;;;;AAkDO,SAAS,SAAS,OAAqB;IAC5C,0DAA0D;IAC1D,MAAM,YAAoB;QACxB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;YACR,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,SAAS;YACT,OAAO;YACP,QAAQ;QACV;KACD;IAED,4BAA4B;IAC5B,IAAI,gBAAgB;IACpB,IAAI,SAAS,QAAQ;QACnB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;IAC7E;IACA,IAAI,SAAS,UAAU;QACrB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,QAAQ,QAAQ;IACjF;IACA,IAAI,SAAS,QAAQ;QACnB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;QAC9C,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;IAE5C;IAEA,OAAO;QACL,MAAM;YACJ,OAAO;YACP,OAAO,cAAc,MAAM;QAC7B;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAEO,SAAS,QAAQ,EAAU;IAChC,4EAA4E;IAC5E,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;YACR,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,SAAS;YACT,OAAO;YACP,QAAQ;QACV;KACD;IAED,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE;IAE7D,OAAO;QACL,MAAM;QACN,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAEO,SAAS,iBAAiB,EAAU;IACzC,oEAAoE;IACpE,OAAO;QACL,MAAM;YACJ,OAAO;YACP,WAAW;YACX,SAAS;YACT,QAAQ;YACR,gBAAgB;YAChB,YAAY;gBACV;oBAAE,MAAM;oBAAc,OAAO;oBAAK,WAAW;oBAAG,SAAS;gBAAO;gBAChE;oBAAE,MAAM;oBAAc,OAAO;oBAAI,WAAW;oBAAG,SAAS;gBAAO;gBAC/D;oBAAE,MAAM;oBAAc,OAAO;oBAAK,WAAW;oBAAG,SAAS;gBAAO;gBAChE;oBAAE,MAAM;oBAAc,OAAO;oBAAK,WAAW;oBAAG,SAAS;gBAAO;gBAChE;oBAAE,MAAM;oBAAc,OAAO;oBAAK,WAAW;oBAAG,SAAS;gBAAO;aACjE;QACH;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;AACF;AAEO,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,OAA4B,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAO,cAAc;;QAC5E,QAAQ;yCAAE,OAAO;gBACf,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;gBAEpE,8BAA8B;gBAC9B,MAAM,gBAAgB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAEpE,yCAAyC;gBACzC,MAAM,iBAAuB;oBAC3B,IAAI,OAAO,UAAU;oBACrB,GAAG,OAAO;oBACV,QAAQ;oBACR,WAAW;oBACX,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY;oBACZ,SAAS;oBACT,OAAO;oBACP,QAAQ;gBACV;gBAEA,wBAAwB;gBACxB,YAAY,cAAc,CACxB;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;iDACpC,CAAC;wBACC,IAAI,KAAK,OAAO;4BACd,OAAO;gCACL,GAAG,GAAG;gCACN,OAAO;oCAAC;uCAAmB,IAAI,KAAK;iCAAC;gCACrC,OAAO,IAAI,KAAK,GAAG;4BACrB;wBACF;wBACA,OAAO;oBACT;;gBAGF,OAAO;oBAAE;oBAAe;gBAAe;YACzC;;QACA,OAAO;yCAAE,CAAC,KAAK,SAAS;gBACtB,+BAA+B;gBAC/B,IAAI,SAAS,eAAe;oBAC1B,YAAY,cAAc,CACxB;wBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;oBAAG,GACpC,QAAQ,aAAa;gBAEzB;gBACA,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;QACA,SAAS;yCAAE,CAAC,MAAM,WAAW;gBAC3B,yCAAyC;gBACzC,YAAY,cAAc,CACxB;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;iDACpC,CAAC;wBACC,IAAI,KAAK,SAAS,SAAS,gBAAgB;4BACzC,OAAO;gCACL,GAAG,GAAG;gCACN,OAAO,IAAI,KAAK,CAAC,GAAG;iEAAC,CAAC,OACpB,KAAK,EAAE,KAAK,QAAQ,cAAc,CAAC,EAAE,GAAG,OAAO;;4BAEnD;wBACF;wBACA,OAAO;oBACT;;gBAGF,2BAA2B;gBAC3B,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG;gBAE1D,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,SAAS;yCAAE;gBACT,wCAAwC;gBACxC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;YACpE;;IACF;AACF;GAhFgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA+Eb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAO,CAAC,WAAW,EAAE,IAAI,EAAE;;QAC5C,QAAQ;yCAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE;gBAC3B,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC;gBAAI;gBAEvE,8BAA8B;gBAC9B,MAAM,eAAe,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC;gBAErE,wBAAwB;gBACxB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC;iDAAK,CAAC,MAAc,CAAC;4BACnE,GAAG,GAAG;4BACN,GAAG,IAAI;4BACP,YAAY,IAAI,OAAO,WAAW;wBACpC,CAAC;;gBAED,0BAA0B;gBAC1B,YAAY,cAAc,CACxB;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;iDACpC,CAAC;wBACC,IAAI,KAAK,OAAO;4BACd,OAAO;gCACL,GAAG,GAAG;gCACN,OAAO,IAAI,KAAK,CAAC,GAAG;iEAAC,CAAC,OACpB,KAAK,EAAE,KAAK,KAAK;4CAAE,GAAG,IAAI;4CAAE,GAAG,IAAI;4CAAE,YAAY,IAAI,OAAO,WAAW;wCAAG,IAAI;;4BAElF;wBACF;wBACA,OAAO;oBACT;;gBAGF,OAAO;oBAAE;gBAAa;YACxB;;QACA,OAAO;yCAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;gBACrB,+BAA+B;gBAC/B,IAAI,SAAS,cAAc;oBACzB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,QAAQ,YAAY;gBAC3E;gBACA,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;QACA,SAAS;yCAAE,CAAC;gBACV,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,SAAS;yCAAE,CAAC,MAAM,OAAO,EAAE,EAAE,EAAE;gBAC7B,wCAAwC;gBACxC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC;gBAAI;gBACrE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;YACpE;;IACF;AACF;IAtDgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAqDb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,KAAe,oHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;;QAC/D,QAAQ;yCAAE,OAAO;gBACf,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;gBAEpE,8BAA8B;gBAC9B,MAAM,gBAAgB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAEpE,mCAAmC;gBACnC,YAAY,cAAc,CACxB;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;iDACpC,CAAC;wBACC,IAAI,KAAK,OAAO;4BACd,OAAO;gCACL,GAAG,GAAG;gCACN,OAAO,IAAI,KAAK,CAAC,MAAM;iEAAC,CAAC,OAAe,KAAK,EAAE,KAAK;;gCACpD,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG;4BACjC;wBACF;wBACA,OAAO;oBACT;;gBAGF,OAAO;oBAAE;gBAAc;YACzB;;QACA,OAAO;yCAAE,CAAC,KAAK,IAAI;gBACjB,+BAA+B;gBAC/B,IAAI,SAAS,eAAe;oBAC1B,YAAY,cAAc,CACxB;wBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;oBAAG,GACpC,QAAQ,aAAa;gBAEzB;gBACA,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;QACA,SAAS;yCAAE,CAAC,MAAM;gBAChB,8BAA8B;gBAC9B,YAAY,aAAa,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC;gBAAI;gBACjE,YAAY,aAAa,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,SAAS,CAAC;gBAAI;gBAEpE,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,SAAS;yCAAE;gBACT,wCAAwC;gBACxC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;YACpE;;IACF;AACF;IAnDgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkDb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;0CAAE,CAAC,KAAe,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC;;QACrE,QAAQ;0CAAE,OAAO;gBACf,+BAA+B;gBAC/B,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC;kDAAK,CAAC,MAAc,CAAC;4BACnE,GAAG,GAAG;4BACN,QAAQ;wBACV,CAAC;;gBAED,kBAAkB;gBAClB,YAAY,cAAc,CACxB;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;kDACpC,CAAC;wBACC,IAAI,KAAK,OAAO;4BACd,OAAO;gCACL,GAAG,GAAG;gCACN,OAAO,IAAI,KAAK,CAAC,GAAG;kEAAC,CAAC,OACpB,KAAK,EAAE,KAAK,KAAK;4CAAE,GAAG,IAAI;4CAAE,QAAQ;wCAAqB,IAAI;;4BAEjE;wBACF;wBACA,OAAO;oBACT;;YAEJ;;QACA,OAAO;0CAAE,CAAC,KAAK;gBACb,yBAAyB;gBACzB,YAAY,YAAY,CAAC,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC;kDAAK,CAAC,MAAc,CAAC;4BACnE,GAAG,GAAG;4BACN,QAAQ;wBACV,CAAC;;gBACD,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;QACA,SAAS;0CAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,SAAS;0CAAE,CAAC,MAAM,OAAO;gBACvB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,MAAM,CAAC;gBAAI;gBACrE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,+HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK;gBAAG;YACpE;;IACF;AACF;IA5CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 6735, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession } from \"next-auth/react\"\nimport { DashboardLayout } from \"@/components/layout/dashboard-layout\"\nimport { PageHeader } from \"@/components/layout/page-header\"\nimport { ProtectedRoute } from \"@/components/auth/protected-route\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { StatsCard } from \"@/components/ui/stats-card\"\nimport { QuickActions, type QuickActionItem } from \"@/components/ui/quick-actions\"\nimport { ActivityTimeline, type TimelineItem } from \"@/components/ui/activity-timeline\"\nimport { BookCard, type BookData } from \"@/components/ui/book-card\"\nimport { ChartWrapper, ChartPlaceholder } from \"@/components/ui/chart-wrapper\"\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\"\nimport { ErrorMessage } from \"@/components/ui/error-message\"\nimport { useAnalyticsOverview } from \"@/hooks/api/use-analytics\"\nimport { useBooks } from \"@/hooks/api/use-books\"\nimport { useUser } from \"@/hooks/api/use-auth\"\nimport { \n  PlusCircle, \n  BookOpen, \n  TrendingUp, \n  DollarSign, \n  Users, \n  BarChart3,\n  FileText, \n  Settings, \n  Download,\n  Eye,\n  AlertCircle,\n  Menu\n} from \"lucide-react\"\n\nexport default function DashboardPage() {\n  const { data: session } = useSession()\n  \n  // API hooks for real data\n  const { data: user, isLoading: userLoading, error: userError } = useUser()\n  const { data: analytics, isLoading: analyticsLoading, error: analyticsError } = useAnalyticsOverview()\n  const { data: booksData, isLoading: booksLoading, error: booksError } = useBooks({ limit: 6 })\n  \n  // Loading state\n  if (userLoading || analyticsLoading || booksLoading) {\n    return (\n      <ProtectedRoute>\n        <DashboardLayout>\n          <div className=\"flex items-center justify-center min-h-[400px]\">\n            <LoadingSpinner size=\"lg\" />\n          </div>\n        </DashboardLayout>\n      </ProtectedRoute>\n    )\n  }\n  \n  // Error state\n  if (userError || analyticsError || booksError) {\n    return (\n      <ProtectedRoute>\n        <DashboardLayout>\n          <div className=\"flex items-center justify-center min-h-[400px]\">\n            <div className=\"text-center space-y-4\">\n              <AlertCircle className=\"h-12 w-12 text-destructive mx-auto\" />\n              <div className=\"space-y-2\">\n                <h3 className=\"text-lg font-semibold\">\n                  Failed to load dashboard\n                </h3>\n                <p className=\"text-muted-foreground\">\n                  {String(\n                    userError ||\n                      analyticsError ||\n                      booksError ||\n                      \"An error occurred while loading the dashboard\"\n                  )}\n                </p>\n                <Button onClick={() => window.location.reload()}>\n                  Try Again\n                </Button>\n              </div>\n            </div>\n          </div>\n        </DashboardLayout>\n      </ProtectedRoute>\n    );\n  }\n\n  // Extract data with fallbacks\n  const books = booksData?.books || []\n  const totalBooks = booksData?.total || 0\n  \n  const quickActions: QuickActionItem[] = [\n    {\n      id: \"1\",\n      title: \"Generate New Book\",\n      description: \"Create a new book with AI assistance\",\n      icon: <PlusCircle className=\"h-5 w-5\" />,\n      variant: \"primary\",\n      onClick: () => window.location.href = '/dashboard/books/new'\n    },\n    {\n      id: \"2\", \n      title: \"View Analytics\",\n      description: \"Check your sales and performance data\",\n      icon: <BarChart3 className=\"h-5 w-5\" />,\n      onClick: () => window.location.href = '/dashboard/analytics'\n    },\n    {\n      id: \"3\",\n      title: \"Manage Library\",\n      description: \"Browse and edit your book collection\",\n      icon: <BookOpen className=\"h-5 w-5\" />,\n      badge: totalBooks > 0 ? totalBooks.toString() : undefined,\n      onClick: () => window.location.href = '/dashboard/books'\n    },\n    {\n      id: \"4\",\n      title: \"Account Settings\",\n      description: \"Update your profile and preferences\",\n      icon: <Settings className=\"h-5 w-5\" />,\n      onClick: () => window.location.href = '/dashboard/settings'\n    }\n  ]\n\n  const recentActivity: TimelineItem[] = [\n    {\n      id: \"1\",\n      title: \"Welcome to Publish AI!\",\n      description: \"Your account has been created successfully. Start by generating your first book.\",\n      timestamp: \"Just now\",\n      status: \"completed\"\n    },\n    {\n      id: \"2\",\n      title: \"Explore trending topics\",\n      description: \"Use our AI trend analyzer to discover profitable book niches.\",\n      timestamp: \"Getting started\",\n      status: \"pending\"\n    },\n    {\n      id: \"3\",\n      title: \"Generate your first manuscript\",\n      description: \"Let AI create high-quality content tailored to your chosen niche.\",\n      timestamp: \"Next step\",\n      status: \"pending\"\n    }\n  ]\n\n  const recentBooks: BookData[] = [\n    {\n      id: \"demo-1\",\n      title: \"Getting Started Guide\",\n      description: \"Learn how to use Publish AI to create and publish books effortlessly.\",\n      author: \"Publish AI\",\n      status: \"draft\",\n      genre: \"Tutorial\",\n      lastModified: \"Just created\",\n      pages: 25\n    }\n  ]\n\n  const salesData = [\n    { month: \"Jan\", sales: 0, revenue: 0 },\n    { month: \"Feb\", sales: 0, revenue: 0 },\n    { month: \"Mar\", sales: 0, revenue: 0 },\n    { month: \"Apr\", sales: 0, revenue: 0 },\n    { month: \"May\", sales: 0, revenue: 0 },\n    { month: \"Jun\", sales: 0, revenue: 0 }\n  ]\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"p-6\">\n          <PageHeader\n            title=\"Dashboard\"\n            description={`Welcome back${user?.name ? `, ${user.name}` : ''}! Here's an overview of your publishing activity.`}\n            actions={\n              <Button className=\"gap-2\" onClick={() => console.log('Create new book')}>\n                <PlusCircle className=\"h-4 w-4\" />\n                Create New Book\n              </Button>\n            }\n          />\n          \n          {/* Stats Cards Section */}\n          <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n            <StatsCard\n              value={analytics?.activeBooks?.toString() || totalBooks.toString()}\n              label=\"Total Books\"\n              description={totalBooks > 0 ? \"Your book collection\" : \"Start creating your first book\"}\n              icon={<BookOpen className=\"h-5 w-5\" />}\n              trend={analytics?.booksChange ? {\n                direction: analytics.booksChange > 0 ? \"up\" : analytics.booksChange < 0 ? \"down\" : \"neutral\",\n                value: Math.abs(analytics.booksChange),\n                label: \"vs last month\"\n              } : undefined}\n            />\n            <StatsCard\n              value={books.filter(book => book.status === 'published').length.toString()}\n              label=\"Published\"\n              description=\"Books available to readers\"\n              icon={<TrendingUp className=\"h-5 w-5\" />}\n              variant=\"success\"\n            />\n            <StatsCard\n              value={analytics?.totalRevenue ? `$${analytics.totalRevenue.toFixed(2)}` : \"$0\"}\n              label=\"Total Revenue\"\n              description={analytics?.totalRevenue ? \"Lifetime earnings\" : \"Start earning today\"}\n              icon={<DollarSign className=\"h-5 w-5\" />}\n              variant=\"info\"\n              trend={analytics?.revenueChange ? {\n                direction: analytics.revenueChange > 0 ? \"up\" : analytics.revenueChange < 0 ? \"down\" : \"neutral\",\n                value: Math.abs(analytics.revenueChange),\n                label: \"vs last month\"\n              } : undefined}\n            />\n            <StatsCard\n              value={analytics?.totalSales?.toString() || \"0\"}\n              label=\"Total Sales\"\n              description={analytics?.totalSales ? \"Books sold\" : \"Build your audience\"}\n              icon={<Users className=\"h-5 w-5\" />}\n              trend={analytics?.salesChange ? {\n                direction: analytics.salesChange > 0 ? \"up\" : analytics.salesChange < 0 ? \"down\" : \"neutral\",\n                value: Math.abs(analytics.salesChange),\n                label: \"vs last month\"\n              } : undefined}\n            />\n          </div>\n\n          {/* Quick Actions Section */}\n          <div className=\"mt-8\">\n            <h2 className=\"text-xl font-semibold mb-4\">Quick Actions</h2>\n            <QuickActions items={quickActions} columns={2} />\n          </div>\n\n          {/* Main Content Grid */}\n          <div className=\"mt-8 grid gap-8 lg:grid-cols-3\">\n            {/* Recent Activity Timeline */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"space-y-6\">\n                {/* Recent Books */}\n                <div>\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h2 className=\"text-xl font-semibold\">Recent Books</h2>\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Eye className=\"h-4 w-4 mr-2\" />\n                      View All\n                    </Button>\n                  </div>\n                  {books.length > 0 ? (\n                    <div className=\"grid gap-4 md:grid-cols-2\">\n                      {books.slice(0, 4).map((book) => (\n                        <BookCard\n                          key={book.id}\n                          book={{\n                            id: book.id,\n                            title: book.title,\n                            description: book.description,\n                            author: user?.name || \"You\",\n                            status: book.status,\n                            genre: book.category,\n                            lastModified: new Date(book.updated_at).toLocaleDateString(),\n                            pages: Math.floor(book.word_count / 250) // Approximate pages\n                          }}\n                          variant=\"compact\"\n                          onView={(book) => window.location.href = `/dashboard/books/${book.id}`}\n                          onEdit={(book) => window.location.href = `/dashboard/books/${book.id}/edit`}\n                        />\n                      ))}\n                    </div>\n                  ) : (\n                    <Card>\n                      <CardContent className=\"py-8 text-center\">\n                        <BookOpen className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n                        <h3 className=\"font-medium mb-2\">No books yet</h3>\n                        <p className=\"text-sm text-muted-foreground mb-4\">\n                          Start by creating your first book with AI assistance\n                        </p>\n                        <Button onClick={() => console.log('Create first book')}>\n                          <PlusCircle className=\"h-4 w-4 mr-2\" />\n                          Create Your First Book\n                        </Button>\n                      </CardContent>\n                    </Card>\n                  )}\n                </div>\n\n                {/* Performance Chart */}\n                <div>\n                  <h2 className=\"text-xl font-semibold mb-4\">Performance Overview</h2>\n                  <ChartWrapper\n                    title=\"Sales & Revenue\"\n                    description=\"Track your publishing performance over time\"\n                    height=\"sm\"\n                    empty={true}\n                    emptyMessage=\"Start publishing to see your performance data\"\n                    actions={\n                      <Button variant=\"outline\" size=\"sm\">\n                        <Download className=\"h-4 w-4 mr-2\" />\n                        Export\n                      </Button>\n                    }\n                  >\n                    <ChartPlaceholder type=\"area\" data={salesData} />\n                  </ChartWrapper>\n                </div>\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <div className=\"space-y-6\">\n              {/* Activity Timeline */}\n              <div>\n                <h2 className=\"text-xl font-semibold mb-4\">Getting Started</h2>\n                <ActivityTimeline \n                  items={recentActivity} \n                  compact \n                  size=\"sm\"\n                />\n              </div>\n\n              {/* Getting Started Guide */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>Quick Start Guide</CardTitle>\n                  <CardDescription>\n                    Follow these steps to publish your first book\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-semibold\">\n                        1\n                      </div>\n                      <div>\n                        <div className=\"font-medium\">Discover Trends</div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          Find profitable niches\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-8 h-8 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-semibold\">\n                        2\n                      </div>\n                      <div>\n                        <div className=\"font-medium\">Generate Content</div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          AI creates your manuscript\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-8 h-8 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-semibold\">\n                        3\n                      </div>\n                      <div>\n                        <div className=\"font-medium\">Publish & Earn</div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          One-click to Amazon KDP\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"mt-6\">\n                    <Button className=\"w-full\" onClick={() => console.log('Start tutorial')}>\n                      <FileText className=\"h-4 w-4 mr-2\" />\n                      Start Tutorial\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAlBA;;;;;;;;;;;;;;;;;AAiCe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,0BAA0B;IAC1B,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,WAAW,EAAE,OAAO,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvE,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,gBAAgB,EAAE,OAAO,cAAc,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD;IACnG,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;IAAE;IAE5F,gBAAgB;IAChB,IAAI,eAAe,oBAAoB,cAAc;QACnD,qBACE,6LAAC,mJAAA,CAAA,iBAAc;sBACb,cAAA,6LAAC,sJAAA,CAAA,kBAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wBAAC,MAAK;;;;;;;;;;;;;;;;;;;;;IAK/B;IAEA,cAAc;IACd,IAAI,aAAa,kBAAkB,YAAY;QAC7C,qBACE,6LAAC,mJAAA,CAAA,iBAAc;sBACb,cAAA,6LAAC,sJAAA,CAAA,kBAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDAGtC,6LAAC;wCAAE,WAAU;kDACV,OACC,aACE,kBACA,cACA;;;;;;kDAGN,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;kDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS/D;IAEA,8BAA8B;IAC9B,MAAM,QAAQ,WAAW,SAAS,EAAE;IACpC,MAAM,aAAa,WAAW,SAAS;IAEvC,MAAM,eAAkC;QACtC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,SAAS;YACT,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;QACxC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;QACxC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO,aAAa,IAAI,WAAW,QAAQ,KAAK;YAChD,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;QACxC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;QACxC;KACD;IAED,MAAM,iBAAiC;QACrC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;QACV;KACD;IAED,MAAM,cAA0B;QAC9B;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,cAAc;YACd,OAAO;QACT;KACD;IAED,MAAM,YAAY;QAChB;YAAE,OAAO;YAAO,OAAO;YAAG,SAAS;QAAE;QACrC;YAAE,OAAO;YAAO,OAAO;YAAG,SAAS;QAAE;QACrC;YAAE,OAAO;YAAO,OAAO;YAAG,SAAS;QAAE;QACrC;YAAE,OAAO;YAAO,OAAO;YAAG,SAAS;QAAE;QACrC;YAAE,OAAO;YAAO,OAAO;YAAG,SAAS;QAAE;QACrC;YAAE,OAAO;YAAO,OAAO;YAAG,SAAS;QAAE;KACtC;IAED,qBACE,6LAAC,mJAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,sJAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,aAAU;wBACT,OAAM;wBACN,aAAa,CAAC,YAAY,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,KAAK,IAAI,EAAE,GAAG,GAAG,iDAAiD,CAAC;wBACjH,uBACE,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;4BAAQ,SAAS,IAAM,QAAQ,GAAG,CAAC;;8CACnD,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAOxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,4IAAA,CAAA,YAAS;gCACR,OAAO,WAAW,aAAa,cAAc,WAAW,QAAQ;gCAChE,OAAM;gCACN,aAAa,aAAa,IAAI,yBAAyB;gCACvD,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAC1B,OAAO,WAAW,cAAc;oCAC9B,WAAW,UAAU,WAAW,GAAG,IAAI,OAAO,UAAU,WAAW,GAAG,IAAI,SAAS;oCACnF,OAAO,KAAK,GAAG,CAAC,UAAU,WAAW;oCACrC,OAAO;gCACT,IAAI;;;;;;0CAEN,6LAAC,4IAAA,CAAA,YAAS;gCACR,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM,CAAC,QAAQ;gCACxE,OAAM;gCACN,aAAY;gCACZ,oBAAM,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAC5B,SAAQ;;;;;;0CAEV,6LAAC,4IAAA,CAAA,YAAS;gCACR,OAAO,WAAW,eAAe,CAAC,CAAC,EAAE,UAAU,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;gCAC3E,OAAM;gCACN,aAAa,WAAW,eAAe,sBAAsB;gCAC7D,oBAAM,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAC5B,SAAQ;gCACR,OAAO,WAAW,gBAAgB;oCAChC,WAAW,UAAU,aAAa,GAAG,IAAI,OAAO,UAAU,aAAa,GAAG,IAAI,SAAS;oCACvF,OAAO,KAAK,GAAG,CAAC,UAAU,aAAa;oCACvC,OAAO;gCACT,IAAI;;;;;;0CAEN,6LAAC,4IAAA,CAAA,YAAS;gCACR,OAAO,WAAW,YAAY,cAAc;gCAC5C,OAAM;gCACN,aAAa,WAAW,aAAa,eAAe;gCACpD,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACvB,OAAO,WAAW,cAAc;oCAC9B,WAAW,UAAU,WAAW,GAAG,IAAI,OAAO,UAAU,WAAW,GAAG,IAAI,SAAS;oCACnF,OAAO,KAAK,GAAG,CAAC,UAAU,WAAW;oCACrC,OAAO;gCACT,IAAI;;;;;;;;;;;;kCAKR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC,+IAAA,CAAA,eAAY;gCAAC,OAAO;gCAAc,SAAS;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAwB;;;;;;sEACtC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;;8EAC7B,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;gDAInC,MAAM,MAAM,GAAG,kBACd,6LAAC;oDAAI,WAAU;8DACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACtB,6LAAC,2IAAA,CAAA,WAAQ;4DAEP,MAAM;gEACJ,IAAI,KAAK,EAAE;gEACX,OAAO,KAAK,KAAK;gEACjB,aAAa,KAAK,WAAW;gEAC7B,QAAQ,MAAM,QAAQ;gEACtB,QAAQ,KAAK,MAAM;gEACnB,OAAO,KAAK,QAAQ;gEACpB,cAAc,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;gEAC1D,OAAO,KAAK,KAAK,CAAC,KAAK,UAAU,GAAG,KAAK,oBAAoB;4DAC/D;4DACA,SAAQ;4DACR,QAAQ,CAAC,OAAS,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;4DACtE,QAAQ,CAAC,OAAS,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;2DAbtE,KAAK,EAAE;;;;;;;;;yEAkBlB,6LAAC,mIAAA,CAAA,OAAI;8DACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAG,WAAU;0EAAmB;;;;;;0EACjC,6LAAC;gEAAE,WAAU;0EAAqC;;;;;;0EAGlD,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAS,IAAM,QAAQ,GAAG,CAAC;;kFACjC,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;sDASjD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC,+IAAA,CAAA,eAAY;oDACX,OAAM;oDACN,aAAY;oDACZ,QAAO;oDACP,OAAO;oDACP,cAAa;oDACb,uBACE,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;;0EAC7B,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;8DAKzC,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;wDAAC,MAAK;wDAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO5C,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC,mJAAA,CAAA,mBAAgB;gDACf,OAAO;gDACP,OAAO;gDACP,MAAK;;;;;;;;;;;;kDAKT,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAiH;;;;;;kFAGhI,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAAc;;;;;;0FAC7B,6LAAC;gFAAI,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;0EAKnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAA6G;;;;;;kFAG5H,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAAc;;;;;;0FAC7B,6LAAC;gFAAI,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;0EAKnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAA6G;;;;;;kFAG5H,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAAc;;;;;;0FAC7B,6LAAC;gFAAI,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;;;;;;;kEAMrD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAS,SAAS,IAAM,QAAQ,GAAG,CAAC;;8EACpD,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAzVwB;;QACI,iJAAA,CAAA,aAAU;QAG6B,qIAAA,CAAA,UAAO;QACQ,0IAAA,CAAA,uBAAoB;QAC5B,sIAAA,CAAA,WAAQ;;;KAN1D", "debugId": null}}]}