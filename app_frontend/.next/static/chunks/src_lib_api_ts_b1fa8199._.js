(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "apiClient": (()=>apiClient),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-client] (ecmascript)");
;
;
;
class ApiCache {
    cache = new Map();
    config = {
        ttl: 5 * 60 * 1000,
        maxSize: 100
    };
    set(key, data, ttl) {
        // Remove oldest entries if cache is full
        if (this.cache.size >= this.config.maxSize) {
            const firstKey = this.cache.keys().next().value;
            if (!firstKey) return;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl: ttl || this.config.ttl
        });
    }
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) return null;
        // Check if expired
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    clear() {
        this.cache.clear();
    }
    delete(key) {
        this.cache.delete(key);
    }
}
class ApiClient {
    client;
    cache = new ApiCache();
    loadingStates = new Map();
    retryConfig = {
        retries: 3,
        retryDelay: 1000,
        retryCondition: (error)=>{
            // Retry on network errors and 5xx status codes
            return !error.response || error.response.status >= 500 && error.response.status < 600;
        }
    };
    constructor(){
        this.client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: ("TURBOPACK compile-time value", "http://localhost:8000") || "http://localhost:8000",
            headers: {
                "Content-Type": "application/json"
            },
            timeout: 5000
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // Request interceptor
        this.client.interceptors.request.use(async (config)=>{
            // Add auth token if available - use NextAuth session instead of localStorage
            if ("TURBOPACK compile-time truthy", 1) {
                try {
                    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSession"])();
                    // Check for NextAuth session first
                    if (session?.accessToken) {
                        config.headers.Authorization = `Bearer ${session.accessToken}`;
                    } else {
                        // Fallback to localStorage for backward compatibility
                        const token = localStorage.getItem("auth_token");
                        if (token) {
                            config.headers.Authorization = `Bearer ${token}`;
                        }
                    }
                } catch (error) {
                    // If getSession fails, try localStorage as fallback
                    const token = localStorage.getItem("auth_token");
                    if (token) {
                        config.headers.Authorization = `Bearer ${token}`;
                    }
                }
            }
            // Add request ID for tracking
            config.headers["X-Request-ID"] = crypto.randomUUID();
            // Set loading state
            const requestKey = this.getRequestKey(config);
            this.setLoading(requestKey, true);
            // Security: Never log requests containing sensitive data
            if ("TURBOPACK compile-time truthy", 1) {
                const hasCredentials = config.url?.includes("/auth/") || config.data?.password || config.data?.token;
                if (!hasCredentials) {
                // Only log non-sensitive requests in development
                }
            }
            return config;
        }, (error)=>Promise.reject(error));
        // Response interceptor with retry logic
        this.client.interceptors.response.use((response)=>{
            // Clear loading state
            const requestKey = this.getRequestKey(response.config);
            this.setLoading(requestKey, false);
            return response;
        }, async (error)=>{
            const config = error.config;
            const requestKey = this.getRequestKey(config);
            this.setLoading(requestKey, false);
            // Retry logic
            if (this.shouldRetry(error, config)) {
                config._retryCount = (config._retryCount || 0) + 1;
                // Exponential backoff
                const delay = this.retryConfig.retryDelay * Math.pow(2, config._retryCount - 1);
                await new Promise((resolve)=>setTimeout(resolve, delay));
                return this.client(config);
            }
            const apiError = {
                message: error.message || "An unexpected error occurred",
                status: error.response?.status,
                details: error.response?.data
            };
            if (error.response?.data) {
                const errorData = error.response.data;
                apiError.message = errorData.detail || errorData.message || apiError.message;
                apiError.code = errorData.code;
            }
            // Handle different error types
            this.handleError(apiError);
            return Promise.reject(apiError);
        });
    }
    shouldRetry(error, config) {
        const retryCount = config._retryCount || 0;
        return retryCount < this.retryConfig.retries && config.method?.toLowerCase() === "get" && // Only retry GET requests
        this.retryConfig.retryCondition?.(error) === true;
    }
    handleError(error) {
        // Handle connection errors (backend not running)
        if (!error.status || error.message.includes("Network Error") || error.message.includes("ECONNREFUSED")) {
            // Don't show error toast for connection issues when backend is expected to be down
            return;
        }
        // Handle auth errors
        if (error.status === 401) {
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.removeItem("auth_token");
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Session expired. Please login again.");
                window.location.href = "/auth/login";
            }
            return;
        }
        // Handle other errors with toast notifications
        if (error.status && error.status >= 400) {
            if (error.status >= 500) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Server error. Please try again later.");
            } else if (error.status === 404) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Resource not found");
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message);
            }
        }
    }
    getRequestKey(config) {
        return `${config.method?.toUpperCase()}_${config.url}`;
    }
    setLoading(key, loading) {
        if (loading) {
            this.loadingStates.set(key, true);
        } else {
            this.loadingStates.delete(key);
        }
    }
    // Generic request methods with caching
    async get(url, params, useCache = true) {
        const cacheKey = `GET_${url}_${JSON.stringify(params || {})}`;
        // Check cache first
        if (useCache) {
            const cached = this.cache.get(cacheKey);
            if (cached) {
                return cached;
            }
        }
        const response = await this.client.get(url, {
            params
        });
        const data = response.data;
        // Cache successful GET requests
        if (useCache && response.status === 200) {
            this.cache.set(cacheKey, data);
        }
        return data;
    }
    async post(url, data) {
        // If no API URL is set, return mock response
        console.log("API URL: ", ("TURBOPACK compile-time value", "http://localhost:8000"));
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        const response = await this.client.post(url, data);
        // Invalidate related cache entries
        this.invalidateCache(url);
        return response.data;
    }
    async put(url, data) {
        const response = await this.client.put(url, data);
        // Invalidate related cache entries
        this.invalidateCache(url);
        return response.data;
    }
    async patch(url, data) {
        const response = await this.client.patch(url, data);
        // Invalidate related cache entries
        this.invalidateCache(url);
        return response.data;
    }
    async delete(url) {
        const response = await this.client.delete(url);
        // Invalidate related cache entries
        this.invalidateCache(url);
        return response.data;
    }
    // File upload with progress
    async uploadFile(url, file, onProgress) {
        const formData = new FormData();
        formData.append("file", file);
        const response = await this.client.post(url, formData, {
            headers: {
                "Content-Type": "multipart/form-data"
            },
            onUploadProgress: (progressEvent)=>{
                if (onProgress && progressEvent.total) {
                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);
                    onProgress(progress);
                }
            }
        });
        return response.data;
    }
    // Optimistic updates
    async optimisticUpdate(url, data, optimisticData, cacheKey) {
        // Set optimistic data immediately
        if (cacheKey) {
            this.cache.set(cacheKey, optimisticData);
        }
        try {
            const result = await this.put(url, data);
            // Update cache with real data
            if (cacheKey) {
                this.cache.set(cacheKey, result);
            }
            return result;
        } catch (error) {
            // Revert optimistic update on error
            if (cacheKey) {
                this.cache.delete(cacheKey);
            }
            throw error;
        }
    }
    // Cache management
    invalidateCache(url) {
        // Remove cache entries that might be affected by this change
        const keysToDelete = [];
        // Simple pattern matching - could be more sophisticated
        const baseUrl = url.split("/").slice(0, -1).join("/");
        for (const key of Array.from(this.cache["cache"].keys())){
            if (key.includes(baseUrl) || key.includes(url)) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach((key)=>this.cache.delete(key));
    }
    // Loading state management
    isLoading(url, method = "GET") {
        const key = `${method.toUpperCase()}_${url}`;
        return this.loadingStates.has(key);
    }
    // Utility methods
    setAuthToken(token) {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem("auth_token", token);
        }
    }
    removeAuthToken() {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.removeItem("auth_token");
        }
    }
    // Cache utilities
    clearCache() {
        this.cache.clear();
    }
    getCacheStats() {
        return {
            size: this.cache["cache"].size,
            keys: Array.from(this.cache["cache"].keys())
        };
    }
    // Health check
    async healthCheck() {
        try {
            await this.get("/health", {}, false); // Don't cache health checks
            return true;
        } catch  {
            return false;
        }
    }
}
const apiClient = new ApiClient();
const __TURBOPACK__default__export__ = apiClient;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_lib_api_ts_b1fa8199._.js.map