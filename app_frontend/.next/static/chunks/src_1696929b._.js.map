{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-white hover:bg-primary-600 active:bg-primary-700 shadow-sm hover:shadow-md\",\n        secondary: \"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300 border border-secondary-300\",\n        outline: \"border border-border bg-background hover:bg-surface-200 hover:border-secondary-400 text-foreground\",\n        ghost: \"hover:bg-surface-200 hover:text-accent text-secondary-700\",\n        link: \"text-primary hover:text-primary-600 underline-offset-4 hover:underline p-0 h-auto\",\n        destructive: \"bg-error text-white hover:bg-red-600 active:bg-red-700 shadow-sm\",\n        success: \"bg-success text-white hover:bg-green-600 active:bg-green-700 shadow-sm\",\n      },\n      size: {\n        sm: \"h-8 px-3 py-1.5 text-xs rounded-md\",\n        default: \"h-10 px-4 py-2 text-sm\",\n        lg: \"h-12 px-6 py-3 text-base rounded-xl\",\n        xl: \"h-14 px-8 py-4 text-lg rounded-xl\",\n        icon: \"h-10 w-10 p-0\",\n        \"icon-sm\": \"h-8 w-8 p-0\",\n        \"icon-lg\": \"h-12 w-12 p-0\",\n      },\n      loading: {\n        true: \"cursor-not-allowed\",\n        false: \"\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      loading: false,\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  loadingText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant, \n    size, \n    loading = false,\n    loadingText,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled,\n    asChild = false, \n    ...props \n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    const isDisabled = disabled || loading\n    \n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, loading, className }))}\n        disabled={isDisabled}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            aria-hidden=\"true\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {!loading && leftIcon && <span className=\"mr-1\">{leftIcon}</span>}\n        {loading ? loadingText || children : children}\n        {!loading && rightIcon && <span className=\"ml-1\">{rightIcon}</span>}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,qYACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAaF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,WAAW,EACX,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,aAAa,YAAY;IAE/B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAS;QAAU;QACjE,UAAU;QACV,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,eAAY;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,WAAW,0BAAY,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD,UAAU,eAAe,WAAW;YACpC,CAAC,WAAW,2BAAa,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+jBACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/container.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nexport interface ContainerProps extends HTMLAttributes<HTMLDivElement> {\n  maxWidth?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"full\"\n}\n\nconst Container = forwardRef<HTMLDivElement, ContainerProps>(\n  ({ className, maxWidth = \"2xl\", children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"mx-auto w-full px-4 sm:px-6 lg:px-8\",\n          {\n            \"max-w-screen-sm\": maxWidth === \"sm\",\n            \"max-w-screen-md\": maxWidth === \"md\",\n            \"max-w-screen-lg\": maxWidth === \"lg\",\n            \"max-w-screen-xl\": maxWidth === \"xl\",\n            \"max-w-screen-2xl\": maxWidth === \"2xl\",\n            \"max-w-full\": maxWidth === \"full\",\n          },\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\nContainer.displayName = \"Container\"\n\nexport { Container }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACzB,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uCACA;YACE,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,oBAAoB,aAAa;YACjC,cAAc,aAAa;QAC7B,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEF,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/auth/authHelpers.ts"], "sourcesContent": ["import { getServerSession } from \"next-auth/next\"\nimport { Session } from \"next-auth\"\nimport type { AuthError, AuthErrorType, AuthResponse } from \"@/types/auth\"\n\n// API base URL\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\"\n\n/**\n * Get the current session from NextAuth\n */\nexport async function getCurrentSession(): Promise<Session | null> {\n  try {\n    const session = await getServerSession()\n    return session\n  } catch (error) {\n    console.error(\"Error getting session:\", error)\n    return null\n  }\n}\n\n/**\n * Get auth headers for API requests\n */\nexport function getAuthHeaders(accessToken?: string): HeadersInit {\n  const headers: HeadersInit = {\n    \"Content-Type\": \"application/json\",\n  }\n\n  if (accessToken) {\n    headers[\"Authorization\"] = `Bearer ${accessToken}`\n  }\n\n  return headers\n}\n\n\n/**\n * Standardize authentication errors\n */\nexport function standardizeAuthError(error: any): AuthError {\n  if (error?.response?.status === 401) {\n    return {\n      type: AuthErrorType.CredentialsSignIn,\n      message: \"Invalid email or password\",\n      code: \"INVALID_CREDENTIALS\",\n    }\n  }\n\n  if (error?.response?.status === 404) {\n    return {\n      type: AuthErrorType.CredentialsSignIn,\n      message: \"User not found\",\n      code: \"USER_NOT_FOUND\",\n    }\n  }\n\n  if (error?.response?.status === 429) {\n    return {\n      type: AuthErrorType.Default,\n      message: \"Too many attempts. Please try again later\",\n      code: \"RATE_LIMIT_EXCEEDED\",\n    }\n  }\n\n  if (error?.message?.includes(\"OAuth\")) {\n    return {\n      type: AuthErrorType.OAuthSignIn,\n      message: error.message,\n      code: \"OAUTH_ERROR\",\n    }\n  }\n\n  return {\n    type: AuthErrorType.Default,\n    message: error?.message || \"An unexpected error occurred\",\n    code: \"UNKNOWN_ERROR\",\n  }\n}\n\n/**\n * Validate session and check if it's expired\n */\nexport function isSessionValid(session: Session | null): boolean {\n  if (!session) return false\n  \n  // Check if session has required fields\n  if (!session.user?.id || !session.user?.email) return false\n  \n  // If there's an error in the session, it's invalid\n  if (session.error) return false\n  \n  return true\n}\n\n/**\n * Get user-friendly error messages\n */\nexport function getAuthErrorMessage(errorType: AuthErrorType): string {\n  switch (errorType) {\n    case AuthErrorType.CredentialsSignIn:\n      return \"Invalid email or password. Please check your credentials and try again.\"\n    case AuthErrorType.OAuthSignIn:\n      return \"Failed to sign in with the selected provider. Please try again.\"\n    case AuthErrorType.OAuthCallback:\n      return \"Failed to complete OAuth sign in. Please try again.\"\n    case AuthErrorType.OAuthCreateAccount:\n      return \"Failed to create account with the selected provider.\"\n    case AuthErrorType.OAuthAccountNotLinked:\n      return \"This account is already linked to another provider.\"\n    case AuthErrorType.SessionRequired:\n      return \"You must be signed in to access this page.\"\n    default:\n      return \"An unexpected error occurred. Please try again.\"\n  }\n}\n\n/**\n * Format user data for display\n */\nexport function formatUserData(user: any) {\n  return {\n    id: user.id,\n    email: user.email,\n    name: user.name || \"User\",\n    image: user.image || user.avatar_url || null,\n    initials: getInitials(user.name || user.email),\n  }\n}\n\n/**\n * Get initials from name or email\n */\nfunction getInitials(nameOrEmail: string): string {\n  if (!nameOrEmail) return \"U\"\n  \n  // If it's an email, use the first letter\n  if (nameOrEmail.includes(\"@\")) {\n    return nameOrEmail[0].toUpperCase()\n  }\n  \n  // If it's a name, get first letters of first and last name\n  const parts = nameOrEmail.split(\" \")\n  if (parts.length >= 2) {\n    return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase()\n  }\n  \n  return nameOrEmail[0].toUpperCase()\n}\n\n/**\n * Check if error is due to network issues\n */\nexport function isNetworkError(error: any): boolean {\n  return (\n    error?.code === \"ECONNREFUSED\" ||\n    error?.code === \"ENOTFOUND\" ||\n    error?.message?.includes(\"fetch failed\") ||\n    error?.message?.includes(\"Network request failed\")\n  )\n}\n\n/**\n * Get redirect URL after authentication\n */\nexport function getAuthRedirectUrl(callbackUrl?: string | null): string {\n  // Default redirect to dashboard\n  const defaultRedirect = \"/dashboard\"\n  \n  if (!callbackUrl) return defaultRedirect\n  \n  // Ensure the callback URL is safe (same origin)\n  try {\n    const url = new URL(callbackUrl, window.location.origin)\n    if (url.origin === window.location.origin) {\n      return url.pathname + url.search + url.hash\n    }\n  } catch {\n    // Invalid URL\n  }\n  \n  return defaultRedirect\n}"], "names": [], "mappings": ";;;;;;;;;;AAKqB;AALrB;;AAIA,eAAe;AACf,MAAM,eAAe,6DAAmC;AAKjD,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAKO,SAAS,eAAe,WAAoB;IACjD,MAAM,UAAuB;QAC3B,gBAAgB;IAClB;IAEA,IAAI,aAAa;QACf,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa;IACpD;IAEA,OAAO;AACT;AAMO,SAAS,qBAAqB,KAAU;IAC7C,IAAI,OAAO,UAAU,WAAW,KAAK;QACnC,OAAO;YACL,MAAM,cAAc,iBAAiB;YACrC,SAAS;YACT,MAAM;QACR;IACF;IAEA,IAAI,OAAO,UAAU,WAAW,KAAK;QACnC,OAAO;YACL,MAAM,cAAc,iBAAiB;YACrC,SAAS;YACT,MAAM;QACR;IACF;IAEA,IAAI,OAAO,UAAU,WAAW,KAAK;QACnC,OAAO;YACL,MAAM,cAAc,OAAO;YAC3B,SAAS;YACT,MAAM;QACR;IACF;IAEA,IAAI,OAAO,SAAS,SAAS,UAAU;QACrC,OAAO;YACL,MAAM,cAAc,WAAW;YAC/B,SAAS,MAAM,OAAO;YACtB,MAAM;QACR;IACF;IAEA,OAAO;QACL,MAAM,cAAc,OAAO;QAC3B,SAAS,OAAO,WAAW;QAC3B,MAAM;IACR;AACF;AAKO,SAAS,eAAe,OAAuB;IACpD,IAAI,CAAC,SAAS,OAAO;IAErB,uCAAuC;IACvC,IAAI,CAAC,QAAQ,IAAI,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE,OAAO,OAAO;IAEtD,mDAAmD;IACnD,IAAI,QAAQ,KAAK,EAAE,OAAO;IAE1B,OAAO;AACT;AAKO,SAAS,oBAAoB,SAAwB;IAC1D,OAAQ;QACN,KAAK,cAAc,iBAAiB;YAClC,OAAO;QACT,KAAK,cAAc,WAAW;YAC5B,OAAO;QACT,KAAK,cAAc,aAAa;YAC9B,OAAO;QACT,KAAK,cAAc,kBAAkB;YACnC,OAAO;QACT,KAAK,cAAc,qBAAqB;YACtC,OAAO;QACT,KAAK,cAAc,eAAe;YAChC,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,eAAe,IAAS;IACtC,OAAO;QACL,IAAI,KAAK,EAAE;QACX,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI,IAAI;QACnB,OAAO,KAAK,KAAK,IAAI,KAAK,UAAU,IAAI;QACxC,UAAU,YAAY,KAAK,IAAI,IAAI,KAAK,KAAK;IAC/C;AACF;AAEA;;CAEC,GACD,SAAS,YAAY,WAAmB;IACtC,IAAI,CAAC,aAAa,OAAO;IAEzB,yCAAyC;IACzC,IAAI,YAAY,QAAQ,CAAC,MAAM;QAC7B,OAAO,WAAW,CAAC,EAAE,CAAC,WAAW;IACnC;IAEA,2DAA2D;IAC3D,MAAM,QAAQ,YAAY,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,GAAG;QACrB,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,WAAW;IAClE;IAEA,OAAO,WAAW,CAAC,EAAE,CAAC,WAAW;AACnC;AAKO,SAAS,eAAe,KAAU;IACvC,OACE,OAAO,SAAS,kBAChB,OAAO,SAAS,eAChB,OAAO,SAAS,SAAS,mBACzB,OAAO,SAAS,SAAS;AAE7B;AAKO,SAAS,mBAAmB,WAA2B;IAC5D,gCAAgC;IAChC,MAAM,kBAAkB;IAExB,IAAI,CAAC,aAAa,OAAO;IAEzB,gDAAgD;IAChD,IAAI;QACF,MAAM,MAAM,IAAI,IAAI,aAAa,OAAO,QAAQ,CAAC,MAAM;QACvD,IAAI,IAAI,MAAM,KAAK,OAAO,QAAQ,CAAC,MAAM,EAAE;YACzC,OAAO,IAAI,QAAQ,GAAG,IAAI,MAAM,GAAG,IAAI,IAAI;QAC7C;IACF,EAAE,OAAM;IACN,cAAc;IAChB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { useSession, signOut } from \"next-auth/react\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Container } from \"@/components/ui/container\"\nimport { Menu, Bell, User, LogOut, Settings, BookOpen } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { formatUserData } from \"@/lib/auth/authHelpers\"\n\ninterface HeaderProps {\n  onMenuClick?: () => void\n}\n\nexport function Header({ onMenuClick }: HeaderProps) {\n  const pathname = usePathname()\n  const { data: session, status } = useSession()\n\n  const user = session?.user ? formatUserData(session.user) : null\n\n  const navigation = [\n    { name: \"Dashboard\", href: \"/dashboard\" },\n    { name: \"Books\", href: \"/dashboard/books\" },\n    { name: \"Analytics\", href: \"/dashboard/analytics\" },\n    { name: \"Trends\", href: \"/dashboard/trends\" },\n  ]\n\n  const handleLogout = async () => {\n    try {\n      await signOut({ \n        callbackUrl: \"/auth/login\",\n        redirect: true \n      })\n    } catch (error) {\n      console.error(\"Logout error:\", error)\n    }\n  }\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <Container maxWidth=\"full\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Left section */}\n          <div className=\"flex items-center gap-4\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"md:hidden\"\n              onClick={onMenuClick}\n            >\n              <Menu className=\"h-5 w-5\" />\n              <span className=\"sr-only\">Toggle menu</span>\n            </Button>\n\n            <Link href=\"/\" className=\"flex items-center gap-2\">\n              <BookOpen className=\"h-6 w-6 text-primary\" />\n              <span className=\"font-display text-xl font-semibold hidden sm:inline\">\n                Publish AI\n              </span>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center gap-1 ml-6\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    \"px-3 py-2 text-sm font-medium rounded-lg transition-colors\",\n                    pathname === item.href\n                      ? \"bg-primary/10 text-primary\"\n                      : \"text-muted-foreground hover:text-foreground hover:bg-muted\"\n                  )}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n\n          {/* Right section */}\n          <div className=\"flex items-center gap-2\">\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute top-0 right-0 h-2 w-2 rounded-full bg-primary\" />\n              <span className=\"sr-only\">Notifications</span>\n            </Button>\n\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                  <Avatar className=\"h-8 w-8\">\n                    <AvatarImage src={user?.image || undefined} alt={user?.name} />\n                    <AvatarFallback>\n                      {user?.initials || \"U\"}\n                    </AvatarFallback>\n                  </Avatar>\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                <DropdownMenuLabel className=\"font-normal\">\n                  <div className=\"flex flex-col space-y-1\">\n                    <p className=\"text-sm font-medium leading-none\">{user?.name || \"Guest\"}</p>\n                    <p className=\"text-xs leading-none text-muted-foreground\">\n                      {user?.email || \"<EMAIL>\"}\n                    </p>\n                  </div>\n                </DropdownMenuLabel>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem asChild>\n                  <Link href=\"/dashboard/profile\" className=\"cursor-pointer\">\n                    <User className=\"mr-2 h-4 w-4\" />\n                    <span>Profile</span>\n                  </Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem asChild>\n                  <Link href=\"/dashboard/settings\" className=\"cursor-pointer\">\n                    <Settings className=\"mr-2 h-4 w-4\" />\n                    <span>Settings</span>\n                  </Link>\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem \n                  className=\"cursor-pointer text-destructive\"\n                  onClick={handleLogout}\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  <span>Log out</span>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </Container>\n    </header>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAlBA;;;;;;;;;;;AAwBO,SAAS,OAAO,EAAE,WAAW,EAAe;;IACjD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAE3C,MAAM,OAAO,SAAS,OAAO,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,IAAI;IAE5D,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAmB;QAC1C;YAAE,MAAM;YAAa,MAAM;QAAuB;QAClD;YAAE,MAAM;YAAU,MAAM;QAAoB;KAC7C;IAED,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gBACZ,aAAa;gBACb,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC,wIAAA,CAAA,YAAS;YAAC,UAAS;sBAClB,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAsD;;;;;;;;;;;;0CAMxE,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA,aAAa,KAAK,IAAI,GAClB,+BACA;kDAGL,KAAK,IAAI;uCATL,KAAK,IAAI;;;;;;;;;;;;;;;;kCAgBtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,WAAU;;kDAC5C,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,MAAM,SAAS;wDAAW,KAAK,MAAM;;;;;;kEACvD,6LAAC,qIAAA,CAAA,iBAAc;kEACZ,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;kDAK3B,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,6LAAC,+IAAA,CAAA,oBAAiB;gDAAC,WAAU;0DAC3B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC,MAAM,QAAQ;;;;;;sEAC/D,6LAAC;4DAAE,WAAU;sEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;0DAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;;sEACxC,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAsB,WAAU;;sEACzC,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;;kEAET,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA1HgB;;QACG,qIAAA,CAAA,cAAW;QACM,iJAAA,CAAA,aAAU;;;KAF9B", "debugId": null}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport {\n  LayoutDashboard,\n  BookOpen,\n  BarChart3,\n  TrendingUp,\n  Upload,\n  Settings,\n  HelpCircle,\n  X,\n  PlusCircle,\n  FileText,\n  DollarSign,\n} from \"lucide-react\"\n\ninterface SidebarProps {\n  isOpen?: boolean\n  onClose?: () => void\n}\n\nconst sidebarItems = [\n  {\n    title: \"Main\",\n    items: [\n      { name: \"Dashboard\", href: \"/dashboard\", icon: LayoutDashboard },\n      { name: \"My Books\", href: \"/dashboard/books\", icon: BookOpen },\n      { name: \"Analytics\", href: \"/dashboard/analytics\", icon: BarChart3 },\n      { name: \"Trends\", href: \"/dashboard/trends\", icon: TrendingUp },\n    ],\n  },\n  {\n    title: \"Publishing\",\n    items: [\n      { name: \"New Book\", href: \"/dashboard/books/new\", icon: PlusCircle },\n      { name: \"Manuscripts\", href: \"/dashboard/manuscripts\", icon: FileText },\n      { name: \"Publications\", href: \"/dashboard/publications\", icon: Upload },\n      { name: \"Revenue\", href: \"/dashboard/revenue\", icon: DollarSign },\n    ],\n  },\n  {\n    title: \"Support\",\n    items: [\n      { name: \"Settings\", href: \"/dashboard/settings\", icon: Settings },\n      { name: \"Help & Docs\", href: \"/dashboard/help\", icon: HelpCircle },\n    ],\n  },\n]\n\nexport function Sidebar({ isOpen = true, onClose }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile backdrop */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black/50 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <aside\n        className={cn(\n          \"fixed inset-y-0 left-0 z-50 w-64 bg-surface transition-transform duration-300 md:relative md:translate-x-0\",\n          isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Mobile header */}\n          <div className=\"flex h-16 items-center justify-between border-b px-4 md:hidden\">\n            <span className=\"font-display text-lg font-semibold\">Menu</span>\n            <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n              <X className=\"h-5 w-5\" />\n              <span className=\"sr-only\">Close sidebar</span>\n            </Button>\n          </div>\n\n          {/* Navigation */}\n          <ScrollArea className=\"flex-1 px-3 py-4\">\n            <nav className=\"space-y-6\">\n              {sidebarItems.map((section) => (\n                <div key={section.title}>\n                  <h3 className=\"mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-muted-foreground\">\n                    {section.title}\n                  </h3>\n                  <div className=\"space-y-1\">\n                    {section.items.map((item) => {\n                      const isActive = pathname === item.href\n                      const Icon = item.icon\n                      return (\n                        <Link\n                          key={item.name}\n                          href={item.href}\n                          className={cn(\n                            \"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors\",\n                            isActive\n                              ? \"bg-primary/10 text-primary\"\n                              : \"text-muted-foreground hover:bg-muted hover:text-foreground\"\n                          )}\n                        >\n                          <Icon className=\"h-5 w-5\" />\n                          {item.name}\n                        </Link>\n                      )\n                    })}\n                  </div>\n                </div>\n              ))}\n            </nav>\n          </ScrollArea>\n\n          {/* Footer */}\n          <div className=\"border-t p-4\">\n            <div className=\"rounded-lg bg-primary/10 p-3\">\n              <h4 className=\"text-sm font-semibold\">Upgrade to Pro</h4>\n              <p className=\"mt-1 text-xs text-muted-foreground\">\n                Unlock advanced features and analytics\n              </p>\n              <Button size=\"sm\" className=\"mt-3 w-full\">\n                Upgrade Now\n              </Button>\n            </div>\n          </div>\n        </div>\n      </aside>\n    </>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AA0BA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;gBAAc,MAAM,+NAAA,CAAA,kBAAe;YAAC;YAC/D;gBAAE,MAAM;gBAAY,MAAM;gBAAoB,MAAM,iNAAA,CAAA,WAAQ;YAAC;YAC7D;gBAAE,MAAM;gBAAa,MAAM;gBAAwB,MAAM,qNAAA,CAAA,YAAS;YAAC;YACnE;gBAAE,MAAM;gBAAU,MAAM;gBAAqB,MAAM,qNAAA,CAAA,aAAU;YAAC;SAC/D;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAwB,MAAM,qNAAA,CAAA,aAAU;YAAC;YACnE;gBAAE,MAAM;gBAAe,MAAM;gBAA0B,MAAM,iNAAA,CAAA,WAAQ;YAAC;YACtE;gBAAE,MAAM;gBAAgB,MAAM;gBAA2B,MAAM,yMAAA,CAAA,SAAM;YAAC;YACtE;gBAAE,MAAM;gBAAW,MAAM;gBAAsB,MAAM,qNAAA,CAAA,aAAU;YAAC;SACjE;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAuB,MAAM,6MAAA,CAAA,WAAQ;YAAC;YAChE;gBAAE,MAAM;gBAAe,MAAM;gBAAmB,MAAM,iOAAA,CAAA,aAAU;YAAC;SAClE;IACH;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,IAAI,EAAE,OAAO,EAAgB;;IAC9D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8GACA,SAAS,kBAAkB;0BAG7B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS;;sDAC3C,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAK9B,6LAAC,6IAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;oDAClB,MAAM,WAAW,aAAa,KAAK,IAAI;oDACvC,MAAM,OAAO,KAAK,IAAI;oDACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,+BACA;;0EAGN,6LAAC;gEAAK,WAAU;;;;;;4DACf,KAAK,IAAI;;uDAVL,KAAK,IAAI;;;;;gDAapB;;;;;;;uCAvBM,QAAQ,KAAK;;;;;;;;;;;;;;;sCA+B7B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GAhFgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 1415, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Container } from \"@/components/ui/container\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { BookOpen, Github, Twitter, Linkedin } from \"lucide-react\"\n\nconst footerLinks = {\n  product: [\n    { name: \"Features\", href: \"#features\" },\n    { name: \"Pricing\", href: \"#pricing\" },\n    { name: \"API\", href: \"/docs/api\" },\n    { name: \"Integrations\", href: \"#integrations\" },\n  ],\n  company: [\n    { name: \"About\", href: \"/about\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Careers\", href: \"/careers\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  resources: [\n    { name: \"Documentation\", href: \"/docs\" },\n    { name: \"Guides\", href: \"/guides\" },\n    { name: \"Support\", href: \"/support\" },\n    { name: \"Status\", href: \"/status\" },\n  ],\n  legal: [\n    { name: \"Privacy\", href: \"/privacy\" },\n    { name: \"Terms\", href: \"/terms\" },\n    { name: \"Cookie Policy\", href: \"/cookies\" },\n    { name: \"License\", href: \"/license\" },\n  ],\n}\n\nconst socialLinks = [\n  { name: \"GitHub\", icon: Github, href: \"https://github.com\" },\n  { name: \"Twitter\", icon: Twitter, href: \"https://twitter.com\" },\n  { name: \"LinkedIn\", icon: Linkedin, href: \"https://linkedin.com\" },\n]\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-surface border-t\">\n      <Container>\n        <div className=\"py-12 md:py-16\">\n          {/* Main footer content */}\n          <div className=\"grid grid-cols-2 gap-8 md:grid-cols-5\">\n            {/* Brand section */}\n            <div className=\"col-span-2 md:col-span-1\">\n              <Link href=\"/\" className=\"flex items-center gap-2 mb-4\">\n                <BookOpen className=\"h-6 w-6 text-primary\" />\n                <span className=\"font-display text-lg font-semibold\">\n                  Publish AI\n                </span>\n              </Link>\n              <p className=\"text-sm text-muted-foreground\">\n                AI-powered book publishing platform for modern authors.\n              </p>\n              {/* Social links */}\n              <div className=\"flex gap-4 mt-6\">\n                {socialLinks.map((link) => {\n                  const Icon = link.icon\n                  return (\n                    <a\n                      key={link.name}\n                      href={link.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      <Icon className=\"h-5 w-5\" />\n                      <span className=\"sr-only\">{link.name}</span>\n                    </a>\n                  )\n                })}\n              </div>\n            </div>\n\n            {/* Links sections */}\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          <Separator className=\"my-8\" />\n\n          {/* Bottom section */}\n          <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\n            <p className=\"text-sm text-muted-foreground\">\n              © {new Date().getFullYear()} Publish AI. All rights reserved.\n            </p>\n            <p className=\"text-sm text-muted-foreground\">\n              Made with ❤️ by the Publish AI team\n            </p>\n          </div>\n        </div>\n      </Container>\n    </footer>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAO,MAAM;QAAY;QACjC;YAAE,MAAM;YAAgB,MAAM;QAAgB;KAC/C;IACD,SAAS;QACP;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,WAAW;QACT;YAAE,MAAM;YAAiB,MAAM;QAAQ;QACvC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAU,MAAM;QAAU;KACnC;IACD,OAAO;QACL;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAU,MAAM,yMAAA,CAAA,SAAM;QAAE,MAAM;IAAqB;IAC3D;QAAE,MAAM;QAAW,MAAM,2MAAA,CAAA,UAAO;QAAE,MAAM;IAAsB;IAC9D;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;QAAE,MAAM;IAAuB;CAClE;AAEM,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC,wIAAA,CAAA,YAAS;sBACR,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAIvD,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,OAAO,KAAK,IAAI;4CACtB,qBACE,6LAAC;gDAEC,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAW,KAAK,IAAI;;;;;;;+CAP/B,KAAK,IAAI;;;;;wCAUpB;;;;;;;;;;;;0CAKJ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAa1B,6LAAC,wIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCAGrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAgC;oCACxC,IAAI,OAAO,WAAW;oCAAG;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD;KAvHgB", "debugId": null}}, {"offset": {"line": 1872, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border shadow-sm hover:shadow-md\",\n        elevated: \"border-border shadow-md hover:shadow-lg\",\n        outlined: \"border-border shadow-none hover:shadow-sm\",\n        ghost: \"border-transparent shadow-none hover:bg-surface-100\",\n        success: \"border-success bg-green-50 shadow-sm\",\n        warning: \"border-warning bg-yellow-50 shadow-sm\",\n        error: \"border-error bg-red-50 shadow-sm\",\n        info: \"border-info bg-blue-50 shadow-sm\",\n      },\n      size: {\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n      hoverable: {\n        true: \"cursor-pointer hover:scale-[1.02] active:scale-[0.98]\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      hoverable: false,\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, size, hoverable, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, size, hoverable }), className)}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-2\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-tight tracking-tight text-accent\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground leading-relaxed\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"pt-4\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center pt-4\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,8EACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;YACP,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,WAAW;YACT,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,WAAW;IACb;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClD,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAM;QAAU,IAAI;QACzD,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;;AAE5D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/error-boundary.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport { <PERSON>ert<PERSON>riangle } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from \"@/components/ui/card\"\n\ninterface Props {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; reset: () => void }>\n}\n\ninterface State {\n  hasError: boolean\n  error: Error | null\n}\n\nexport class ErrorBoundary extends React.Component<Props, State> {\n  constructor(props: Props) {\n    super(props)\n    this.state = { hasError: false, error: null }\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error(\"ErrorBoundary caught an error:\", error, errorInfo)\n    \n    // Log to monitoring service in production\n    if (process.env.NODE_ENV === 'production') {\n      this.logError(error, errorInfo)\n    }\n  }\n\n  private logError = async (error: Error, errorInfo: React.ErrorInfo) => {\n    try {\n      const errorReport = {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        errorId: crypto.randomUUID(),\n        timestamp: new Date().toISOString(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n      }\n      \n      // Send to backend error reporting endpoint\n      await fetch('/api/errors/report', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(errorReport),\n      })\n    } catch (reportingError) {\n      console.error('Failed to report error:', reportingError)\n    }\n  }\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: null })\n  }\n\n  render() {\n    if (this.state.hasError && this.state.error) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return (\n          <FallbackComponent\n            error={this.state.error}\n            reset={this.handleReset}\n          />\n        )\n      }\n\n      return <DefaultErrorFallback error={this.state.error} reset={this.handleReset} />\n    }\n\n    return this.props.children\n  }\n}\n\ninterface ErrorFallbackProps {\n  error: Error\n  reset: () => void\n}\n\nexport function DefaultErrorFallback({ error, reset }: ErrorFallbackProps) {\n  return (\n    <div className=\"flex items-center justify-center min-h-[400px] p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader>\n          <div className=\"flex items-center gap-2\">\n            <AlertTriangle className=\"h-5 w-5 text-destructive\" />\n            <CardTitle>Something went wrong</CardTitle>\n          </div>\n          <CardDescription>\n            An unexpected error occurred. Please try again.\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <details className=\"rounded-lg bg-muted p-3\">\n            <summary className=\"cursor-pointer text-sm font-medium\">\n              Error details\n            </summary>\n            <pre className=\"mt-2 whitespace-pre-wrap text-xs text-muted-foreground\">\n              {error.message}\n            </pre>\n          </details>\n        </CardContent>\n        <CardFooter className=\"flex gap-2\">\n          <Button onClick={reset}>Try again</Button>\n          <Button\n            variant=\"outline\"\n            onClick={() => window.location.href = \"/dashboard\"}\n          >\n            Go to Dashboard\n          </Button>\n        </CardFooter>\n      </Card>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AA+BQ;;AA7BR;AACA;AACA;AACA;AALA;;;;;;AAiBO,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;YAAO,OAAO;QAAK;IAC9C;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,0CAA0C;QAC1C,uCAA2C;;QAE3C;IACF;IAEQ,WAAW,OAAO,OAAc;QACtC,IAAI;YACF,MAAM,cAAc;gBAClB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;gBAClB,gBAAgB,UAAU,cAAc;gBACxC,SAAS,OAAO,UAAU;gBAC1B,WAAW,IAAI,OAAO,WAAW;gBACjC,KAAK,OAAO,QAAQ,CAAC,IAAI;gBACzB,WAAW,UAAU,SAAS;YAChC;YAEA,2CAA2C;YAC3C,MAAM,MAAM,sBAAsB;gBAChC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;QACF,EAAE,OAAO,gBAAgB;YACvB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF,EAAC;IAED,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAK;IAC/C,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBACE,6LAAC;oBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBACvB,OAAO,IAAI,CAAC,WAAW;;;;;;YAG7B;YAEA,qBAAO,6LAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC,WAAW;;;;;;QAC/E;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAOO,SAAS,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAsB;IACvE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAQ,WAAU;0CAAqC;;;;;;0CAGxD,6LAAC;gCAAI,WAAU;0CACZ,MAAM,OAAO;;;;;;;;;;;;;;;;;8BAIpB,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;sCAAO;;;;;;sCACxB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sCACvC;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAnCgB", "debugId": null}}, {"offset": {"line": 2225, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Head<PERSON> } from \"./header\"\nimport { Sidebar } from \"./sidebar\"\nimport { Footer } from \"./footer\"\nimport { ErrorBoundary } from \"./error-boundary\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  className?: string\n  showFooter?: boolean\n}\n\nexport function DashboardLayout({ \n  children, \n  className,\n  showFooter = false\n}: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header \n        onMenuClick={() => setSidebarOpen(!sidebarOpen)} \n      />\n      \n      <div className=\"flex h-[calc(100vh-4rem)]\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <main className={cn(\n          \"flex-1 overflow-y-auto\",\n          className\n        )}>\n          <ErrorBoundary>\n            {children}\n          </ErrorBoundary>\n        </main>\n      </div>\n      \n      {showFooter && <Footer />}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAeO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,aAAa,KAAK,EACG;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;gBACL,aAAa,IAAM,eAAe,CAAC;;;;;;0BAGrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0IAAA,CAAA,UAAO;wBACN,QAAQ;wBACR,SAAS,IAAM,eAAe;;;;;;kCAGhC,6LAAC;wBAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,0BACA;kCAEA,cAAA,6LAAC,oJAAA,CAAA,gBAAa;sCACX;;;;;;;;;;;;;;;;;YAKN,4BAAc,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAG5B;GAhCgB;KAAA", "debugId": null}}, {"offset": {"line": 2313, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/breadcrumb.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { ChevronRight, Home } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BreadcrumbItem {\n  label: string\n  href?: string\n}\n\ninterface BreadcrumbProps {\n  items?: BreadcrumbItem[]\n  className?: string\n}\n\nexport function Breadcrumb({ items, className }: BreadcrumbProps) {\n  const pathname = usePathname()\n  \n  // Auto-generate breadcrumbs from pathname if items not provided\n  const breadcrumbItems = items || generateBreadcrumbs(pathname)\n\n  if (breadcrumbItems.length === 0) return null\n\n  return (\n    <nav \n      aria-label=\"Breadcrumb\"\n      className={cn(\"flex items-center space-x-1 text-sm\", className)}\n    >\n      <Link\n        href=\"/dashboard\"\n        className=\"flex items-center text-muted-foreground hover:text-foreground transition-colors\"\n      >\n        <Home className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Home</span>\n      </Link>\n      \n      {breadcrumbItems.map((item, index) => {\n        const isLast = index === breadcrumbItems.length - 1\n        \n        return (\n          <div key={index} className=\"flex items-center\">\n            <ChevronRight className=\"h-4 w-4 text-muted-foreground mx-1\" />\n            {isLast || !item.href ? (\n              <span className=\"font-medium text-foreground\">\n                {item.label}\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"text-muted-foreground hover:text-foreground transition-colors\"\n              >\n                {item.label}\n              </Link>\n            )}\n          </div>\n        )\n      })}\n    </nav>\n  )\n}\n\nfunction generateBreadcrumbs(pathname: string): BreadcrumbItem[] {\n  const segments = pathname.split(\"/\").filter(Boolean)\n  const breadcrumbs: BreadcrumbItem[] = []\n  \n  // Skip the first segment if it's \"dashboard\"\n  const startIndex = segments[0] === \"dashboard\" ? 1 : 0\n  \n  segments.slice(startIndex).forEach((segment, index) => {\n    const href = \"/\" + segments.slice(0, startIndex + index + 1).join(\"/\")\n    const label = segment\n      .split(\"-\")\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(\" \")\n    \n    breadcrumbs.push({ label, href })\n  })\n  \n  return breadcrumbs\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAiBO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;;IAC9D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,kBAAkB,SAAS,oBAAoB;IAErD,IAAI,gBAAgB,MAAM,KAAK,GAAG,OAAO;IAEzC,qBACE,6LAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;;0BAErD,6LAAC,+JAAA,CAAA,UAAI;gBACH,MAAK;gBACL,WAAU;;kCAEV,6LAAC,sMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;YAG3B,gBAAgB,GAAG,CAAC,CAAC,MAAM;gBAC1B,MAAM,SAAS,UAAU,gBAAgB,MAAM,GAAG;gBAElD,qBACE,6LAAC;oBAAgB,WAAU;;sCACzB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBACvB,UAAU,CAAC,KAAK,IAAI,iBACnB,6LAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;iDAGb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,KAAK;;;;;;;mBAXP;;;;;YAgBd;;;;;;;AAGN;GA5CgB;;QACG,qIAAA,CAAA,cAAW;;;KADd;AA8ChB,SAAS,oBAAoB,QAAgB;IAC3C,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAgC,EAAE;IAExC,6CAA6C;IAC7C,MAAM,aAAa,QAAQ,CAAC,EAAE,KAAK,cAAc,IAAI;IAErD,SAAS,KAAK,CAAC,YAAY,OAAO,CAAC,CAAC,SAAS;QAC3C,MAAM,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,aAAa,QAAQ,GAAG,IAAI,CAAC;QAClE,MAAM,QAAQ,QACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAER,YAAY,IAAI,CAAC;YAAE;YAAO;QAAK;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2438, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/typography.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nconst headingVariants = cva(\n  \"font-semibold text-accent tracking-tight\",\n  {\n    variants: {\n      variant: {\n        h1: \"text-4xl md:text-5xl lg:text-6xl\",\n        h2: \"text-3xl md:text-4xl lg:text-5xl\",\n        h3: \"text-2xl md:text-3xl lg:text-4xl\",\n        h4: \"text-xl md:text-2xl lg:text-3xl\",\n        h5: \"text-lg md:text-xl lg:text-2xl\",\n        h6: \"text-base md:text-lg lg:text-xl\",\n      },\n    },\n    defaultVariants: {\n      variant: \"h1\",\n    },\n  }\n)\n\nconst textVariants = cva(\n  \"\",\n  {\n    variants: {\n      variant: {\n        body: \"text-base text-foreground\",\n        lead: \"text-lg text-muted-foreground\",\n        small: \"text-sm text-foreground\",\n        muted: \"text-sm text-muted-foreground\",\n        caption: \"text-xs text-muted-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"body\",\n    },\n  }\n)\n\nexport interface HeadingProps\n  extends HTMLAttributes<HTMLHeadingElement>,\n    VariantProps<typeof headingVariants> {\n  as?: \"h1\" | \"h2\" | \"h3\" | \"h4\" | \"h5\" | \"h6\"\n}\n\nexport const Heading = forwardRef<HTMLHeadingElement, HeadingProps>(\n  ({ className, variant, as, ...props }, ref) => {\n    const Comp = as || variant || \"h1\"\n    return (\n      <Comp\n        className={cn(headingVariants({ variant: variant || as }), className)}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nHeading.displayName = \"Heading\"\n\nexport interface TextProps\n  extends HTMLAttributes<HTMLParagraphElement>,\n    VariantProps<typeof textVariants> {\n  as?: \"p\" | \"span\" | \"div\"\n}\n\nexport const Text = forwardRef<HTMLParagraphElement, TextProps>(\n  ({ className, variant, as: Comp = \"p\", ...props }, ref) => {\n    return (\n      <Comp\n        className={cn(textVariants({ variant }), className)}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nText.displayName = \"Text\""], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,kBAAkB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACxB,4CACA;IACE,UAAU;QACR,SAAS;YACP,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,IACA;IACE,UAAU;QACR,SAAS;YACP,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AASK,MAAM,wBAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IACrC,MAAM,OAAO,MAAM,WAAW;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;YAAE,SAAS,WAAW;QAAG,IAAI;QAC3D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,QAAQ,WAAW,GAAG;AAQf,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE;IACjD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;QAAQ,IAAI;QACzC,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,KAAK,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2524, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/page-header.tsx"], "sourcesContent": ["import { ReactNode } from \"react\"\nimport { Breadcrumb } from \"./breadcrumb\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { cn } from \"@/lib/utils\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  breadcrumb?: boolean\n  actions?: ReactNode\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  breadcrumb = true,\n  actions,\n  className,\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 pb-6\", className)}>\n      {breadcrumb && <Breadcrumb />}\n      \n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between\">\n        <div className=\"space-y-1\">\n          <Heading as=\"h1\" variant=\"h3\">\n            {title}\n          </Heading>\n          {description && (\n            <Text variant=\"muted\" className=\"max-w-2xl\">\n              {description}\n            </Text>\n          )}\n        </div>\n        \n        {actions && (\n          <div className=\"flex items-center gap-2 flex-shrink-0\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAUO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,aAAa,IAAI,EACjB,OAAO,EACP,SAAS,EACO;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAClC,4BAAc,6LAAC,6IAAA,CAAA,aAAU;;;;;0BAE1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,UAAO;gCAAC,IAAG;gCAAK,SAAQ;0CACtB;;;;;;4BAEF,6BACC,6LAAC,yIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAQ,WAAU;0CAC7B;;;;;;;;;;;;oBAKN,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;KA/BgB", "debugId": null}}, {"offset": {"line": 2607, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { Loader2 } from \"lucide-react\"\n\nexport interface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n}\n\nconst sizeClasses = {\n  sm: \"h-4 w-4\",\n  md: \"h-6 w-6\",\n  lg: \"h-8 w-8\",\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  return (\n    <Loader2\n      className={cn(\n        \"animate-spin text-primary\",\n        sizeClasses[size],\n        className\n      )}\n    />\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,6LAAC,oNAAA,CAAA,UAAO;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;KAVgB", "debugId": null}}, {"offset": {"line": 2642, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/loading.tsx"], "sourcesContent": ["import { LoadingSpinner } from \"@/components/ui/loading-spinner\"\n\ninterface LoadingProps {\n  fullScreen?: boolean\n  message?: string\n}\n\nexport function Loading({ fullScreen = false, message }: LoadingProps) {\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50\">\n        <div className=\"flex flex-col items-center gap-4\">\n          <LoadingSpinner size=\"lg\" />\n          {message && (\n            <p className=\"text-sm text-muted-foreground animate-pulse\">\n              {message}\n            </p>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center justify-center py-12\">\n      <div className=\"flex flex-col items-center gap-4\">\n        <LoadingSpinner size=\"md\" />\n        {message && (\n          <p className=\"text-sm text-muted-foreground animate-pulse\">\n            {message}\n          </p>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,QAAQ,EAAE,aAAa,KAAK,EAAE,OAAO,EAAgB;IACnE,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,iBAAc;wBAAC,MAAK;;;;;;oBACpB,yBACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;IAMb;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,iJAAA,CAAA,iBAAc;oBAAC,MAAK;;;;;;gBACpB,yBACC,6LAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;;;;;;;AAMb;KA5BgB", "debugId": null}}, {"offset": {"line": 2727, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/auth/protected-route.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession } from \"next-auth/react\"\nimport { useRouter } from \"next/navigation\"\nimport { useEffect } from \"react\"\nimport { Loading } from \"@/components/layout/loading\"\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({ \n  children, \n  redirectTo = \"/auth/login\" \n}: ProtectedRouteProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(redirectTo)\n    }\n  }, [status, router, redirectTo])\n\n  if (status === \"loading\") {\n    return <Loading fullScreen message=\"Checking authentication...\" />\n  }\n\n  if (status === \"unauthenticated\") {\n    return null\n  }\n\n  return <>{children}</>\n}\n\nexport function PublicRoute({ \n  children, \n  redirectTo = \"/dashboard\" \n}: ProtectedRouteProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      router.push(redirectTo)\n    }\n  }, [status, router, redirectTo])\n\n  if (status === \"loading\") {\n    return <Loading fullScreen message=\"Checking authentication...\" />\n  }\n\n  if (status === \"authenticated\") {\n    return null\n  }\n\n  return <>{children}</>\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,eAAe,EAC7B,QAAQ,EACR,aAAa,aAAa,EACN;;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW,mBAAmB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAQ;QAAQ;KAAW;IAE/B,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC,0IAAA,CAAA,UAAO;YAAC,UAAU;YAAC,SAAQ;;;;;;IACrC;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GAtBgB;;QAIoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;KALV;AAwBT,SAAS,YAAY,EAC1B,QAAQ,EACR,aAAa,YAAY,EACL;;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW,iBAAiB;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF;gCAAG;QAAC;QAAQ;QAAQ;KAAW;IAE/B,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC,0IAAA,CAAA,UAAO;YAAC,UAAU;YAAC,SAAQ;;;;;;IACrC;IAEA,IAAI,WAAW,iBAAiB;QAC9B,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;IAtBgB;;QAIoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;MALV", "debugId": null}}, {"offset": {"line": 2833, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst progressVariants = cva(\n  \"relative h-2 w-full overflow-hidden rounded-full bg-secondary\",\n  {\n    variants: {\n      size: {\n        sm: \"h-1\",\n        default: \"h-2\",\n        lg: \"h-3\",\n        xl: \"h-4\",\n      },\n      variant: {\n        default: \"bg-secondary\",\n        success: \"bg-green-100\",\n        warning: \"bg-yellow-100\", \n        error: \"bg-red-100\",\n        info: \"bg-blue-100\",\n      }\n    },\n    defaultVariants: {\n      size: \"default\",\n      variant: \"default\",\n    },\n  }\n)\n\nconst progressIndicatorVariants = cva(\n  \"h-full w-full flex-1 bg-primary transition-all duration-300 ease-in-out\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary\",\n        success: \"bg-green-500\",\n        warning: \"bg-yellow-500\",\n        error: \"bg-red-500\", \n        info: \"bg-blue-500\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface ProgressProps\n  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,\n    VariantProps<typeof progressVariants> {\n  /** Show percentage text */\n  showPercentage?: boolean\n  /** Custom label */\n  label?: string\n  /** Position of label/percentage */\n  labelPosition?: \"top\" | \"bottom\" | \"inline\"\n}\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  ProgressProps\n>(({ className, value, size, variant, showPercentage, label, labelPosition = \"top\", ...props }, ref) => {\n  const percentage = value || 0\n\n  return (\n    <div className=\"w-full space-y-1\">\n      {/* Top label/percentage */}\n      {(label || showPercentage) && labelPosition === \"top\" && (\n        <div className=\"flex justify-between text-sm\">\n          {label && <span className=\"text-foreground font-medium\">{label}</span>}\n          {showPercentage && <span className=\"text-muted-foreground\">{Math.round(percentage)}%</span>}\n        </div>\n      )}\n      \n      {/* Progress bar with inline label */}\n      <div className=\"relative\">\n        <ProgressPrimitive.Root\n          ref={ref}\n          className={cn(progressVariants({ size, variant }), className)}\n          {...props}\n        >\n          <ProgressPrimitive.Indicator\n            className={cn(progressIndicatorVariants({ variant }))}\n            style={{ transform: `translateX(-${100 - percentage}%)` }}\n          />\n        </ProgressPrimitive.Root>\n        \n        {/* Inline percentage */}\n        {showPercentage && labelPosition === \"inline\" && (\n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            <span className=\"text-xs font-medium text-white mix-blend-difference\">\n              {Math.round(percentage)}%\n            </span>\n          </div>\n        )}\n      </div>\n      \n      {/* Bottom label/percentage */}\n      {(label || showPercentage) && labelPosition === \"bottom\" && (\n        <div className=\"flex justify-between text-sm\">\n          {label && <span className=\"text-foreground font-medium\">{label}</span>}\n          {showPercentage && <span className=\"text-muted-foreground\">{Math.round(percentage)}%</span>}\n        </div>\n      )}\n    </div>\n  )\n})\nProgress.displayName = ProgressPrimitive.Root.displayName\n\n// Spinner component\nconst spinnerVariants = cva(\n  \"animate-spin rounded-full border-2 border-current border-t-transparent\",\n  {\n    variants: {\n      size: {\n        sm: \"h-4 w-4\",\n        default: \"h-6 w-6\", \n        lg: \"h-8 w-8\",\n        xl: \"h-12 w-12\",\n      },\n      variant: {\n        default: \"text-primary\",\n        secondary: \"text-secondary\",\n        success: \"text-green-500\",\n        warning: \"text-yellow-500\",\n        error: \"text-red-500\",\n        info: \"text-blue-500\",\n        muted: \"text-muted-foreground\",\n      }\n    },\n    defaultVariants: {\n      size: \"default\",\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface SpinnerProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof spinnerVariants> {\n  /** Loading text to display */\n  text?: string\n  /** Position of text relative to spinner */\n  textPosition?: \"right\" | \"bottom\"\n}\n\nconst Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(\n  ({ className, size, variant, text, textPosition = \"right\", ...props }, ref) => {\n    if (text) {\n      return (\n        <div \n          ref={ref}\n          className={cn(\n            \"flex items-center gap-3\",\n            textPosition === \"bottom\" && \"flex-col gap-2\",\n            className\n          )}\n          {...props}\n        >\n          <div className={cn(spinnerVariants({ size, variant }))} />\n          <span className=\"text-sm text-muted-foreground\">{text}</span>\n        </div>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(spinnerVariants({ size, variant }), className)}\n        {...props}\n      />\n    )\n  }\n)\nSpinner.displayName = \"Spinner\"\n\n// Circular progress component\nexport interface CircularProgressProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** Progress value (0-100) */\n  value: number\n  /** Size of the circle */\n  size?: number\n  /** Stroke width */\n  strokeWidth?: number\n  /** Color variant */\n  variant?: \"default\" | \"success\" | \"warning\" | \"error\" | \"info\"\n  /** Show percentage in center */\n  showPercentage?: boolean\n  /** Custom content in center */\n  children?: React.ReactNode\n}\n\nconst CircularProgress = React.forwardRef<HTMLDivElement, CircularProgressProps>(\n  ({ \n    className, \n    value, \n    size = 120, \n    strokeWidth = 8, \n    variant = \"default\", \n    showPercentage = false,\n    children,\n    ...props \n  }, ref) => {\n    const normalizedValue = Math.min(Math.max(value, 0), 100)\n    const radius = (size - strokeWidth) / 2\n    const circumference = radius * 2 * Math.PI\n    const strokeDasharray = circumference\n    const strokeDashoffset = circumference - (normalizedValue / 100) * circumference\n\n    const getColor = () => {\n      switch (variant) {\n        case \"success\": return \"#10b981\"\n        case \"warning\": return \"#f59e0b\" \n        case \"error\": return \"#ef4444\"\n        case \"info\": return \"#3b82f6\"\n        default: return \"#FF385C\" // primary color\n      }\n    }\n\n    return (\n      <div \n        ref={ref}\n        className={cn(\"relative inline-flex items-center justify-center\", className)}\n        style={{ width: size, height: size }}\n        {...props}\n      >\n        <svg\n          width={size}\n          height={size}\n          className=\"transform -rotate-90\"\n        >\n          {/* Background circle */}\n          <circle\n            cx={size / 2}\n            cy={size / 2}\n            r={radius}\n            stroke=\"currentColor\"\n            strokeWidth={strokeWidth}\n            fill=\"none\"\n            className=\"text-secondary opacity-25\"\n          />\n          {/* Progress circle */}\n          <circle\n            cx={size / 2}\n            cy={size / 2}\n            r={radius}\n            stroke={getColor()}\n            strokeWidth={strokeWidth}\n            fill=\"none\"\n            strokeLinecap=\"round\"\n            strokeDasharray={strokeDasharray}\n            strokeDashoffset={strokeDashoffset}\n            className=\"transition-all duration-300 ease-in-out\"\n          />\n        </svg>\n        \n        {/* Center content */}\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          {children || (showPercentage && (\n            <span className=\"text-sm font-semibold text-foreground\">\n              {Math.round(normalizedValue)}%\n            </span>\n          ))}\n        </div>\n      </div>\n    )\n  }\n)\nCircularProgress.displayName = \"CircularProgress\"\n\nexport { Progress, Spinner, CircularProgress, progressVariants, spinnerVariants }\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,mBAAmB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACzB,iEACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;QACA,SAAS;YACP,SAAS;YACT,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AAGF,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,2EACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAcF,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,gBAAgB,KAAK,EAAE,GAAG,OAAO,EAAE;IAC9F,MAAM,aAAa,SAAS;IAE5B,qBACE,6LAAC;QAAI,WAAU;;YAEZ,CAAC,SAAS,cAAc,KAAK,kBAAkB,uBAC9C,6LAAC;gBAAI,WAAU;;oBACZ,uBAAS,6LAAC;wBAAK,WAAU;kCAA+B;;;;;;oBACxD,gCAAkB,6LAAC;wBAAK,WAAU;;4BAAyB,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAKvF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uKAAA,CAAA,OAAsB;wBACrB,KAAK;wBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;4BAAE;4BAAM;wBAAQ,IAAI;wBAClD,GAAG,KAAK;kCAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;4BAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;gCAAE;4BAAQ;4BAClD,OAAO;gCAAE,WAAW,CAAC,YAAY,EAAE,MAAM,WAAW,EAAE,CAAC;4BAAC;;;;;;;;;;;oBAK3D,kBAAkB,kBAAkB,0BACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;gCACb,KAAK,KAAK,CAAC;gCAAY;;;;;;;;;;;;;;;;;;YAO/B,CAAC,SAAS,cAAc,KAAK,kBAAkB,0BAC9C,6LAAC;gBAAI,WAAU;;oBACZ,uBAAS,6LAAC;wBAAK,WAAU;kCAA+B;;;;;;oBACxD,gCAAkB,6LAAC;wBAAK,WAAU;;4BAAyB,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;;;;;;;AAK7F;;AACA,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW;AAEzD,oBAAoB;AACpB,MAAM,kBAAkB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACxB,0EACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;QACA,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AAYF,MAAM,wBAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAC7B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,eAAe,OAAO,EAAE,GAAG,OAAO,EAAE;IACrE,IAAI,MAAM;QACR,qBACE,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2BACA,iBAAiB,YAAY,kBAC7B;YAED,GAAG,KAAK;;8BAET,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;wBAAE;wBAAM;oBAAQ;;;;;;8BACnD,6LAAC;oBAAK,WAAU;8BAAiC;;;;;;;;;;;;IAGvD;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;YAAE;YAAM;QAAQ,IAAI;QACjD,GAAG,KAAK;;;;;;AAGf;;AAEF,QAAQ,WAAW,GAAG;AAkBtB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QACtC,CAAC,EACC,SAAS,EACT,KAAK,EACL,OAAO,GAAG,EACV,cAAc,CAAC,EACf,UAAU,SAAS,EACnB,iBAAiB,KAAK,EACtB,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,kBAAkB,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,IAAI;IACrD,MAAM,SAAS,CAAC,OAAO,WAAW,IAAI;IACtC,MAAM,gBAAgB,SAAS,IAAI,KAAK,EAAE;IAC1C,MAAM,kBAAkB;IACxB,MAAM,mBAAmB,gBAAgB,AAAC,kBAAkB,MAAO;IAEnE,MAAM,WAAW;QACf,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO,UAAU,gBAAgB;;QAC5C;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;QAClE,OAAO;YAAE,OAAO;YAAM,QAAQ;QAAK;QAClC,GAAG,KAAK;;0BAET,6LAAC;gBACC,OAAO;gBACP,QAAQ;gBACR,WAAU;;kCAGV,6LAAC;wBACC,IAAI,OAAO;wBACX,IAAI,OAAO;wBACX,GAAG;wBACH,QAAO;wBACP,aAAa;wBACb,MAAK;wBACL,WAAU;;;;;;kCAGZ,6LAAC;wBACC,IAAI,OAAO;wBACX,IAAI,OAAO;wBACX,GAAG;wBACH,QAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,eAAc;wBACd,iBAAiB;wBACjB,kBAAkB;wBAClB,WAAU;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACZ,YAAa,gCACZ,6LAAC;oBAAK,WAAU;;wBACb,KAAK,KAAK,CAAC;wBAAiB;;;;;;;;;;;;;;;;;;AAMzC;;AAEF,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3192, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center gap-1 rounded-full border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-surface-100 text-accent hover:bg-surface-200\",\n        secondary: \"border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n        info: \"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200\",\n        primary: \"border-transparent bg-primary-100 text-primary-900 hover:bg-primary-200\",\n        outline: \"border-border text-accent hover:bg-surface-50\",\n        ghost: \"border-transparent hover:bg-surface-100 text-accent\",\n      },\n      size: {\n        sm: \"px-2 py-0.5 text-xs\",\n        default: \"px-2.5 py-1 text-sm\",\n        lg: \"px-3 py-1.5 text-base\",\n      },\n      dot: {\n        true: \"\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      dot: false,\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {\n  /** Show a colored dot indicator */\n  dot?: boolean\n  /** Icon to display before the text */\n  icon?: React.ReactNode\n  /** Make the badge clickable */\n  onClick?: () => void\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, dot, icon, onClick, children, ...props }, ref) => {\n    const isClickable = !!onClick\n    \n    const getDotColor = () => {\n      switch (variant) {\n        case 'success': return 'bg-green-500'\n        case 'warning': return 'bg-yellow-500'\n        case 'error': return 'bg-red-500'\n        case 'info': return 'bg-blue-500'\n        case 'primary': return 'bg-primary'\n        case 'secondary': return 'bg-secondary'\n        default: return 'bg-accent'\n      }\n    }\n\n    if (isClickable) {\n      return (\n        <button\n          ref={ref as any}\n          type=\"button\"\n          className={cn(\n            badgeVariants({ variant, size }),\n            \"cursor-pointer hover:scale-105 active:scale-95\",\n            className\n          )}\n          onClick={onClick}\n          {...(props as React.ButtonHTMLAttributes<HTMLButtonElement>)}\n        >\n          {dot && (\n            <span\n              className={cn(\n                \"h-1.5 w-1.5 rounded-full\",\n                getDotColor()\n              )}\n            />\n          )}\n          \n          {icon && (\n            <span className=\"shrink-0\">\n              {icon}\n            </span>\n          )}\n          \n          {children}\n        </button>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...(props as React.HTMLAttributes<HTMLDivElement>)}\n      >\n        {dot && (\n          <span\n            className={cn(\n              \"h-1.5 w-1.5 rounded-full\",\n              getDotColor()\n            )}\n          />\n        )}\n        \n        {icon && (\n          <span className=\"shrink-0\">\n            {icon}\n          </span>\n        )}\n        \n        {children}\n      </div>\n    )\n  }\n)\nBadge.displayName = \"Badge\"\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,iKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,KAAK;YACH,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,KAAK;IACP;AACF;AAcF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrE,MAAM,cAAc,CAAC,CAAC;IAEtB,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YACC,KAAK;YACL,MAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;gBAAE;gBAAS;YAAK,IAC9B,kDACA;YAEF,SAAS;YACR,GAAI,KAAK;;gBAET,qBACC,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;gBAKL,sBACC,6LAAC;oBAAK,WAAU;8BACb;;;;;;gBAIJ;;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAI,KAAK;;YAET,qBACC,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;YAKL,sBACC,6LAAC;gBAAK,WAAU;0BACb;;;;;;YAIJ;;;;;;;AAGP;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3333, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst inputVariants = cva(\n  \"flex w-full rounded-lg border bg-background px-4 py-3 text-sm font-medium transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border hover:border-secondary-400 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20\",\n        error: \"border-error bg-red-50 focus-visible:border-error focus-visible:ring-2 focus-visible:ring-error/20\",\n        success: \"border-success bg-green-50 focus-visible:border-success focus-visible:ring-2 focus-visible:ring-success/20\",\n        warning: \"border-warning bg-yellow-50 focus-visible:border-warning focus-visible:ring-2 focus-visible:ring-warning/20\",\n      },\n      size: {\n        sm: \"h-8 px-3 py-1.5 text-xs\",\n        default: \"h-10 px-4 py-2.5 text-sm\",\n        lg: \"h-12 px-4 py-3 text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface InputProps\n  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,\n    VariantProps<typeof inputVariants> {\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  error?: string\n  success?: string\n  warning?: string\n  helperText?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ \n    className, \n    variant, \n    size, \n    type, \n    leftIcon, \n    rightIcon, \n    error, \n    success, \n    warning, \n    helperText,\n    ...props \n  }, ref) => {\n    // Determine variant based on validation states\n    const currentVariant = error ? 'error' : success ? 'success' : warning ? 'warning' : variant\n\n    const input = (\n      <input\n        type={type}\n        className={cn(\n          inputVariants({ variant: currentVariant, size }),\n          leftIcon && \"pl-10\",\n          rightIcon && \"pr-10\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n\n    // If no icons, return simple input\n    if (!leftIcon && !rightIcon) {\n      return (\n        <div className=\"w-full\">\n          {input}\n          {(error || success || warning || helperText) && (\n            <div className=\"mt-1.5 text-xs\">\n              {error && <p className=\"text-error flex items-center gap-1\">{error}</p>}\n              {success && <p className=\"text-success flex items-center gap-1\">{success}</p>}\n              {warning && <p className=\"text-warning flex items-center gap-1\">{warning}</p>}\n              {helperText && !error && !success && !warning && (\n                <p className=\"text-muted-foreground\">{helperText}</p>\n              )}\n            </div>\n          )}\n        </div>\n      )\n    }\n\n    // Return input with icon wrapper\n    return (\n      <div className=\"w-full\">\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground\">\n              {leftIcon}\n            </div>\n          )}\n          {input}\n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        {(error || success || warning || helperText) && (\n          <div className=\"mt-1.5 text-xs\">\n            {error && <p className=\"text-error flex items-center gap-1\">{error}</p>}\n            {success && <p className=\"text-success flex items-center gap-1\">{success}</p>}\n            {warning && <p className=\"text-warning flex items-center gap-1\">{warning}</p>}\n            {helperText && !error && !success && !warning && (\n              <p className=\"text-muted-foreground\">{helperText}</p>\n            )}\n          </div>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input, inputVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,2SACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAcF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,UAAU,EACV,GAAG,OACJ,EAAE;IACD,+CAA+C;IAC/C,MAAM,iBAAiB,QAAQ,UAAU,UAAU,YAAY,UAAU,YAAY;IAErF,MAAM,sBACJ,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;YAAE,SAAS;YAAgB;QAAK,IAC9C,YAAY,SACZ,aAAa,SACb;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;IAIb,mCAAmC;IACnC,IAAI,CAAC,YAAY,CAAC,WAAW;QAC3B,qBACE,6LAAC;YAAI,WAAU;;gBACZ;gBACA,CAAC,SAAS,WAAW,WAAW,UAAU,mBACzC,6LAAC;oBAAI,WAAU;;wBACZ,uBAAS,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAC5D,yBAAW,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;wBAChE,yBAAW,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;wBAChE,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,yBACpC,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;IAMlD;IAEA,iCAAiC;IACjC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;oBAGJ;oBACA,2BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAIN,CAAC,SAAS,WAAW,WAAW,UAAU,mBACzC,6LAAC;gBAAI,WAAU;;oBACZ,uBAAS,6LAAC;wBAAE,WAAU;kCAAsC;;;;;;oBAC5D,yBAAW,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;oBAChE,yBAAW,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;oBAChE,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,yBACpC,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;AAMlD;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3527, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3566, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst textareaVariants = cva(\n  \"flex w-full rounded-lg border bg-background px-4 py-3 text-sm font-medium transition-all duration-200 placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 resize-none\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border hover:border-secondary-400 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20\",\n        error: \"border-error bg-red-50 focus-visible:border-error focus-visible:ring-2 focus-visible:ring-error/20\",\n        success: \"border-success bg-green-50 focus-visible:border-success focus-visible:ring-2 focus-visible:ring-success/20\",\n        warning: \"border-warning bg-yellow-50 focus-visible:border-warning focus-visible:ring-2 focus-visible:ring-warning/20\",\n      },\n      size: {\n        sm: \"min-h-[80px] text-xs\",\n        default: \"min-h-[100px] text-sm\",\n        lg: \"min-h-[120px] text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,\n    VariantProps<typeof textareaVariants> {\n  error?: string\n  success?: string\n  warning?: string\n  helperText?: string\n  maxLength?: number\n  showCharCount?: boolean\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ \n    className, \n    variant, \n    size, \n    error, \n    success, \n    warning, \n    helperText,\n    maxLength,\n    showCharCount = false,\n    value,\n    ...props \n  }, ref) => {\n    // Determine variant based on validation states\n    const currentVariant = error ? 'error' : success ? 'success' : warning ? 'warning' : variant\n    \n    // Calculate character count\n    const charCount = typeof value === 'string' ? value.length : 0\n    const showCount = showCharCount || maxLength\n\n    return (\n      <div className=\"w-full\">\n        <textarea\n          className={cn(textareaVariants({ variant: currentVariant, size }), className)}\n          ref={ref}\n          value={value}\n          maxLength={maxLength}\n          {...props}\n        />\n        \n        {/* Helper text and character count */}\n        <div className=\"mt-1.5 flex items-center justify-between text-xs\">\n          <div>\n            {error && <p className=\"text-error flex items-center gap-1\">{error}</p>}\n            {success && <p className=\"text-success flex items-center gap-1\">{success}</p>}\n            {warning && <p className=\"text-warning flex items-center gap-1\">{warning}</p>}\n            {helperText && !error && !success && !warning && (\n              <p className=\"text-muted-foreground\">{helperText}</p>\n            )}\n          </div>\n          \n          {showCount && (\n            <p className={cn(\n              \"text-muted-foreground\",\n              maxLength && charCount > maxLength * 0.9 && \"text-warning\",\n              maxLength && charCount >= maxLength && \"text-error\"\n            )}>\n              {charCount}{maxLength && `/${maxLength}`}\n            </p>\n          )}\n        </div>\n      </div>\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea, textareaVariants }"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,mBAAmB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACzB,kOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAcF,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,UAAU,EACV,SAAS,EACT,gBAAgB,KAAK,EACrB,KAAK,EACL,GAAG,OACJ,EAAE;IACD,+CAA+C;IAC/C,MAAM,iBAAiB,QAAQ,UAAU,UAAU,YAAY,UAAU,YAAY;IAErF,4BAA4B;IAC5B,MAAM,YAAY,OAAO,UAAU,WAAW,MAAM,MAAM,GAAG;IAC7D,MAAM,YAAY,iBAAiB;IAEnC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;oBAAE,SAAS;oBAAgB;gBAAK,IAAI;gBACnE,KAAK;gBACL,OAAO;gBACP,WAAW;gBACV,GAAG,KAAK;;;;;;0BAIX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BACE,uBAAS,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;4BAC5D,yBAAW,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;4BAChE,yBAAW,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;4BAChE,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,yBACpC,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;oBAIzC,2BACC,6LAAC;wBAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,yBACA,aAAa,YAAY,YAAY,OAAO,gBAC5C,aAAa,aAAa,aAAa;;4BAEtC;4BAAW,aAAa,CAAC,CAAC,EAAE,WAAW;;;;;;;;;;;;;;;;;;;AAMpD;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3702, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {\n    variant?: 'default' | 'error' | 'success' | 'warning'\n    size?: 'sm' | 'default' | 'lg'\n  }\n>(({ className, children, variant = 'default', size = 'default', ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      // Base styles\n      \"flex w-full items-center justify-between rounded-lg border bg-background px-4 py-3 text-sm font-medium transition-all duration-200 data-[placeholder]:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      // Variant styles\n      {\n        \"border-border hover:border-secondary-400 focus:border-primary focus:ring-2 focus:ring-primary/20\": variant === 'default',\n        \"border-error bg-red-50 focus:border-error focus:ring-2 focus:ring-error/20\": variant === 'error',\n        \"border-success bg-green-50 focus:border-success focus:ring-2 focus:ring-success/20\": variant === 'success',\n        \"border-warning bg-yellow-50 focus:border-warning focus:ring-2 focus:ring-warning/20\": variant === 'warning',\n      },\n      // Size styles\n      {\n        \"h-8 px-3 py-1.5 text-xs\": size === 'sm',\n        \"h-10 px-4 py-2.5 text-sm\": size === 'default',\n        \"h-12 px-4 py-3 text-base\": size === 'lg',\n      },\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-lg border border-border bg-popover text-popover-foreground shadow-xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-md py-2.5 pl-10 pr-3 text-sm font-medium text-popover-foreground outline-none transition-colors hover:bg-muted focus:bg-muted data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-primary\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAMnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3E,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,wQACA,iBAAiB;QACjB;YACE,oGAAoG,YAAY;YAChH,8EAA8E,YAAY;YAC1F,sFAAsF,YAAY;YAClG,uFAAuF,YAAY;QACrG,GACA,cAAc;QACd;YACE,2BAA2B,SAAS;YACpC,4BAA4B,SAAS;YACrC,4BAA4B,SAAS;QACvC,GACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+jBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qQACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3929, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/wizard-steps/topic-selection-step.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\"\nimport { TrendingUp, Lightbulb, Target, Search, Sparkles } from \"lucide-react\"\nimport { BookCreationData } from \"../new-book-wizard\"\n\nconst topicSchema = z.object({\n  topic: z.string().min(5, \"Topic must be at least 5 characters\"),\n  genre: z.string().min(1, \"Please select a genre\"),\n  targetAudience: z.string().min(5, \"Please describe your target audience\"),\n  industryFocus: z.array(z.string()).max(3, \"Maximum 3 industries allowed\").optional(),\n})\n\ntype TopicFormData = z.infer<typeof topicSchema>\n\ninterface TopicSelectionStepProps {\n  data: Partial<BookCreationData>\n  onUpdate: (data: Partial<BookCreationData>) => void\n  onNext: () => void\n  onCancel: () => void\n}\n\nconst genres = [\n  \"Business & Economics\",\n  \"Self-Help & Personal Development\",\n  \"Health & Fitness\",\n  \"Technology\",\n  \"Fiction\",\n  \"Non-Fiction\",\n  \"Biography & Memoir\",\n  \"History\",\n  \"Science\",\n  \"Travel\",\n  \"Cooking\",\n  \"Art & Design\",\n  \"Education\",\n  \"Parenting\",\n  \"Religion & Spirituality\",\n  \"Romance\",\n  \"Mystery & Thriller\",\n  \"Science Fiction & Fantasy\",\n  \"Young Adult\",\n  \"Children's Books\",\n]\n\nconst trendingTopics = [\n  { topic: \"AI and Machine Learning for Beginners\", trend: \"+125%\", genre: \"Technology\" },\n  { topic: \"Sustainable Living and Zero Waste\", trend: \"+89%\", genre: \"Lifestyle\" },\n  { topic: \"Cryptocurrency Investment Guide\", trend: \"+67%\", genre: \"Finance\" },\n  { topic: \"Remote Work Productivity\", trend: \"+54%\", genre: \"Business\" },\n  { topic: \"Mental Health and Mindfulness\", trend: \"+43%\", genre: \"Self-Help\" },\n  { topic: \"Plant-Based Nutrition\", trend: \"+38%\", genre: \"Health\" },\n]\n\nconst industryOptions = [\n  \"Health\", \"Wealth\", \"Beauty\", \"Technology\", \"Business\", \"Fitness\",\n  \"Nutrition\", \"Self-Help\", \"Finance\", \"Real Estate\", \"Marketing\",\n  \"Education\", \"Relationships\", \"Lifestyle\", \"Travel\", \"Food\",\n  \"Fashion\", \"Entertainment\", \"Sports\", \"Science\"\n]\n\nexport function TopicSelectionStep({ data, onUpdate, onNext, onCancel }: TopicSelectionStepProps) {\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [selectedKeywords, setSelectedKeywords] = useState<string[]>(data.keywords || [])\n  const [customKeyword, setCustomKeyword] = useState(\"\")\n  const [selectedIndustries, setSelectedIndustries] = useState<string[]>(\n    data.industryFocus || [\"Health\", \"Wealth\", \"Beauty\"]\n  )\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: { errors, isValid },\n  } = useForm<TopicFormData>({\n    resolver: zodResolver(topicSchema),\n    defaultValues: {\n      topic: data.topic || \"\",\n      genre: data.genre || \"\",\n      targetAudience: data.targetAudience || \"\",\n      industryFocus: data.industryFocus || [\"Health\", \"Wealth\", \"Beauty\"],\n    },\n  })\n\n  const watchedTopic = watch(\"topic\")\n\n  const handleTrendingTopicSelect = (topic: string, genre: string) => {\n    setValue(\"topic\", topic)\n    setValue(\"genre\", genre)\n  }\n\n  const addKeyword = () => {\n    if (customKeyword.trim() && !selectedKeywords.includes(customKeyword.trim())) {\n      const newKeywords = [...selectedKeywords, customKeyword.trim()]\n      setSelectedKeywords(newKeywords)\n      setCustomKeyword(\"\")\n    }\n  }\n\n  const removeKeyword = (keyword: string) => {\n    setSelectedKeywords(selectedKeywords.filter(k => k !== keyword))\n  }\n\n  const toggleIndustry = (industry: string) => {\n    setSelectedIndustries(current => {\n      if (current.includes(industry)) {\n        return current.filter(i => i !== industry)\n      } else if (current.length < 3) {\n        return [...current, industry]\n      }\n      return current\n    })\n  }\n\n  const analyzeTrends = async () => {\n    setIsAnalyzing(true)\n    try {\n      // Simulate API call to analyze trends\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      // Mock trend analysis results\n      const mockTrendData = {\n        searchVolume: Math.floor(Math.random() * 10000) + 1000,\n        competition: [\"Low\", \"Medium\", \"High\"][Math.floor(Math.random() * 3)],\n        profitability: Math.floor(Math.random() * 5) + 1,\n      }\n      \n      onUpdate({ trendData: mockTrendData })\n    } catch (error) {\n      console.error(\"Failed to analyze trends:\", error)\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  const onSubmit = (formData: TopicFormData) => {\n    onUpdate({\n      ...formData,\n      keywords: selectedKeywords,\n      industryFocus: selectedIndustries,\n    })\n    onNext()\n  }\n\n  return (\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Lightbulb className=\"h-5 w-5 text-primary\" />\n            Choose Your Book Topic\n          </CardTitle>\n          <CardDescription>\n            Select a topic that resonates with your interests and has market potential.\n            Our AI will help you identify trending niches.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Trending Topics */}\n          <div>\n            <div className=\"flex items-center gap-2 mb-4\">\n              <TrendingUp className=\"h-4 w-4 text-green-600\" />\n              <Label className=\"font-medium\">Trending Topics</Label>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              {trendingTopics.map((trending, index) => (\n                <button\n                  key={index}\n                  type=\"button\"\n                  onClick={() => handleTrendingTopicSelect(trending.topic, trending.genre)}\n                  className=\"p-4 text-left border rounded-lg hover:bg-primary/5 hover:border-primary transition-colors\"\n                >\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"font-medium text-sm\">{trending.topic}</div>\n                      <div className=\"text-xs text-muted-foreground mt-1\">\n                        {trending.genre}\n                      </div>\n                    </div>\n                    <Badge variant=\"default\" className=\"text-xs\">\n                      {trending.trend}\n                    </Badge>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Custom Topic */}\n          <div className=\"space-y-4\">\n            <Label htmlFor=\"topic\">Book Topic *</Label>\n            <Textarea\n              id=\"topic\"\n              placeholder=\"Describe your book topic in detail. What specific problem does it solve? What value does it provide?\"\n              className=\"min-h-[100px]\"\n              {...register(\"topic\")}\n            />\n            {errors.topic && (\n              <Text variant=\"caption\" className=\"text-destructive\">\n                {errors.topic.message}\n              </Text>\n            )}\n          </div>\n\n          {/* Genre Selection */}\n          <div className=\"space-y-2\">\n            <Label>Genre *</Label>\n            <Select\n              value={watch(\"genre\")}\n              onValueChange={(value) => setValue(\"genre\", value)}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Select a genre\" />\n              </SelectTrigger>\n              <SelectContent>\n                {genres.map((genre) => (\n                  <SelectItem key={genre} value={genre}>\n                    {genre}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            {errors.genre && (\n              <Text variant=\"caption\" className=\"text-destructive\">\n                {errors.genre.message}\n              </Text>\n            )}\n          </div>\n\n          {/* Target Audience */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"targetAudience\">Target Audience *</Label>\n            <Textarea\n              id=\"targetAudience\"\n              placeholder=\"Who is your ideal reader? Age group, interests, profession, pain points...\"\n              {...register(\"targetAudience\")}\n            />\n            {errors.targetAudience && (\n              <Text variant=\"caption\" className=\"text-destructive\">\n                {errors.targetAudience.message}\n              </Text>\n            )}\n          </div>\n\n          {/* Industry Focus */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2\">\n              <Target className=\"h-4 w-4 text-primary\" />\n              <Label className=\"font-medium\">\n                Industry Focus (1-3 industries)\n              </Label>\n            </div>\n            <Text variant=\"caption\" className=\"text-muted-foreground\">\n              Select up to 3 industries to focus your research and content on. \n              Default: Health, Wealth, Beauty\n            </Text>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\">\n              {industryOptions.map((industry) => (\n                <button\n                  key={industry}\n                  type=\"button\"\n                  onClick={() => toggleIndustry(industry)}\n                  className={`p-3 text-sm rounded-lg border transition-all ${\n                    selectedIndustries.includes(industry)\n                      ? \"bg-primary text-primary-foreground border-primary shadow-sm\"\n                      : \"bg-background hover:bg-muted border-border\"\n                  }`}\n                >\n                  {industry}\n                </button>\n              ))}\n            </div>\n            {selectedIndustries.length > 0 && (\n              <div className=\"p-3 bg-primary/5 rounded-lg border border-primary/20\">\n                <Text variant=\"small\" className=\"font-medium mb-2\">\n                  Selected Industries ({selectedIndustries.length}/3):\n                </Text>\n                <div className=\"flex flex-wrap gap-2\">\n                  {selectedIndustries.map((industry) => (\n                    <Badge key={industry} variant=\"default\">\n                      {industry}\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Keywords */}\n          <div className=\"space-y-4\">\n            <Label>Keywords (Optional)</Label>\n            <div className=\"flex gap-2\">\n              <Input\n                placeholder=\"Add relevant keywords\"\n                value={customKeyword}\n                onChange={(e) => setCustomKeyword(e.target.value)}\n                onKeyPress={(e) => e.key === \"Enter\" && (e.preventDefault(), addKeyword())}\n              />\n              <Button type=\"button\" onClick={addKeyword} variant=\"outline\">\n                Add\n              </Button>\n            </div>\n            {selectedKeywords.length > 0 && (\n              <div className=\"flex flex-wrap gap-2\">\n                {selectedKeywords.map((keyword) => (\n                  <Badge\n                    key={keyword}\n                    variant=\"secondary\"\n                    className=\"cursor-pointer\"\n                    onClick={() => removeKeyword(keyword)}\n                  >\n                    {keyword} ×\n                  </Badge>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Trend Analysis */}\n          {watchedTopic && watchedTopic.length > 10 && (\n            <div className=\"p-4 bg-primary/5 rounded-lg border border-primary/20\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center gap-2\">\n                  <Search className=\"h-4 w-4 text-primary\" />\n                  <Text variant=\"small\" className=\"font-medium\">\n                    AI Trend Analysis\n                  </Text>\n                </div>\n                <Button\n                  type=\"button\"\n                  onClick={analyzeTrends}\n                  disabled={isAnalyzing}\n                  size=\"sm\"\n                  variant=\"outline\"\n                >\n                  {isAnalyzing ? (\n                    <>\n                      <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                      Analyzing...\n                    </>\n                  ) : (\n                    <>\n                      <Sparkles className=\"h-4 w-4 mr-2\" />\n                      Analyze Trends\n                    </>\n                  )}\n                </Button>\n              </div>\n              {data.trendData && (\n                <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                  <div>\n                    <Text variant=\"caption\" className=\"text-muted-foreground\">\n                      Search Volume\n                    </Text>\n                    <div className=\"font-medium\">{data.trendData.searchVolume.toLocaleString()}</div>\n                  </div>\n                  <div>\n                    <Text variant=\"caption\" className=\"text-muted-foreground\">\n                      Competition\n                    </Text>\n                    <div className=\"font-medium\">{data.trendData.competition}</div>\n                  </div>\n                  <div>\n                    <Text variant=\"caption\" className=\"text-muted-foreground\">\n                      Profit Score\n                    </Text>\n                    <div className=\"font-medium\">{data.trendData.profitability}/5 ⭐</div>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between\">\n        <Button type=\"button\" onClick={onCancel} variant=\"outline\">\n          Cancel\n        </Button>\n        <Button type=\"submit\" disabled={!isValid}>\n          Next: Configure Content\n        </Button>\n      </div>\n    </form>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAhBA;;;;;;;;;;;;;;;;AAmBA,MAAM,cAAc,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;IAC3B,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IACzB,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IACzB,gBAAgB,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAClC,eAAe,CAAA,GAAA,oJAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,KAAK,GAAG,CAAC,GAAG,gCAAgC,QAAQ;AACpF;AAWA,MAAM,SAAS;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAyC,OAAO;QAAS,OAAO;IAAa;IACtF;QAAE,OAAO;QAAqC,OAAO;QAAQ,OAAO;IAAY;IAChF;QAAE,OAAO;QAAmC,OAAO;QAAQ,OAAO;IAAU;IAC5E;QAAE,OAAO;QAA4B,OAAO;QAAQ,OAAO;IAAW;IACtE;QAAE,OAAO;QAAiC,OAAO;QAAQ,OAAO;IAAY;IAC5E;QAAE,OAAO;QAAyB,OAAO;QAAQ,OAAO;IAAS;CAClE;AAED,MAAM,kBAAkB;IACtB;IAAU;IAAU;IAAU;IAAc;IAAY;IACxD;IAAa;IAAa;IAAW;IAAe;IACpD;IAAa;IAAiB;IAAa;IAAU;IACrD;IAAW;IAAiB;IAAU;CACvC;AAEM,SAAS,mBAAmB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAA2B;;IAC9F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,KAAK,QAAQ,IAAI,EAAE;IACtF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzD,KAAK,aAAa,IAAI;QAAC;QAAU;QAAU;KAAS;IAGtD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO,KAAK,KAAK,IAAI;YACrB,OAAO,KAAK,KAAK,IAAI;YACrB,gBAAgB,KAAK,cAAc,IAAI;YACvC,eAAe,KAAK,aAAa,IAAI;gBAAC;gBAAU;gBAAU;aAAS;QACrE;IACF;IAEA,MAAM,eAAe,MAAM;IAE3B,MAAM,4BAA4B,CAAC,OAAe;QAChD,SAAS,SAAS;QAClB,SAAS,SAAS;IACpB;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,IAAI,MAAM,CAAC,iBAAiB,QAAQ,CAAC,cAAc,IAAI,KAAK;YAC5E,MAAM,cAAc;mBAAI;gBAAkB,cAAc,IAAI;aAAG;YAC/D,oBAAoB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,IAAK,MAAM;IACzD;IAEA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,CAAA;YACpB,IAAI,QAAQ,QAAQ,CAAC,WAAW;gBAC9B,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,MAAM,GAAG,GAAG;gBAC7B,OAAO;uBAAI;oBAAS;iBAAS;YAC/B;YACA,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe;QACf,IAAI;YACF,sCAAsC;YACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,8BAA8B;YAC9B,MAAM,gBAAgB;gBACpB,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;gBAClD,aAAa;oBAAC;oBAAO;oBAAU;iBAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBACrE,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACjD;YAEA,SAAS;gBAAE,WAAW;YAAc;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,SAAS;YACP,GAAG,QAAQ;YACX,UAAU;YACV,eAAe;QACjB;QACA;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU,aAAa;QAAW,WAAU;;0BAChD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,+MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAGhD,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAKnB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,0BAA0B,SAAS,KAAK,EAAE,SAAS,KAAK;gDACvE,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAuB,SAAS,KAAK;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EACZ,SAAS,KAAK;;;;;;;;;;;;sEAGnB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,SAAS,KAAK;;;;;;;;;;;;+CAbd;;;;;;;;;;;;;;;;0CAqBb,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,WAAU;wCACT,GAAG,SAAS,QAAQ;;;;;;oCAEtB,OAAO,KAAK,kBACX,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAC/B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAM3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC,qIAAA,CAAA,SAAM;wCACL,OAAO,MAAM;wCACb,eAAe,CAAC,QAAU,SAAS,SAAS;;0DAE5C,6LAAC,qIAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;0DACX,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,qIAAA,CAAA,aAAU;wDAAa,OAAO;kEAC5B;uDADc;;;;;;;;;;;;;;;;oCAMtB,OAAO,KAAK,kBACX,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAC/B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAM3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAiB;;;;;;kDAChC,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACX,GAAG,SAAS,iBAAiB;;;;;;oCAE/B,OAAO,cAAc,kBACpB,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAC/B,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;0CAMpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAIjC,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAAwB;;;;;;kDAI1D,6LAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,eAAe;gDAC9B,WAAW,CAAC,6CAA6C,EACvD,mBAAmB,QAAQ,CAAC,YACxB,gEACA,8CACJ;0DAED;+CATI;;;;;;;;;;oCAaV,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;;oDAAmB;oDAC3B,mBAAmB,MAAM;oDAAC;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;0DACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC,oIAAA,CAAA,QAAK;wDAAgB,SAAQ;kEAC3B;uDADS;;;;;;;;;;;;;;;;;;;;;;0CAUtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,YAAY;;;;;;0DAE3E,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAS;gDAAY,SAAQ;0DAAU;;;;;;;;;;;;oCAI9D,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,oIAAA,CAAA,QAAK;gDAEJ,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,cAAc;;oDAE5B;oDAAQ;;+CALJ;;;;;;;;;;;;;;;;4BAad,gBAAgB,aAAa,MAAM,GAAG,oBACrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAQ,WAAU;kEAAc;;;;;;;;;;;;0DAIhD,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU;gDACV,MAAK;gDACL,SAAQ;0DAEP,4BACC;;sEACE,6LAAC,iJAAA,CAAA,iBAAc;4DAAC,MAAK;4DAAK,WAAU;;;;;;wDAAS;;iFAI/C;;sEACE,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;oCAM5C,KAAK,SAAS,kBACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG1D,6LAAC;wDAAI,WAAU;kEAAe,KAAK,SAAS,CAAC,YAAY,CAAC,cAAc;;;;;;;;;;;;0DAE1E,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG1D,6LAAC;wDAAI,WAAU;kEAAe,KAAK,SAAS,CAAC,WAAW;;;;;;;;;;;;0DAE1D,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG1D,6LAAC;wDAAI,WAAU;;4DAAe,KAAK,SAAS,CAAC,aAAa;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,SAAS;wBAAU,SAAQ;kCAAU;;;;;;kCAG3D,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,UAAU,CAAC;kCAAS;;;;;;;;;;;;;;;;;;AAMlD;GAxUgB;;QAcV,iKAAA,CAAA,UAAO;;;KAdG", "debugId": null}}, {"offset": {"line": 4799, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-1.5 w-full grow overflow-hidden rounded-full bg-primary/20\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4858, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/wizard-steps/content-generation-step.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Slider } from \"@/components/ui/slider\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\"\nimport { FileText, Brain, Lightbulb, Target, BookOpen, Zap } from \"lucide-react\"\nimport { BookCreationData } from \"../new-book-wizard\"\n\nconst contentSchema = z.object({\n  tone: z.string().min(1, \"Please select a tone\"),\n  length: z.enum([\"short\", \"medium\", \"long\"]),\n  chapterCount: z.number().min(3).max(50),\n})\n\ntype ContentFormData = z.infer<typeof contentSchema>\n\ninterface ContentGenerationStepProps {\n  data: Partial<BookCreationData>\n  onUpdate: (data: Partial<BookCreationData>) => void\n  onNext: () => void\n  onPrevious: () => void\n}\n\nconst tones = [\n  { value: \"professional\", label: \"Professional\", description: \"Formal, authoritative, and expert-level\" },\n  { value: \"conversational\", label: \"Conversational\", description: \"Friendly, approachable, and engaging\" },\n  { value: \"educational\", label: \"Educational\", description: \"Clear, instructional, and easy to follow\" },\n  { value: \"inspirational\", label: \"Inspirational\", description: \"Motivating, uplifting, and encouraging\" },\n  { value: \"humorous\", label: \"Humorous\", description: \"Light-hearted, witty, and entertaining\" },\n  { value: \"academic\", label: \"Academic\", description: \"Scholarly, detailed, and research-based\" },\n]\n\nconst lengthOptions = [\n  { value: \"short\", label: \"Short\", pages: \"50-75\", description: \"Quick read, focused content\" },\n  { value: \"medium\", label: \"Medium\", pages: \"100-150\", description: \"Comprehensive coverage\" },\n  { value: \"long\", label: \"Long\", pages: \"200-300\", description: \"In-depth exploration\" },\n]\n\nexport function ContentGenerationStep({ data, onUpdate, onNext, onPrevious }: ContentGenerationStepProps) {\n  const [isGeneratingOutline, setIsGeneratingOutline] = useState(false)\n  const [generatedOutline, setGeneratedOutline] = useState<string[]>(data.outline || [])\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: { errors, isValid },\n  } = useForm<ContentFormData>({\n    resolver: zodResolver(contentSchema),\n    defaultValues: {\n      tone: data.tone || \"\",\n      length: data.length || \"medium\",\n      chapterCount: data.chapterCount || 8,\n    },\n  })\n\n  const watchedLength = watch(\"length\")\n  const watchedChapterCount = watch(\"chapterCount\")\n\n  const generateOutline = async () => {\n    setIsGeneratingOutline(true)\n    try {\n      // Simulate AI outline generation\n      await new Promise(resolve => setTimeout(resolve, 3000))\n      \n      const mockOutline = [\n        \"Introduction: Understanding the Problem\",\n        \"Chapter 1: Fundamentals and Core Concepts\",\n        \"Chapter 2: Getting Started - First Steps\",\n        \"Chapter 3: Advanced Techniques and Strategies\",\n        \"Chapter 4: Common Challenges and Solutions\",\n        \"Chapter 5: Real-World Applications\",\n        \"Chapter 6: Best Practices and Guidelines\",\n        \"Chapter 7: Future Trends and Innovations\",\n        \"Conclusion: Putting It All Together\",\n      ].slice(0, watchedChapterCount + 1)\n\n      setGeneratedOutline(mockOutline)\n      onUpdate({ outline: mockOutline })\n    } catch (error) {\n      console.error(\"Failed to generate outline:\", error)\n    } finally {\n      setIsGeneratingOutline(false)\n    }\n  }\n\n  const onSubmit = (formData: ContentFormData) => {\n    onUpdate({\n      ...formData,\n      outline: generatedOutline,\n    })\n    onNext()\n  }\n\n  const getLengthInfo = (length: string) => {\n    return lengthOptions.find(option => option.value === length)\n  }\n\n  return (\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5 text-primary\" />\n            Configure Content Generation\n          </CardTitle>\n          <CardDescription>\n            Define how your book content will be generated. Our AI will create engaging,\n            well-structured content based on your preferences.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Tone Selection */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 mb-4\">\n              <Brain className=\"h-4 w-4 text-blue-600\" />\n              <Label className=\"font-medium\">Writing Tone & Style</Label>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              {tones.map((tone) => (\n                <button\n                  key={tone.value}\n                  type=\"button\"\n                  onClick={() => setValue(\"tone\", tone.value)}\n                  className={`p-4 text-left border rounded-lg transition-colors ${\n                    watch(\"tone\") === tone.value\n                      ? \"border-primary bg-primary/5\"\n                      : \"border-border hover:bg-muted/50\"\n                  }`}\n                >\n                  <div className=\"font-medium text-sm\">{tone.label}</div>\n                  <div className=\"text-xs text-muted-foreground mt-1\">\n                    {tone.description}\n                  </div>\n                </button>\n              ))}\n            </div>\n            {errors.tone && (\n              <Text variant=\"caption\" className=\"text-destructive\">\n                {errors.tone.message}\n              </Text>\n            )}\n          </div>\n\n          <Separator />\n\n          {/* Book Length */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 mb-4\">\n              <BookOpen className=\"h-4 w-4 text-green-600\" />\n              <Label className=\"font-medium\">Book Length</Label>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n              {lengthOptions.map((option) => (\n                <button\n                  key={option.value}\n                  type=\"button\"\n                  onClick={() => setValue(\"length\", option.value as \"short\" | \"medium\" | \"long\")}\n                  className={`p-4 text-center border rounded-lg transition-colors ${\n                    watchedLength === option.value\n                      ? \"border-primary bg-primary/5\"\n                      : \"border-border hover:bg-muted/50\"\n                  }`}\n                >\n                  <div className=\"font-medium text-sm\">{option.label}</div>\n                  <div className=\"text-xs text-primary font-medium\">{option.pages} pages</div>\n                  <div className=\"text-xs text-muted-foreground mt-1\">\n                    {option.description}\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Chapter Count */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <Label className=\"font-medium\">Number of Chapters</Label>\n              <Badge variant=\"outline\" className=\"px-3\">\n                {watchedChapterCount} chapters\n              </Badge>\n            </div>\n            <div className=\"px-4\">\n              <Slider\n                value={[watchedChapterCount]}\n                onValueChange={([value]) => setValue(\"chapterCount\", value)}\n                min={3}\n                max={20}\n                step={1}\n                className=\"w-full\"\n              />\n              <div className=\"flex justify-between text-xs text-muted-foreground mt-2\">\n                <span>3 chapters</span>\n                <span>20 chapters</span>\n              </div>\n            </div>\n            <div className=\"text-sm text-muted-foreground\">\n              Estimated reading time: {Math.round(watchedChapterCount * 12)} minutes\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* AI Outline Generation */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <Lightbulb className=\"h-4 w-4 text-yellow-600\" />\n                <Label className=\"font-medium\">Content Outline</Label>\n              </div>\n              <Button\n                type=\"button\"\n                onClick={generateOutline}\n                disabled={isGeneratingOutline || !data.topic}\n                size=\"sm\"\n                variant=\"outline\"\n              >\n                {isGeneratingOutline ? (\n                  <>\n                    <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                    Generating...\n                  </>\n                ) : (\n                  <>\n                    <Zap className=\"h-4 w-4 mr-2\" />\n                    Generate Outline\n                  </>\n                )}\n              </Button>\n            </div>\n\n            {!data.topic && (\n              <div className=\"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                <Text variant=\"small\" className=\"text-yellow-800\">\n                  Complete the topic selection step to generate an AI-powered outline.\n                </Text>\n              </div>\n            )}\n\n            {generatedOutline.length > 0 && (\n              <div className=\"space-y-2\">\n                <Text variant=\"small\" className=\"font-medium text-muted-foreground\">\n                  Generated Outline Preview:\n                </Text>\n                <div className=\"bg-muted/30 border rounded-lg p-4\">\n                  <div className=\"space-y-2\">\n                    {generatedOutline.map((chapter, index) => (\n                      <div key={index} className=\"flex items-start gap-3\">\n                        <Badge variant=\"secondary\" className=\"text-xs min-w-[24px] h-6\">\n                          {index + 1}\n                        </Badge>\n                        <Text variant=\"small\" className=\"flex-1\">\n                          {chapter}\n                        </Text>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                <Text variant=\"caption\" className=\"text-muted-foreground\">\n                  This outline will guide the AI content generation. You can modify it in the next steps.\n                </Text>\n              </div>\n            )}\n          </div>\n\n          {/* Content Preview */}\n          {watchedLength && (\n            <div className=\"p-4 bg-primary/5 rounded-lg border border-primary/20\">\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n                <div>\n                  <div className=\"text-lg font-semibold text-primary\">\n                    {getLengthInfo(watchedLength)?.pages}\n                  </div>\n                  <Text variant=\"caption\" className=\"text-muted-foreground\">\n                    Pages\n                  </Text>\n                </div>\n                <div>\n                  <div className=\"text-lg font-semibold text-primary\">\n                    {watchedChapterCount}\n                  </div>\n                  <Text variant=\"caption\" className=\"text-muted-foreground\">\n                    Chapters\n                  </Text>\n                </div>\n                <div>\n                  <div className=\"text-lg font-semibold text-primary\">\n                    {Math.round(watchedChapterCount * 12)}m\n                  </div>\n                  <Text variant=\"caption\" className=\"text-muted-foreground\">\n                    Read Time\n                  </Text>\n                </div>\n                <div>\n                  <div className=\"text-lg font-semibold text-primary\">\n                    {watch(\"tone\") ? tones.find(t => t.value === watch(\"tone\"))?.label : \"—\"}\n                  </div>\n                  <Text variant=\"caption\" className=\"text-muted-foreground\">\n                    Tone\n                  </Text>\n                </div>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between\">\n        <Button type=\"button\" onClick={onPrevious} variant=\"outline\">\n          Previous: Topic Selection\n        </Button>\n        <Button \n          type=\"submit\" \n          disabled={!isValid || (generatedOutline.length === 0 && !!data.topic)}\n        >\n          Next: Design Cover\n        </Button>\n      </div>\n    </form>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAjBA;;;;;;;;;;;;;;AAoBA,MAAM,gBAAgB,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;IAC7B,MAAM,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IACxB,QAAQ,CAAA,GAAA,oJAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;QAAU;KAAO;IAC1C,cAAc,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACtC;AAWA,MAAM,QAAQ;IACZ;QAAE,OAAO;QAAgB,OAAO;QAAgB,aAAa;IAA0C;IACvG;QAAE,OAAO;QAAkB,OAAO;QAAkB,aAAa;IAAuC;IACxG;QAAE,OAAO;QAAe,OAAO;QAAe,aAAa;IAA2C;IACtG;QAAE,OAAO;QAAiB,OAAO;QAAiB,aAAa;IAAyC;IACxG;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAyC;IAC9F;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAA0C;CAChG;AAED,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAS,OAAO;QAAS,OAAO;QAAS,aAAa;IAA8B;IAC7F;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;QAAW,aAAa;IAAyB;IAC5F;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;QAAW,aAAa;IAAuB;CACvF;AAEM,SAAS,sBAAsB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAA8B;;IACtG,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,KAAK,OAAO,IAAI,EAAE;IAErF,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM,KAAK,IAAI,IAAI;YACnB,QAAQ,KAAK,MAAM,IAAI;YACvB,cAAc,KAAK,YAAY,IAAI;QACrC;IACF;IAEA,MAAM,gBAAgB,MAAM;IAC5B,MAAM,sBAAsB,MAAM;IAElC,MAAM,kBAAkB;QACtB,uBAAuB;QACvB,IAAI;YACF,iCAAiC;YACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,cAAc;gBAClB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAAC,KAAK,CAAC,GAAG,sBAAsB;YAEjC,oBAAoB;YACpB,SAAS;gBAAE,SAAS;YAAY;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,SAAS;YACP,GAAG,QAAQ;YACX,SAAS;QACX;QACA;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,cAAc,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IACvD;IAEA,qBACE,6LAAC;QAAK,UAAU,aAAa;QAAW,WAAU;;0BAChD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAG/C,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAKnB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,SAAS,QAAQ,KAAK,KAAK;gDAC1C,WAAW,CAAC,kDAAkD,EAC5D,MAAM,YAAY,KAAK,KAAK,GACxB,gCACA,mCACJ;;kEAEF,6LAAC;wDAAI,WAAU;kEAAuB,KAAK,KAAK;;;;;;kEAChD,6LAAC;wDAAI,WAAU;kEACZ,KAAK,WAAW;;;;;;;+CAXd,KAAK,KAAK;;;;;;;;;;oCAgBpB,OAAO,IAAI,kBACV,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAC/B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;0CAK1B,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,SAAS,UAAU,OAAO,KAAK;gDAC9C,WAAW,CAAC,oDAAoD,EAC9D,kBAAkB,OAAO,KAAK,GAC1B,gCACA,mCACJ;;kEAEF,6LAAC;wDAAI,WAAU;kEAAuB,OAAO,KAAK;;;;;;kEAClD,6LAAC;wDAAI,WAAU;;4DAAoC,OAAO,KAAK;4DAAC;;;;;;;kEAChE,6LAAC;wDAAI,WAAU;kEACZ,OAAO,WAAW;;;;;;;+CAZhB,OAAO,KAAK;;;;;;;;;;;;;;;;0CAoBzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAc;;;;;;0DAC/B,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAChC;oDAAoB;;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO;oDAAC;iDAAoB;gDAC5B,eAAe,CAAC,CAAC,MAAM,GAAK,SAAS,gBAAgB;gDACrD,KAAK;gDACL,KAAK;gDACL,MAAM;gDACN,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAGV,6LAAC;wCAAI,WAAU;;4CAAgC;4CACpB,KAAK,KAAK,CAAC,sBAAsB;4CAAI;;;;;;;;;;;;;0CAIlE,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAEjC,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU,uBAAuB,CAAC,KAAK,KAAK;gDAC5C,MAAK;gDACL,SAAQ;0DAEP,oCACC;;sEACE,6LAAC,iJAAA,CAAA,iBAAc;4DAAC,MAAK;4DAAK,WAAU;;;;;;wDAAS;;iFAI/C;;sEACE,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;oCAOvC,CAAC,KAAK,KAAK,kBACV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yIAAA,CAAA,OAAI;4CAAC,SAAQ;4CAAQ,WAAU;sDAAkB;;;;;;;;;;;oCAMrD,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;0DAAoC;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAClC,QAAQ;;;;;;8EAEX,6LAAC,yIAAA,CAAA,OAAI;oEAAC,SAAQ;oEAAQ,WAAU;8EAC7B;;;;;;;2DALK;;;;;;;;;;;;;;;0DAWhB,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAU,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;4BAQ/D,+BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,cAAc,gBAAgB;;;;;;8DAEjC,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;sDAI5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ;;;;;;8DAEH,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;sDAI5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK,CAAC,sBAAsB;wDAAI;;;;;;;8DAExC,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;sDAI5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,MAAM,UAAU,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,MAAM,UAAU,QAAQ;;;;;;8DAEvE,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,SAAS;wBAAY,SAAQ;kCAAU;;;;;;kCAG7D,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,UAAU,CAAC,WAAY,iBAAiB,MAAM,KAAK,KAAK,CAAC,CAAC,KAAK,KAAK;kCACrE;;;;;;;;;;;;;;;;;;AAMT;GA5RgB;;QAUV,iKAAA,CAAA,UAAO;;;KAVG", "debugId": null}}, {"offset": {"line": 5652, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/wizard-steps/cover-design-step.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\"\nimport { Palette, Image, Sparkles, Eye, Download, RefreshCw } from \"lucide-react\"\nimport { BookCreationData } from \"../new-book-wizard\"\n\nconst coverSchema = z.object({\n  coverStyle: z.string().min(1, \"Please select a cover style\"),\n  coverText: z.string().min(1, \"Please enter cover text\"),\n})\n\ntype CoverFormData = z.infer<typeof coverSchema>\n\ninterface CoverDesignStepProps {\n  data: Partial<BookCreationData>\n  onUpdate: (data: Partial<BookCreationData>) => void\n  onNext: () => void\n  onPrevious: () => void\n}\n\nconst coverStyles = [\n  { value: \"modern\", label: \"Modern\", description: \"Clean, minimalist design with bold typography\" },\n  { value: \"classic\", label: \"Classic\", description: \"Traditional, elegant with serif fonts\" },\n  { value: \"creative\", label: \"Creative\", description: \"Artistic, unique with custom illustrations\" },\n  { value: \"professional\", label: \"Professional\", description: \"Corporate, business-focused design\" },\n  { value: \"playful\", label: \"Playful\", description: \"Fun, colorful with creative elements\" },\n  { value: \"dramatic\", label: \"Dramatic\", description: \"Bold, high-contrast with strong imagery\" },\n]\n\nconst colorPalettes = [\n  { name: \"Ocean Blue\", colors: [\"#0066CC\", \"#4A90E2\", \"#7BB3F0\", \"#FFFFFF\"] },\n  { name: \"Forest Green\", colors: [\"#2E7D32\", \"#4CAF50\", \"#81C784\", \"#FFFFFF\"] },\n  { name: \"Sunset Orange\", colors: [\"#FF6B35\", \"#F7931E\", \"#FFB74D\", \"#FFFFFF\"] },\n  { name: \"Royal Purple\", colors: [\"#6A1B9A\", \"#9C27B0\", \"#BA68C8\", \"#FFFFFF\"] },\n  { name: \"Charcoal Gray\", colors: [\"#424242\", \"#616161\", \"#9E9E9E\", \"#FFFFFF\"] },\n  { name: \"Crimson Red\", colors: [\"#C62828\", \"#F44336\", \"#EF5350\", \"#FFFFFF\"] },\n]\n\nconst mockCoverPreviews = [\n  \"/images/cover-preview-1.jpg\",\n  \"/images/cover-preview-2.jpg\",\n  \"/images/cover-preview-3.jpg\",\n  \"/images/cover-preview-4.jpg\",\n]\n\nexport function CoverDesignStep({ data, onUpdate, onNext, onPrevious }: CoverDesignStepProps) {\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [selectedPalette, setSelectedPalette] = useState<string[]>(data.coverColors || colorPalettes[0].colors)\n  const [generatedCovers, setGeneratedCovers] = useState<string[]>([])\n  const [selectedCover, setSelectedCover] = useState<string>(data.selectedCover || \"\")\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: { errors, isValid },\n  } = useForm<CoverFormData>({\n    resolver: zodResolver(coverSchema),\n    defaultValues: {\n      coverStyle: data.coverStyle || \"\",\n      coverText: data.coverText || data.title || \"\",\n    },\n  })\n\n  const watchedStyle = watch(\"coverStyle\")\n  const watchedText = watch(\"coverText\")\n\n  const generateCovers = async () => {\n    setIsGenerating(true)\n    try {\n      // Simulate AI cover generation\n      await new Promise(resolve => setTimeout(resolve, 4000))\n      \n      setGeneratedCovers(mockCoverPreviews)\n    } catch (error) {\n      console.error(\"Failed to generate covers:\", error)\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const selectPalette = (colors: string[]) => {\n    setSelectedPalette(colors)\n  }\n\n  const onSubmit = (formData: CoverFormData) => {\n    onUpdate({\n      ...formData,\n      coverColors: selectedPalette,\n      selectedCover,\n    })\n    onNext()\n  }\n\n  return (\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Palette className=\"h-5 w-5 text-primary\" />\n            Design Your Book Cover\n          </CardTitle>\n          <CardDescription>\n            Create an eye-catching cover that attracts readers and represents your book's content.\n            Our AI will generate multiple design options based on your preferences.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Cover Style Selection */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 mb-4\">\n              <Image className=\"h-4 w-4 text-purple-600\" />\n              <Label className=\"font-medium\">Cover Style</Label>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n              {coverStyles.map((style) => (\n                <button\n                  key={style.value}\n                  type=\"button\"\n                  onClick={() => setValue(\"coverStyle\", style.value)}\n                  className={`p-4 text-left border rounded-lg transition-colors ${\n                    watchedStyle === style.value\n                      ? \"border-primary bg-primary/5\"\n                      : \"border-border hover:bg-muted/50\"\n                  }`}\n                >\n                  <div className=\"font-medium text-sm\">{style.label}</div>\n                  <div className=\"text-xs text-muted-foreground mt-1\">\n                    {style.description}\n                  </div>\n                </button>\n              ))}\n            </div>\n            {errors.coverStyle && (\n              <Text variant=\"caption\" className=\"text-destructive\">\n                {errors.coverStyle.message}\n              </Text>\n            )}\n          </div>\n\n          <Separator />\n\n          {/* Color Palette Selection */}\n          <div className=\"space-y-4\">\n            <Label className=\"font-medium\">Color Palette</Label>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n              {colorPalettes.map((palette) => (\n                <button\n                  key={palette.name}\n                  type=\"button\"\n                  onClick={() => selectPalette(palette.colors)}\n                  className={`p-3 border rounded-lg transition-colors ${\n                    JSON.stringify(selectedPalette) === JSON.stringify(palette.colors)\n                      ? \"border-primary bg-primary/5\"\n                      : \"border-border hover:bg-muted/50\"\n                  }`}\n                >\n                  <div className=\"text-sm font-medium mb-2\">{palette.name}</div>\n                  <div className=\"flex gap-1\">\n                    {palette.colors.map((color, index) => (\n                      <div\n                        key={index}\n                        className=\"w-6 h-6 rounded border\"\n                        style={{ backgroundColor: color }}\n                      />\n                    ))}\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Cover Text */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"coverText\">Cover Text *</Label>\n            <Input\n              id=\"coverText\"\n              placeholder=\"Enter the title and subtitle for your cover\"\n              {...register(\"coverText\")}\n            />\n            {errors.coverText && (\n              <Text variant=\"caption\" className=\"text-destructive\">\n                {errors.coverText.message}\n              </Text>\n            )}\n            <Text variant=\"caption\" className=\"text-muted-foreground\">\n              This text will appear on your book cover. Keep it concise and impactful.\n            </Text>\n          </div>\n\n          <Separator />\n\n          {/* AI Cover Generation */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <Sparkles className=\"h-4 w-4 text-yellow-600\" />\n                <Label className=\"font-medium\">AI Generated Covers</Label>\n              </div>\n              <Button\n                type=\"button\"\n                onClick={generateCovers}\n                disabled={isGenerating || !watchedStyle || !watchedText}\n                size=\"sm\"\n                variant=\"outline\"\n              >\n                {isGenerating ? (\n                  <>\n                    <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                    Generating...\n                  </>\n                ) : (\n                  <>\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                    Generate Covers\n                  </>\n                )}\n              </Button>\n            </div>\n\n            {(!watchedStyle || !watchedText) && (\n              <div className=\"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                <Text variant=\"small\" className=\"text-yellow-800\">\n                  Select a cover style and enter cover text to generate AI-powered cover designs.\n                </Text>\n              </div>\n            )}\n\n            {isGenerating && (\n              <div className=\"p-8 text-center\">\n                <LoadingSpinner className=\"mx-auto mb-4\" />\n                <Text variant=\"small\" className=\"text-muted-foreground\">\n                  Our AI is creating unique cover designs based on your preferences...\n                </Text>\n              </div>\n            )}\n\n            {generatedCovers.length > 0 && (\n              <div className=\"space-y-4\">\n                <Text variant=\"small\" className=\"font-medium text-muted-foreground\">\n                  Choose your favorite cover design:\n                </Text>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  {generatedCovers.map((cover, index) => (\n                    <div key={index} className=\"space-y-2\">\n                      <button\n                        type=\"button\"\n                        onClick={() => setSelectedCover(cover)}\n                        className={`relative w-full aspect-[3/4] border-2 rounded-lg overflow-hidden transition-all ${\n                          selectedCover === cover\n                            ? \"border-primary shadow-lg scale-105\"\n                            : \"border-border hover:border-primary/50\"\n                        }`}\n                      >\n                        <div className=\"w-full h-full bg-gradient-to-br from-primary/10 to-primary/30 flex items-center justify-center\">\n                          <div className=\"text-center p-4\">\n                            <div className=\"text-sm font-medium mb-2\">{watchedText}</div>\n                            <div className=\"text-xs text-muted-foreground\">\n                              {watchedStyle} Style\n                            </div>\n                          </div>\n                        </div>\n                        {selectedCover === cover && (\n                          <div className=\"absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1\">\n                            <Eye className=\"h-3 w-3\" />\n                          </div>\n                        )}\n                      </button>\n                      <div className=\"flex gap-1\">\n                        <Button size=\"sm\" variant=\"outline\" className=\"flex-1\">\n                          <Eye className=\"h-3 w-3 mr-1\" />\n                          Preview\n                        </Button>\n                        <Button size=\"sm\" variant=\"outline\">\n                          <Download className=\"h-3 w-3\" />\n                        </Button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n                \n                {selectedCover && (\n                  <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg\">\n                    <Text variant=\"small\" className=\"text-green-800 font-medium\">\n                      ✓ Cover selected! You can change this later or generate new options.\n                    </Text>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Design Preview */}\n          {watchedStyle && selectedPalette.length > 0 && (\n            <div className=\"p-4 bg-primary/5 rounded-lg border border-primary/20\">\n              <div className=\"flex items-center gap-4\">\n                <div className=\"flex flex-col items-center\">\n                  <Text variant=\"caption\" className=\"text-muted-foreground mb-2\">\n                    Style\n                  </Text>\n                  <Badge variant=\"outline\">\n                    {coverStyles.find(s => s.value === watchedStyle)?.label}\n                  </Badge>\n                </div>\n                <div className=\"flex flex-col items-center\">\n                  <Text variant=\"caption\" className=\"text-muted-foreground mb-2\">\n                    Colors\n                  </Text>\n                  <div className=\"flex gap-1\">\n                    {selectedPalette.slice(0, 3).map((color, index) => (\n                      <div\n                        key={index}\n                        className=\"w-4 h-4 rounded border\"\n                        style={{ backgroundColor: color }}\n                      />\n                    ))}\n                  </div>\n                </div>\n                <div className=\"flex flex-col items-center flex-1\">\n                  <Text variant=\"caption\" className=\"text-muted-foreground mb-2\">\n                    Cover Text\n                  </Text>\n                  <Text variant=\"small\" className=\"font-medium text-center\">\n                    {watchedText || \"Enter cover text\"}\n                  </Text>\n                </div>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between\">\n        <Button type=\"button\" onClick={onPrevious} variant=\"outline\">\n          Previous: Content Settings\n        </Button>\n        <Button \n          type=\"submit\" \n          disabled={!isValid || (generatedCovers.length > 0 && !selectedCover)}\n        >\n          Next: Book Details\n        </Button>\n      </div>\n    </form>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAhBA;;;;;;;;;;;;;;AAmBA,MAAM,cAAc,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;IAC3B,YAAY,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC9B,WAAW,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AAC/B;AAWA,MAAM,cAAc;IAClB;QAAE,OAAO;QAAU,OAAO;QAAU,aAAa;IAAgD;IACjG;QAAE,OAAO;QAAW,OAAO;QAAW,aAAa;IAAwC;IAC3F;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAA6C;IAClG;QAAE,OAAO;QAAgB,OAAO;QAAgB,aAAa;IAAqC;IAClG;QAAE,OAAO;QAAW,OAAO;QAAW,aAAa;IAAuC;IAC1F;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAA0C;CAChG;AAED,MAAM,gBAAgB;IACpB;QAAE,MAAM;QAAc,QAAQ;YAAC;YAAW;YAAW;YAAW;SAAU;IAAC;IAC3E;QAAE,MAAM;QAAgB,QAAQ;YAAC;YAAW;YAAW;YAAW;SAAU;IAAC;IAC7E;QAAE,MAAM;QAAiB,QAAQ;YAAC;YAAW;YAAW;YAAW;SAAU;IAAC;IAC9E;QAAE,MAAM;QAAgB,QAAQ;YAAC;YAAW;YAAW;YAAW;SAAU;IAAC;IAC7E;QAAE,MAAM;QAAiB,QAAQ;YAAC;YAAW;YAAW;YAAW;SAAU;IAAC;IAC9E;QAAE,MAAM;QAAe,QAAQ;YAAC;YAAW;YAAW;YAAW;SAAU;IAAC;CAC7E;AAED,MAAM,oBAAoB;IACxB;IACA;IACA;IACA;CACD;AAEM,SAAS,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAwB;;IAC1F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,KAAK,WAAW,IAAI,aAAa,CAAC,EAAE,CAAC,MAAM;IAC5G,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,KAAK,aAAa,IAAI;IAEjF,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,YAAY,KAAK,UAAU,IAAI;YAC/B,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI;QAC7C;IACF;IAEA,MAAM,eAAe,MAAM;IAC3B,MAAM,cAAc,MAAM;IAE1B,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,IAAI;YACF,+BAA+B;YAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,mBAAmB;IACrB;IAEA,MAAM,WAAW,CAAC;QAChB,SAAS;YACP,GAAG,QAAQ;YACX,aAAa;YACb;QACF;QACA;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU,aAAa;QAAW,WAAU;;0BAChD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAG9C,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAKnB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,SAAS,cAAc,MAAM,KAAK;gDACjD,WAAW,CAAC,kDAAkD,EAC5D,iBAAiB,MAAM,KAAK,GACxB,gCACA,mCACJ;;kEAEF,6LAAC;wDAAI,WAAU;kEAAuB,MAAM,KAAK;;;;;;kEACjD,6LAAC;wDAAI,WAAU;kEACZ,MAAM,WAAW;;;;;;;+CAXf,MAAM,KAAK;;;;;;;;;;oCAgBrB,OAAO,UAAU,kBAChB,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAC/B,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;0CAKhC,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAc;;;;;;kDAC/B,6LAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,cAAc,QAAQ,MAAM;gDAC3C,WAAW,CAAC,wCAAwC,EAClD,KAAK,SAAS,CAAC,qBAAqB,KAAK,SAAS,CAAC,QAAQ,MAAM,IAC7D,gCACA,mCACJ;;kEAEF,6LAAC;wDAAI,WAAU;kEAA4B,QAAQ,IAAI;;;;;;kEACvD,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;gEAEC,WAAU;gEACV,OAAO;oEAAE,iBAAiB;gEAAM;+DAF3B;;;;;;;;;;;+CAbN,QAAQ,IAAI;;;;;;;;;;;;;;;;0CAyBzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACX,GAAG,SAAS,YAAY;;;;;;oCAE1B,OAAO,SAAS,kBACf,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAC/B,OAAO,SAAS,CAAC,OAAO;;;;;;kDAG7B,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAAwB;;;;;;;;;;;;0CAK5D,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAEjC,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU,gBAAgB,CAAC,gBAAgB,CAAC;gDAC5C,MAAK;gDACL,SAAQ;0DAEP,6BACC;;sEACE,6LAAC,iJAAA,CAAA,iBAAc;4DAAC,MAAK;4DAAK,WAAU;;;;;;wDAAS;;iFAI/C;;sEACE,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;oCAO7C,CAAC,CAAC,gBAAgB,CAAC,WAAW,mBAC7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yIAAA,CAAA,OAAI;4CAAC,SAAQ;4CAAQ,WAAU;sDAAkB;;;;;;;;;;;oCAMrD,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;0DAC1B,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;0DAAwB;;;;;;;;;;;;oCAM3D,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;0DAAoC;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;0DACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,iBAAiB;gEAChC,WAAW,CAAC,gFAAgF,EAC1F,kBAAkB,QACd,uCACA,yCACJ;;kFAEF,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA4B;;;;;;8FAC3C,6LAAC;oFAAI,WAAU;;wFACZ;wFAAa;;;;;;;;;;;;;;;;;;oEAInB,kBAAkB,uBACjB,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAIrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEAAC,MAAK;wEAAK,SAAQ;wEAAU,WAAU;;0FAC5C,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGlC,6LAAC,qIAAA,CAAA,SAAM;wEAAC,MAAK;wEAAK,SAAQ;kFACxB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;;;;;;;;;;;;;uDA9BhB;;;;;;;;;;4CAqCb,+BACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAQ,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;4BAUtE,gBAAgB,gBAAgB,MAAM,GAAG,mBACxC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAA6B;;;;;;8DAG/D,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DACZ,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,eAAe;;;;;;;;;;;;sDAGtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAA6B;;;;;;8DAG/D,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACvC,6LAAC;4DAEC,WAAU;4DACV,OAAO;gEAAE,iBAAiB;4DAAM;2DAF3B;;;;;;;;;;;;;;;;sDAOb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAA6B;;;;;;8DAG/D,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAQ,WAAU;8DAC7B,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,SAAS;wBAAY,SAAQ;kCAAU;;;;;;kCAG7D,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,UAAU,CAAC,WAAY,gBAAgB,MAAM,GAAG,KAAK,CAAC;kCACvD;;;;;;;;;;;;;;;;;;AAMT;GA9SgB;;QAYV,iKAAA,CAAA,UAAO;;;KAZG", "debugId": null}}, {"offset": {"line": 6497, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 6540, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/wizard-steps/metadata-step.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { Switch } from \"@/components/ui/switch\"\nimport { BookOpen, DollarSign, Globe, Tag, Lightbulb, TrendingUp } from \"lucide-react\"\nimport { BookCreationData } from \"../new-book-wizard\"\n\nconst metadataSchema = z.object({\n  title: z.string().min(5, \"Title must be at least 5 characters\"),\n  description: z.string().min(50, \"Description must be at least 50 characters\"),\n  price: z.number().min(0.99).max(999.99),\n  language: z.string().min(1, \"Please select a language\"),\n})\n\ntype MetadataFormData = z.infer<typeof metadataSchema>\n\ninterface MetadataStepProps {\n  data: Partial<BookCreationData>\n  onUpdate: (data: Partial<BookCreationData>) => void\n  onNext: () => void\n  onPrevious: () => void\n}\n\nconst languages = [\n  { value: \"en\", label: \"English\" },\n  { value: \"es\", label: \"Spanish\" },\n  { value: \"fr\", label: \"French\" },\n  { value: \"de\", label: \"German\" },\n  { value: \"it\", label: \"Italian\" },\n  { value: \"pt\", label: \"Portuguese\" },\n  { value: \"zh\", label: \"Chinese\" },\n  { value: \"ja\", label: \"Japanese\" },\n]\n\nconst suggestedCategories = [\n  \"Business & Economics\",\n  \"Self-Help & Personal Development\",\n  \"Technology & Computers\",\n  \"Health & Fitness\",\n  \"Education & Teaching\",\n  \"Science & Mathematics\",\n  \"Arts & Entertainment\",\n  \"Travel & Tourism\",\n  \"Cooking & Food\",\n  \"Parenting & Family\",\n  \"Religion & Spirituality\",\n  \"History & Politics\",\n  \"Biography & Memoir\",\n  \"Fiction & Literature\",\n]\n\nconst pricingTiers = [\n  { range: \"$0.99 - $2.99\", label: \"Budget\", description: \"Entry-level pricing for new authors\" },\n  { range: \"$2.99 - $9.99\", label: \"Standard\", description: \"Most popular range for ebooks\" },\n  { range: \"$9.99 - $19.99\", label: \"Premium\", description: \"Higher value, comprehensive content\" },\n  { range: \"$19.99+\", label: \"Professional\", description: \"Expert-level, specialized content\" },\n]\n\nexport function MetadataStep({ data, onUpdate, onNext, onPrevious }: MetadataStepProps) {\n  const [selectedCategories, setSelectedCategories] = useState<string[]>(data.categories || [])\n  const [generateISBN, setGenerateISBN] = useState(true)\n  const [useAISuggestions, setUseAISuggestions] = useState(true)\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: { errors, isValid },\n  } = useForm<MetadataFormData>({\n    resolver: zodResolver(metadataSchema),\n    defaultValues: {\n      title: data.title || \"\",\n      description: data.description || \"\",\n      price: data.price || 4.99,\n      language: data.language || \"en\",\n    },\n  })\n\n  const watchedPrice = watch(\"price\")\n  const watchedTitle = watch(\"title\")\n  const watchedDescription = watch(\"description\")\n\n  const addCategory = (category: string) => {\n    if (!selectedCategories.includes(category) && selectedCategories.length < 3) {\n      setSelectedCategories([...selectedCategories, category])\n    }\n  }\n\n  const removeCategory = (category: string) => {\n    setSelectedCategories(selectedCategories.filter(c => c !== category))\n  }\n\n  const getPricingTier = (price: number) => {\n    if (price < 2.99) return pricingTiers[0]\n    if (price < 9.99) return pricingTiers[1]\n    if (price < 19.99) return pricingTiers[2]\n    return pricingTiers[3]\n  }\n\n  const generateSuggestedPrice = () => {\n    // Mock AI price suggestion based on genre and content\n    const basePrice = data.genre === \"Technology\" ? 7.99 : 4.99\n    const adjustment = Math.random() * 2 - 1 // Random adjustment\n    const suggestedPrice = Math.max(0.99, Math.round((basePrice + adjustment) * 100) / 100)\n    setValue(\"price\", suggestedPrice)\n  }\n\n  const generateDescription = () => {\n    if (!data.topic || !data.outline) return\n    \n    // Mock AI description generation\n    const mockDescription = `Discover the essential guide to ${data.topic.toLowerCase()}. This comprehensive book provides practical insights and actionable strategies that will transform your understanding and approach.\n\nIn this expertly crafted guide, you'll learn:\n${data.outline?.slice(0, 3).map(chapter => `• ${chapter.replace(/^Chapter \\d+:?\\s*/, '')}`).join('\\n')}\n\nWhether you're a beginner or looking to advance your knowledge, this book offers valuable perspectives and real-world applications that you can implement immediately. Join thousands of readers who have already benefited from these proven strategies and insights.\n\nPerfect for ${data.targetAudience?.toLowerCase() || 'anyone interested in this topic'}, this book combines theoretical knowledge with practical applications to deliver maximum value and impact.`\n\n    setValue(\"description\", mockDescription)\n  }\n\n  const onSubmit = (formData: MetadataFormData) => {\n    const isbn = generateISBN ? `978-${Math.random().toString().slice(2, 15)}` : undefined\n    \n    onUpdate({\n      ...formData,\n      categories: selectedCategories,\n      isbn,\n    })\n    onNext()\n  }\n\n  return (\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <BookOpen className=\"h-5 w-5 text-primary\" />\n            Book Details & Metadata\n          </CardTitle>\n          <CardDescription>\n            Add the essential information that will help readers discover and understand your book.\n            This metadata is crucial for marketing and sales.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Title */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"title\">Book Title *</Label>\n            <Input\n              id=\"title\"\n              placeholder=\"Enter a compelling, descriptive title\"\n              {...register(\"title\")}\n            />\n            {errors.title && (\n              <Text variant=\"caption\" className=\"text-destructive\">\n                {errors.title.message}\n              </Text>\n            )}\n            <Text variant=\"caption\" className=\"text-muted-foreground\">\n              Keep it under 60 characters for better visibility in marketplaces\n            </Text>\n          </div>\n\n          {/* Description */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"description\">Book Description *</Label>\n              {useAISuggestions && (\n                <Button\n                  type=\"button\"\n                  onClick={generateDescription}\n                  size=\"sm\"\n                  variant=\"outline\"\n                  disabled={!data.topic || !data.outline}\n                >\n                  <Lightbulb className=\"h-4 w-4 mr-2\" />\n                  AI Suggest\n                </Button>\n              )}\n            </div>\n            <Textarea\n              id=\"description\"\n              placeholder=\"Write a compelling description that highlights the value and benefits of your book...\"\n              className=\"min-h-[120px]\"\n              {...register(\"description\")}\n            />\n            {errors.description && (\n              <Text variant=\"caption\" className=\"text-destructive\">\n                {errors.description.message}\n              </Text>\n            )}\n            <div className=\"flex justify-between text-sm text-muted-foreground\">\n              <span>\n                {watchedDescription?.length || 0} / 4000 characters\n              </span>\n              <span>\n                Aim for 150-300 words for optimal engagement\n              </span>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Categories */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 mb-4\">\n              <Tag className=\"h-4 w-4 text-blue-600\" />\n              <Label className=\"font-medium\">Categories (Max 3)</Label>\n            </div>\n            \n            {selectedCategories.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-3\">\n                {selectedCategories.map((category) => (\n                  <Badge\n                    key={category}\n                    variant=\"default\"\n                    className=\"cursor-pointer\"\n                    onClick={() => removeCategory(category)}\n                  >\n                    {category} ×\n                  </Badge>\n                ))}\n              </div>\n            )}\n\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n              {suggestedCategories\n                .filter(cat => !selectedCategories.includes(cat))\n                .slice(0, 12)\n                .map((category) => (\n                <button\n                  key={category}\n                  type=\"button\"\n                  onClick={() => addCategory(category)}\n                  disabled={selectedCategories.length >= 3}\n                  className=\"p-2 text-left text-sm border rounded-lg hover:bg-primary/5 hover:border-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {category}\n                </button>\n              ))}\n            </div>\n            \n            <Text variant=\"caption\" className=\"text-muted-foreground\">\n              Choose categories that best describe your book's content and target audience\n            </Text>\n          </div>\n\n          <Separator />\n\n          {/* Pricing */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <DollarSign className=\"h-4 w-4 text-green-600\" />\n                <Label className=\"font-medium\">Pricing Strategy</Label>\n              </div>\n              <Button\n                type=\"button\"\n                onClick={generateSuggestedPrice}\n                size=\"sm\"\n                variant=\"outline\"\n              >\n                <TrendingUp className=\"h-4 w-4 mr-2\" />\n                AI Suggest Price\n              </Button>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"price\">Price (USD) *</Label>\n                <div className=\"relative\">\n                  <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                  <Input\n                    id=\"price\"\n                    type=\"number\"\n                    min=\"0.99\"\n                    max=\"999.99\"\n                    step=\"0.01\"\n                    className=\"pl-10\"\n                    {...register(\"price\", { valueAsNumber: true })}\n                  />\n                </div>\n                {errors.price && (\n                  <Text variant=\"caption\" className=\"text-destructive\">\n                    {errors.price.message}\n                  </Text>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label>Pricing Tier</Label>\n                <div className=\"p-3 border rounded-lg bg-muted/30\">\n                  <div className=\"font-medium text-sm text-primary\">\n                    {getPricingTier(watchedPrice).label}\n                  </div>\n                  <div className=\"text-xs text-muted-foreground\">\n                    {getPricingTier(watchedPrice).description}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Pricing recommendations */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n              {pricingTiers.map((tier, index) => (\n                <div\n                  key={index}\n                  className={`p-3 border rounded-lg text-center cursor-pointer transition-colors ${\n                    getPricingTier(watchedPrice).label === tier.label\n                      ? \"border-primary bg-primary/5\"\n                      : \"border-border hover:bg-muted/50\"\n                  }`}\n                  onClick={() => {\n                    const prices = [1.99, 4.99, 12.99, 24.99]\n                    setValue(\"price\", prices[index])\n                  }}\n                >\n                  <div className=\"text-sm font-medium\">{tier.label}</div>\n                  <div className=\"text-xs text-muted-foreground\">{tier.range}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Language & ISBN */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>Language *</Label>\n              <Select\n                value={watch(\"language\")}\n                onValueChange={(value) => setValue(\"language\", value)}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select language\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {languages.map((lang) => (\n                    <SelectItem key={lang.value} value={lang.value}>\n                      <div className=\"flex items-center gap-2\">\n                        <Globe className=\"h-4 w-4\" />\n                        {lang.label}\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-3\">\n              <Label>ISBN Settings</Label>\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"generate-isbn\"\n                  checked={generateISBN}\n                  onCheckedChange={setGenerateISBN}\n                />\n                <Label htmlFor=\"generate-isbn\" className=\"text-sm\">\n                  Auto-generate ISBN\n                </Label>\n              </div>\n              <Text variant=\"caption\" className=\"text-muted-foreground\">\n                We'll generate a valid ISBN for your book automatically\n              </Text>\n            </div>\n          </div>\n\n          {/* AI Assistance Toggle */}\n          <div className=\"p-4 bg-primary/5 rounded-lg border border-primary/20\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <Text variant=\"small\" className=\"font-medium\">\n                  AI Writing Assistance\n                </Text>\n                <Text variant=\"caption\" className=\"text-muted-foreground\">\n                  Get AI-powered suggestions for titles, descriptions, and pricing\n                </Text>\n              </div>\n              <Switch\n                checked={useAISuggestions}\n                onCheckedChange={setUseAISuggestions}\n              />\n            </div>\n          </div>\n\n          {/* Metadata Summary */}\n          {watchedTitle && watchedDescription && (\n            <div className=\"p-4 bg-muted/30 rounded-lg border\">\n              <Text variant=\"small\" className=\"font-medium mb-3 text-muted-foreground\">\n                Metadata Preview:\n              </Text>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                <div>\n                  <Text variant=\"caption\" className=\"text-muted-foreground\">Title Length</Text>\n                  <div className={`font-medium ${watchedTitle.length > 60 ? 'text-orange-600' : 'text-green-600'}`}>\n                    {watchedTitle.length}/60\n                  </div>\n                </div>\n                <div>\n                  <Text variant=\"caption\" className=\"text-muted-foreground\">Description</Text>\n                  <div className={`font-medium ${watchedDescription.length < 150 ? 'text-orange-600' : 'text-green-600'}`}>\n                    {watchedDescription.length} chars\n                  </div>\n                </div>\n                <div>\n                  <Text variant=\"caption\" className=\"text-muted-foreground\">Categories</Text>\n                  <div className={`font-medium ${selectedCategories.length === 0 ? 'text-orange-600' : 'text-green-600'}`}>\n                    {selectedCategories.length}/3\n                  </div>\n                </div>\n                <div>\n                  <Text variant=\"caption\" className=\"text-muted-foreground\">Price</Text>\n                  <div className=\"font-medium text-primary\">\n                    ${watchedPrice.toFixed(2)}\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between\">\n        <Button type=\"button\" onClick={onPrevious} variant=\"outline\">\n          Previous: Cover Design\n        </Button>\n        <Button \n          type=\"submit\" \n          disabled={!isValid || selectedCategories.length === 0}\n        >\n          Next: Generate Book\n        </Button>\n      </div>\n    </form>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAhBA;;;;;;;;;;;;;;;;AAmBA,MAAM,iBAAiB,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;IAC9B,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IACzB,aAAa,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI;IAChC,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC;IAChC,UAAU,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AAC9B;AAWA,MAAM,YAAY;IAChB;QAAE,OAAO;QAAM,OAAO;IAAU;IAChC;QAAE,OAAO;QAAM,OAAO;IAAU;IAChC;QAAE,OAAO;QAAM,OAAO;IAAS;IAC/B;QAAE,OAAO;QAAM,OAAO;IAAS;IAC/B;QAAE,OAAO;QAAM,OAAO;IAAU;IAChC;QAAE,OAAO;QAAM,OAAO;IAAa;IACnC;QAAE,OAAO;QAAM,OAAO;IAAU;IAChC;QAAE,OAAO;QAAM,OAAO;IAAW;CAClC;AAED,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAiB,OAAO;QAAU,aAAa;IAAsC;IAC9F;QAAE,OAAO;QAAiB,OAAO;QAAY,aAAa;IAAgC;IAC1F;QAAE,OAAO;QAAkB,OAAO;QAAW,aAAa;IAAsC;IAChG;QAAE,OAAO;QAAW,OAAO;QAAgB,aAAa;IAAoC;CAC7F;AAEM,SAAS,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAqB;;IACpF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,KAAK,UAAU,IAAI,EAAE;IAC5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO,KAAK,KAAK,IAAI;YACrB,aAAa,KAAK,WAAW,IAAI;YACjC,OAAO,KAAK,KAAK,IAAI;YACrB,UAAU,KAAK,QAAQ,IAAI;QAC7B;IACF;IAEA,MAAM,eAAe,MAAM;IAC3B,MAAM,eAAe,MAAM;IAC3B,MAAM,qBAAqB,MAAM;IAEjC,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,mBAAmB,QAAQ,CAAC,aAAa,mBAAmB,MAAM,GAAG,GAAG;YAC3E,sBAAsB;mBAAI;gBAAoB;aAAS;QACzD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,mBAAmB,MAAM,CAAC,CAAA,IAAK,MAAM;IAC7D;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,MAAM,OAAO,YAAY,CAAC,EAAE;QACxC,IAAI,QAAQ,MAAM,OAAO,YAAY,CAAC,EAAE;QACxC,IAAI,QAAQ,OAAO,OAAO,YAAY,CAAC,EAAE;QACzC,OAAO,YAAY,CAAC,EAAE;IACxB;IAEA,MAAM,yBAAyB;QAC7B,sDAAsD;QACtD,MAAM,YAAY,KAAK,KAAK,KAAK,eAAe,OAAO;QACvD,MAAM,aAAa,KAAK,MAAM,KAAK,IAAI,EAAE,oBAAoB;;QAC7D,MAAM,iBAAiB,KAAK,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,YAAY,UAAU,IAAI,OAAO;QACnF,SAAS,SAAS;IACpB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,OAAO,EAAE;QAElC,iCAAiC;QACjC,MAAM,kBAAkB,CAAC,gCAAgC,EAAE,KAAK,KAAK,CAAC,WAAW,GAAG;;;AAGxF,EAAE,KAAK,OAAO,EAAE,MAAM,GAAG,GAAG,IAAI,CAAA,UAAW,CAAC,EAAE,EAAE,QAAQ,OAAO,CAAC,qBAAqB,KAAK,EAAE,KAAK,MAAM;;;;YAI3F,EAAE,KAAK,cAAc,EAAE,iBAAiB,kCAAkC,2GAA2G,CAAC;QAE9L,SAAS,eAAe;IAC1B;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,OAAO,eAAe,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG;QAE7E,SAAS;YACP,GAAG,QAAQ;YACX,YAAY;YACZ;QACF;QACA;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU,aAAa;QAAW,WAAU;;0BAChD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAG/C,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAKnB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACX,GAAG,SAAS,QAAQ;;;;;;oCAEtB,OAAO,KAAK,kBACX,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAC/B,OAAO,KAAK,CAAC,OAAO;;;;;;kDAGzB,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAAwB;;;;;;;;;;;;0CAM5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;4CAC5B,kCACC,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,MAAK;gDACL,SAAQ;gDACR,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,OAAO;;kEAEtC,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAK5C,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,WAAU;wCACT,GAAG,SAAS,cAAc;;;;;;oCAE5B,OAAO,WAAW,kBACjB,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAC/B,OAAO,WAAW,CAAC,OAAO;;;;;;kDAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDACE,oBAAoB,UAAU;oDAAE;;;;;;;0DAEnC,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAMV,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAc;;;;;;;;;;;;oCAGhC,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;wCAAI,WAAU;kDACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC,oIAAA,CAAA,QAAK;gDAEJ,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,eAAe;;oDAE7B;oDAAS;;+CALL;;;;;;;;;;kDAWb,6LAAC;wCAAI,WAAU;kDACZ,oBACE,MAAM,CAAC,CAAA,MAAO,CAAC,mBAAmB,QAAQ,CAAC,MAC3C,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,yBACN,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,UAAU,mBAAmB,MAAM,IAAI;gDACvC,WAAU;0DAET;+CANI;;;;;;;;;;kDAWX,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAU,WAAU;kDAAwB;;;;;;;;;;;;0CAK5D,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAEjC,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,MAAK;gDACL,SAAQ;;kEAER,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAK3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,WAAU;gEACT,GAAG,SAAS,SAAS;oEAAE,eAAe;gEAAK,EAAE;;;;;;;;;;;;oDAGjD,OAAO,KAAK,kBACX,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAU,WAAU;kEAC/B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0DAK3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,eAAe,cAAc,KAAK;;;;;;0EAErC,6LAAC;gEAAI,WAAU;0EACZ,eAAe,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kDAOjD,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;gDAEC,WAAW,CAAC,mEAAmE,EAC7E,eAAe,cAAc,KAAK,KAAK,KAAK,KAAK,GAC7C,gCACA,mCACJ;gDACF,SAAS;oDACP,MAAM,SAAS;wDAAC;wDAAM;wDAAM;wDAAO;qDAAM;oDACzC,SAAS,SAAS,MAAM,CAAC,MAAM;gDACjC;;kEAEA,6LAAC;wDAAI,WAAU;kEAAuB,KAAK,KAAK;;;;;;kEAChD,6LAAC;wDAAI,WAAU;kEAAiC,KAAK,KAAK;;;;;;;+CAZrD;;;;;;;;;;;;;;;;0CAmBb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,MAAM;gDACb,eAAe,CAAC,QAAU,SAAS,YAAY;;kEAE/C,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;kEACX,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,aAAU;gEAAkB,OAAO,KAAK,KAAK;0EAC5C,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,KAAK,KAAK;;;;;;;+DAHE,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;kDAWnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS;wDACT,iBAAiB;;;;;;kEAEnB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAgB,WAAU;kEAAU;;;;;;;;;;;;0DAIrD,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAU,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAO9D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAQ,WAAU;8DAAc;;;;;;8DAG9C,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;sDAI5D,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,iBAAiB;;;;;;;;;;;;;;;;;4BAMtB,gBAAgB,oCACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAQ,WAAU;kDAAyC;;;;;;kDAGzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAC1D,6LAAC;wDAAI,WAAW,CAAC,YAAY,EAAE,aAAa,MAAM,GAAG,KAAK,oBAAoB,kBAAkB;;4DAC7F,aAAa,MAAM;4DAAC;;;;;;;;;;;;;0DAGzB,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAC1D,6LAAC;wDAAI,WAAW,CAAC,YAAY,EAAE,mBAAmB,MAAM,GAAG,MAAM,oBAAoB,kBAAkB;;4DACpG,mBAAmB,MAAM;4DAAC;;;;;;;;;;;;;0DAG/B,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAC1D,6LAAC;wDAAI,WAAW,CAAC,YAAY,EAAE,mBAAmB,MAAM,KAAK,IAAI,oBAAoB,kBAAkB;;4DACpG,mBAAmB,MAAM;4DAAC;;;;;;;;;;;;;0DAG/B,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAC1D,6LAAC;wDAAI,WAAU;;4DAA2B;4DACtC,aAAa,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,SAAS;wBAAY,SAAQ;kCAAU;;;;;;kCAG7D,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,UAAU,CAAC,WAAW,mBAAmB,MAAM,KAAK;kCACrD;;;;;;;;;;;;;;;;;;AAMT;GA7XgB;;QAWV,iKAAA,CAAA,UAAO;;;KAXG", "debugId": null}}, {"offset": {"line": 7585, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst tabsListVariants = cva(\n  \"inline-flex items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-surface-100\",\n        outline: \"bg-transparent border border-border\",\n        underline: \"bg-transparent border-b border-border rounded-none p-0\",\n        pills: \"bg-surface-100 gap-1\",\n      },\n      size: {\n        sm: \"h-8 text-xs\",\n        default: \"h-10 text-sm\",\n        lg: \"h-12 text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst tabsTriggerVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n        outline: \"border border-transparent data-[state=active]:border-primary data-[state=active]:bg-primary/5 data-[state=active]:text-primary\",\n        underline: \"rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary px-4 py-3\",\n        pills: \"rounded-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\",\n      },\n      size: {\n        sm: \"px-2 py-1 text-xs\",\n        default: \"px-3 py-1.5 text-sm\",\n        lg: \"px-4 py-2 text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst Tabs = TabsPrimitive.Root\n\nexport interface TabsListProps\n  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>,\n    VariantProps<typeof tabsListVariants> {}\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  TabsListProps\n>(({ className, variant, size, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(tabsListVariants({ variant, size }), className)}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nexport interface TabsTriggerProps\n  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>,\n    VariantProps<typeof tabsTriggerVariants> {\n  /** Icon to display before text */\n  icon?: React.ReactNode\n  /** Badge/counter to display */\n  badge?: React.ReactNode\n}\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  TabsTriggerProps\n>(({ className, variant, size, icon, badge, children, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(tabsTriggerVariants({ variant, size }), className)}\n    {...props}\n  >\n    <div className=\"flex items-center gap-2\">\n      {icon && <span className=\"shrink-0\">{icon}</span>}\n      <span>{children}</span>\n      {badge && <span className=\"shrink-0\">{badge}</span>}\n    </div>\n  </TabsPrimitive.Trigger>\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent, tabsListVariants, tabsTriggerVariants }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,mBAAmB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACzB,yFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,sBAAsB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC5B,mSACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAM/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,oBACzC,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;YAAE;YAAS;QAAK,IAAI;QAClD,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAWrD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;YAAE;YAAS;QAAK,IAAI;QACrD,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;;gBACZ,sBAAQ,6LAAC;oBAAK,WAAU;8BAAY;;;;;;8BACrC,6LAAC;8BAAM;;;;;;gBACN,uBAAS,6LAAC;oBAAK,WAAU;8BAAY;;;;;;;;;;;;;;;;;;AAI5C,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 7731, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/wizard-steps/review-step.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport { \n  BookOpen, \n  Eye, \n  Download, \n  Edit3, \n  Share2, \n  CheckCircle,\n  Image,\n  FileText,\n  Star,\n  Calendar,\n  DollarSign,\n  Users\n} from \"lucide-react\"\nimport { BookCreationData } from \"../new-book-wizard\"\n\ninterface ReviewStepProps {\n  data: Partial<BookCreationData>\n  onComplete: () => void\n  onPrevious: () => void\n}\n\nconst mockChapters = [\n  \"Introduction: Understanding the Problem\",\n  \"Chapter 1: Fundamentals and Core Concepts\", \n  \"Chapter 2: Getting Started - First Steps\",\n  \"Chapter 3: Advanced Techniques and Strategies\",\n  \"Chapter 4: Common Challenges and Solutions\",\n  \"Chapter 5: Real-World Applications\",\n  \"Chapter 6: Best Practices and Guidelines\",\n  \"Chapter 7: Future Trends and Innovations\",\n  \"Conclusion: Putting It All Together\"\n]\n\nconst mockContent = `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. \n\nDuis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n\n## Key Points\n\n1. **Important Concept**: This is a crucial understanding that will help you succeed\n2. **Practical Application**: Here's how you can implement this in real life\n3. **Common Mistakes**: Avoid these pitfalls that many beginners encounter\n\n> \"This is an inspiring quote that reinforces the chapter's main message.\"\n\nThe content continues with detailed explanations, examples, and actionable insights that provide real value to readers.`\n\nexport function ReviewStep({ data, onComplete, onPrevious }: ReviewStepProps) {\n  const [selectedChapter, setSelectedChapter] = useState(0)\n  const [isCreating, setIsCreating] = useState(false)\n\n  const handleCreateBook = async () => {\n    setIsCreating(true)\n    try {\n      // Simulate API call to create book\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      onComplete()\n    } catch (error) {\n      console.error(\"Failed to create book:\", error)\n    } finally {\n      setIsCreating(false)\n    }\n  }\n\n  const getWordCount = () => {\n    const baseWords = data.length === \"short\" ? 15000 : data.length === \"medium\" ? 25000 : 40000\n    return baseWords.toLocaleString()\n  }\n\n  const getReadingTime = () => {\n    const words = data.length === \"short\" ? 15000 : data.length === \"medium\" ? 25000 : 40000\n    return Math.round(words / 200) // Average reading speed: 200 words per minute\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Eye className=\"h-5 w-5 text-primary\" />\n            Review Your Book\n          </CardTitle>\n          <CardDescription>\n            Review the generated content, cover, and metadata before creating your book.\n            You can make edits or regenerate sections if needed.\n          </CardDescription>\n        </CardHeader>\n      </Card>\n\n      {/* Book Summary */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* Cover Preview */}\n            <div className=\"space-y-4\">\n              <Text variant=\"small\" className=\"font-medium text-muted-foreground\">\n                Book Cover\n              </Text>\n              <div className=\"aspect-[3/4] bg-gradient-to-br from-primary/10 to-primary/30 rounded-lg border-2 border-primary/20 flex items-center justify-center\">\n                <div className=\"text-center p-6\">\n                  <Heading variant=\"h4\" className=\"mb-4\">\n                    {data.title || \"Your Book Title\"}\n                  </Heading>\n                  <Text variant=\"body\" className=\"text-sm opacity-75\">\n                    {data.coverStyle ? `${data.coverStyle.charAt(0).toUpperCase()}${data.coverStyle.slice(1)} Style` : 'No Style Selected'}\n                  </Text>\n                </div>\n              </div>\n              <div className=\"flex gap-2\">\n                <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                  <Edit3 className=\"h-4 w-4 mr-2\" />\n                  Edit Cover\n                </Button>\n                <Button variant=\"outline\" size=\"sm\">\n                  <Download className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n\n            {/* Book Details */}\n            <div className=\"space-y-6\">\n              <div>\n                <Text variant=\"small\" className=\"font-medium text-muted-foreground mb-2\">\n                  Book Information\n                </Text>\n                <div className=\"space-y-3\">\n                  <div>\n                    <Text variant=\"small\" className=\"font-medium\">\n                      {data.title}\n                    </Text>\n                    <Text variant=\"caption\" className=\"text-muted-foreground block\">\n                      {data.genre} • {data.language?.toUpperCase()}\n                    </Text>\n                  </div>\n                  \n                  <div className=\"flex flex-wrap gap-2\">\n                    {data.categories?.map((category) => (\n                      <Badge key={category} variant=\"secondary\" className=\"text-xs\">\n                        {category}\n                      </Badge>\n                    ))}\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div className=\"flex items-center gap-2\">\n                      <FileText className=\"h-4 w-4 text-muted-foreground\" />\n                      <span>{data.chapterCount} chapters</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <BookOpen className=\"h-4 w-4 text-muted-foreground\" />\n                      <span>{getWordCount()} words</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n                      <span>{getReadingTime()} min read</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n                      <span>${data.price?.toFixed(2)}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <Separator />\n\n              <div>\n                <Text variant=\"small\" className=\"font-medium text-muted-foreground mb-2\">\n                  Description\n                </Text>\n                <ScrollArea className=\"h-32\">\n                  <Text variant=\"small\" className=\"text-muted-foreground leading-relaxed\">\n                    {data.description}\n                  </Text>\n                </ScrollArea>\n              </div>\n\n              <div>\n                <Text variant=\"small\" className=\"font-medium text-muted-foreground mb-2\">\n                  Target Audience\n                </Text>\n                <Text variant=\"small\" className=\"text-muted-foreground\">\n                  {data.targetAudience}\n                </Text>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Content Preview */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">Content Preview</CardTitle>\n          <CardDescription>\n            Review the generated chapters and content structure\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Tabs defaultValue=\"outline\" className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-2\">\n              <TabsTrigger value=\"outline\">Chapter Outline</TabsTrigger>\n              <TabsTrigger value=\"content\">Sample Content</TabsTrigger>\n            </TabsList>\n            \n            <TabsContent value=\"outline\" className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Text variant=\"small\" className=\"font-medium text-muted-foreground\">\n                    Chapter Structure\n                  </Text>\n                  <div className=\"space-y-2\">\n                    {(data.outline || mockChapters).map((chapter, index) => (\n                      <button\n                        key={index}\n                        onClick={() => setSelectedChapter(index)}\n                        className={`w-full p-3 text-left border rounded-lg transition-colors ${\n                          selectedChapter === index\n                            ? \"border-primary bg-primary/5\"\n                            : \"border-border hover:bg-muted/50\"\n                        }`}\n                      >\n                        <div className=\"flex items-center gap-3\">\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {index + 1}\n                          </Badge>\n                          <Text variant=\"small\" className=\"flex-1\">\n                            {chapter}\n                          </Text>\n                        </div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <Text variant=\"small\" className=\"font-medium text-muted-foreground\">\n                    Chapter Details\n                  </Text>\n                  <div className=\"p-4 border rounded-lg bg-muted/30\">\n                    <Text variant=\"small\" className=\"font-medium mb-2\">\n                      {(data.outline || mockChapters)[selectedChapter]}\n                    </Text>\n                    <div className=\"space-y-2 text-xs text-muted-foreground\">\n                      <div>Estimated length: 2,500-3,000 words</div>\n                      <div>Reading time: 12-15 minutes</div>\n                      <div>Key topics: 3-4 main concepts</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"content\" className=\"space-y-4\">\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Text variant=\"small\" className=\"font-medium text-muted-foreground\">\n                    Sample Content from Chapter 1\n                  </Text>\n                  <Button variant=\"outline\" size=\"sm\">\n                    <Edit3 className=\"h-4 w-4 mr-2\" />\n                    Edit Content\n                  </Button>\n                </div>\n                <ScrollArea className=\"h-80 p-4 border rounded-lg bg-background\">\n                  <div className=\"prose prose-sm max-w-none\">\n                    <Text variant=\"body\" className=\"leading-relaxed whitespace-pre-line\">\n                      {mockContent}\n                    </Text>\n                  </div>\n                </ScrollArea>\n              </div>\n            </TabsContent>\n          </Tabs>\n        </CardContent>\n      </Card>\n\n      {/* Quality Metrics */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <Text variant=\"small\" className=\"font-medium text-muted-foreground mb-4\">\n            Quality Assessment\n          </Text>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <Star className=\"h-5 w-5 text-yellow-500\" />\n              </div>\n              <div className=\"text-lg font-semibold\">9.2/10</div>\n              <Text variant=\"caption\" className=\"text-muted-foreground\">\n                Content Quality\n              </Text>\n            </div>\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <Users className=\"h-5 w-5 text-blue-500\" />\n              </div>\n              <div className=\"text-lg font-semibold\">8.8/10</div>\n              <Text variant=\"caption\" className=\"text-muted-foreground\">\n                Audience Match\n              </Text>\n            </div>\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <BookOpen className=\"h-5 w-5 text-green-500\" />\n              </div>\n              <div className=\"text-lg font-semibold\">9.5/10</div>\n              <Text variant=\"caption\" className=\"text-muted-foreground\">\n                Readability\n              </Text>\n            </div>\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <Image className=\"h-5 w-5 text-purple-500\" />\n              </div>\n              <div className=\"text-lg font-semibold\">9.0/10</div>\n              <Text variant=\"caption\" className=\"text-muted-foreground\">\n                Cover Appeal\n              </Text>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Final Actions */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div>\n              <Text variant=\"small\" className=\"font-medium\">\n                Ready to Create Your Book?\n              </Text>\n              <Text variant=\"caption\" className=\"text-muted-foreground\">\n                Your book will be saved and ready for publishing once created.\n              </Text>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              <Badge variant=\"default\">All Systems Ready</Badge>\n            </div>\n          </div>\n\n          <div className=\"flex gap-3\">\n            <Button\n              onClick={handleCreateBook}\n              disabled={isCreating}\n              className=\"flex-1\"\n              size=\"lg\"\n            >\n              {isCreating ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\n                  Creating Book...\n                </>\n              ) : (\n                <>\n                  <BookOpen className=\"h-5 w-5 mr-2\" />\n                  Create My Book\n                </>\n              )}\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              <Share2 className=\"h-5 w-5 mr-2\" />\n              Share Preview\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between\">\n        <Button \n          type=\"button\" \n          onClick={onPrevious} \n          variant=\"outline\"\n          disabled={isCreating}\n        >\n          Previous: Generation\n        </Button>\n        <Button \n          onClick={handleCreateBook}\n          disabled={isCreating}\n          variant=\"outline\"\n        >\n          {isCreating ? \"Creating...\" : \"Create Book\"}\n        </Button>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AAgCA,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,cAAc,CAAC;;;;;;;;;;;;uHAYkG,CAAC;AAEjH,SAAS,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAmB;;IAC1E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,mBAAmB;QACvB,cAAc;QACd,IAAI;YACF,mCAAmC;YACnC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAY,KAAK,MAAM,KAAK,UAAU,QAAQ,KAAK,MAAM,KAAK,WAAW,QAAQ;QACvF,OAAO,UAAU,cAAc;IACjC;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ,KAAK,MAAM,KAAK,UAAU,QAAQ,KAAK,MAAM,KAAK,WAAW,QAAQ;QACnF,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,8CAA8C;;IAC/E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAG1C,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;;;;;;0BAQrB,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAQ,WAAU;kDAAoC;;;;;;kDAGpE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yIAAA,CAAA,UAAO;oDAAC,SAAQ;oDAAK,WAAU;8DAC7B,KAAK,KAAK,IAAI;;;;;;8DAEjB,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAO,WAAU;8DAC5B,KAAK,UAAU,GAAG,GAAG,KAAK,UAAU,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG;;;;;;;;;;;;;;;;;kDAIzG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;kEAC5C,6LAAC,6MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;0DAC7B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;0DAAyC;;;;;;0DAGzE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC,yIAAA,CAAA,OAAI;gEAAC,SAAQ;gEAAQ,WAAU;0EAC7B,KAAK,KAAK;;;;;;0EAEb,6LAAC,yIAAA,CAAA,OAAI;gEAAC,SAAQ;gEAAU,WAAU;;oEAC/B,KAAK,KAAK;oEAAC;oEAAI,KAAK,QAAQ,EAAE;;;;;;;;;;;;;kEAInC,6LAAC;wDAAI,WAAU;kEACZ,KAAK,UAAU,EAAE,IAAI,CAAC,yBACrB,6LAAC,oIAAA,CAAA,QAAK;gEAAgB,SAAQ;gEAAY,WAAU;0EACjD;+DADS;;;;;;;;;;kEAMhB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;;4EAAM,KAAK,YAAY;4EAAC;;;;;;;;;;;;;0EAE3B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;;4EAAM;4EAAe;;;;;;;;;;;;;0EAExB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;;4EAAM;4EAAiB;;;;;;;;;;;;;0EAE1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,6LAAC;;4EAAK;4EAAE,KAAK,KAAK,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMpC,6LAAC,wIAAA,CAAA,YAAS;;;;;kDAEV,6LAAC;;0DACC,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;0DAAyC;;;;;;0DAGzE,6LAAC,6IAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAQ,WAAU;8DAC7B,KAAK,WAAW;;;;;;;;;;;;;;;;;kDAKvB,6LAAC;;0DACC,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;0DAAyC;;;;;;0DAGzE,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;0DAC7B,KAAK,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;0CAC/B,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAU,WAAU;;8CACrC,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAU;;;;;;sDAC7B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAU;;;;;;;;;;;;8CAG/B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAQ,WAAU;kEAAoC;;;;;;kEAGpE,6LAAC;wDAAI,WAAU;kEACZ,CAAC,KAAK,OAAO,IAAI,YAAY,EAAE,GAAG,CAAC,CAAC,SAAS,sBAC5C,6LAAC;gEAEC,SAAS,IAAM,mBAAmB;gEAClC,WAAW,CAAC,yDAAyD,EACnE,oBAAoB,QAChB,gCACA,mCACJ;0EAEF,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAChC,QAAQ;;;;;;sFAEX,6LAAC,yIAAA,CAAA,OAAI;4EAAC,SAAQ;4EAAQ,WAAU;sFAC7B;;;;;;;;;;;;+DAbA;;;;;;;;;;;;;;;;0DAqBb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAQ,WAAU;kEAAoC;;;;;;kEAGpE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yIAAA,CAAA,OAAI;gEAAC,SAAQ;gEAAQ,WAAU;0EAC7B,CAAC,KAAK,OAAO,IAAI,YAAY,CAAC,CAAC,gBAAgB;;;;;;0EAElD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAI;;;;;;kFACL,6LAAC;kFAAI;;;;;;kFACL,6LAAC;kFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOf,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAQ,WAAU;kEAAoC;;;;;;kEAGpE,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;;0EAC7B,6LAAC,6MAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAItC,6LAAC,6IAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAO,WAAU;kEAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjB,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,yIAAA,CAAA,OAAI;4BAAC,SAAQ;4BAAQ,WAAU;sCAAyC;;;;;;sCAGzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,6LAAC,yIAAA,CAAA,OAAI;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,6LAAC,yIAAA,CAAA,OAAI;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,6LAAC,yIAAA,CAAA,OAAI;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,6LAAC,yIAAA,CAAA,OAAI;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlE,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,yIAAA,CAAA,OAAI;4CAAC,SAAQ;4CAAQ,WAAU;sDAAc;;;;;;sDAG9C,6LAAC,yIAAA,CAAA,OAAI;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;;sCAI7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;oCACV,MAAK;8CAEJ,2BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAAmE;;qEAIpF;;0DACE,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;8CAK3C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,SAAQ;wBACR,UAAU;kCACX;;;;;;kCAGD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,SAAQ;kCAEP,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;AAKxC;GAtVgB;KAAA", "debugId": null}}, {"offset": {"line": 8900, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/wizard-steps/generation-progress-step.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\"\nimport { \n  Zap, \n  CheckCircle, \n  Clock, \n  Brain, \n  FileText, \n  Image, \n  BookOpen,\n  AlertCircle,\n  Pause,\n  Play,\n  RotateCcw\n} from \"lucide-react\"\nimport { BookCreationData } from \"../new-book-wizard\"\n\ninterface GenerationProgressStepProps {\n  data: Partial<BookCreationData>\n  isGenerating: boolean\n  onStartGeneration: () => void\n  onPrevious: () => void\n}\n\ninterface GenerationStep {\n  id: string\n  name: string\n  description: string\n  status: \"pending\" | \"running\" | \"completed\" | \"failed\"\n  progress: number\n  estimatedTime: string\n  icon: React.ComponentType<{ className?: string }>\n}\n\nexport function GenerationProgressStep({ \n  data, \n  isGenerating, \n  onStartGeneration, \n  onPrevious \n}: GenerationProgressStepProps) {\n  const [currentStepIndex, setCurrentStepIndex] = useState(0)\n  const [isPaused, setIsPaused] = useState(false)\n  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(\"12-15 minutes\")\n\n  const [generationSteps, setGenerationSteps] = useState<GenerationStep[]>([\n    {\n      id: \"outline\",\n      name: \"Content Outline\",\n      description: \"Creating detailed chapter outline and structure\",\n      status: \"pending\",\n      progress: 0,\n      estimatedTime: \"2-3 min\",\n      icon: FileText,\n    },\n    {\n      id: \"content\",\n      name: \"Content Generation\",\n      description: \"Writing chapters with AI-powered content creation\",\n      status: \"pending\",\n      progress: 0,\n      estimatedTime: \"8-10 min\",\n      icon: Brain,\n    },\n    {\n      id: \"cover\",\n      name: \"Cover Creation\",\n      description: \"Designing professional book cover\",\n      status: \"pending\",\n      progress: 0,\n      estimatedTime: \"1-2 min\",\n      icon: Image,\n    },\n    {\n      id: \"formatting\",\n      name: \"Formatting & Layout\",\n      description: \"Applying professional formatting and layout\",\n      status: \"pending\",\n      progress: 0,\n      estimatedTime: \"1-2 min\",\n      icon: BookOpen,\n    },\n  ])\n\n  // Simulate generation progress\n  useEffect(() => {\n    if (!isGenerating || isPaused) return\n\n    const interval = setInterval(() => {\n      setGenerationSteps(prev => {\n        const updated = [...prev]\n        const currentStep = updated[currentStepIndex]\n\n        if (currentStep && currentStep.status !== \"completed\") {\n          if (currentStep.status === \"pending\") {\n            currentStep.status = \"running\"\n          }\n\n          // Increase progress\n          currentStep.progress = Math.min(currentStep.progress + Math.random() * 15 + 5, 100)\n\n          // Mark as completed when progress reaches 100\n          if (currentStep.progress >= 100) {\n            currentStep.status = \"completed\"\n            currentStep.progress = 100\n            \n            // Move to next step\n            if (currentStepIndex < updated.length - 1) {\n              setCurrentStepIndex(currentStepIndex + 1)\n            }\n          }\n        }\n\n        return updated\n      })\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [isGenerating, currentStepIndex, isPaused])\n\n  const getTotalProgress = () => {\n    const completedSteps = generationSteps.filter(step => step.status === \"completed\").length\n    const currentProgress = generationSteps[currentStepIndex]?.progress || 0\n    return ((completedSteps * 100) + currentProgress) / generationSteps.length\n  }\n\n  const getStatusIcon = (step: GenerationStep) => {\n    switch (step.status) {\n      case \"completed\":\n        return <CheckCircle className=\"h-5 w-5 text-green-600\" />\n      case \"running\":\n        return <LoadingSpinner size=\"sm\" />\n      case \"failed\":\n        return <AlertCircle className=\"h-5 w-5 text-red-600\" />\n      default:\n        return <Clock className=\"h-5 w-5 text-muted-foreground\" />\n    }\n  }\n\n  const getStatusColor = (step: GenerationStep) => {\n    switch (step.status) {\n      case \"completed\":\n        return \"border-green-200 bg-green-50\"\n      case \"running\":\n        return \"border-primary bg-primary/5\"\n      case \"failed\":\n        return \"border-red-200 bg-red-50\"\n      default:\n        return \"border-border bg-background\"\n    }\n  }\n\n  const handlePauseResume = () => {\n    setIsPaused(!isPaused)\n  }\n\n  const handleRestart = () => {\n    setCurrentStepIndex(0)\n    setIsPaused(false)\n    setGenerationSteps(prev => prev.map(step => ({\n      ...step,\n      status: \"pending\",\n      progress: 0,\n    })))\n  }\n\n  const allStepsCompleted = generationSteps.every(step => step.status === \"completed\")\n\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Zap className=\"h-5 w-5 text-primary\" />\n            AI Book Generation\n          </CardTitle>\n          <CardDescription>\n            Our AI agents are working together to create your book. This process typically takes 12-15 minutes.\n            You can monitor the progress below.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Generation Overview */}\n          <div className=\"p-4 bg-primary/5 rounded-lg border border-primary/20\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n              <div>\n                <div className=\"text-lg font-semibold text-primary\">\n                  {data.chapterCount || 8}\n                </div>\n                <Text variant=\"caption\" className=\"text-muted-foreground\">\n                  Chapters\n                </Text>\n              </div>\n              <div>\n                <div className=\"text-lg font-semibold text-primary\">\n                  {data.length === \"short\" ? \"50-75\" : data.length === \"medium\" ? \"100-150\" : \"200-300\"}\n                </div>\n                <Text variant=\"caption\" className=\"text-muted-foreground\">\n                  Pages\n                </Text>\n              </div>\n              <div>\n                <div className=\"text-lg font-semibold text-primary\">\n                  {data.tone ? data.tone.charAt(0).toUpperCase() + data.tone.slice(1) : \"—\"}\n                </div>\n                <Text variant=\"caption\" className=\"text-muted-foreground\">\n                  Tone\n                </Text>\n              </div>\n              <div>\n                <div className=\"text-lg font-semibold text-primary\">\n                  {estimatedTimeRemaining}\n                </div>\n                <Text variant=\"caption\" className=\"text-muted-foreground\">\n                  Time Left\n                </Text>\n              </div>\n            </div>\n          </div>\n\n          {!isGenerating && !allStepsCompleted && (\n            <div className=\"text-center py-8\">\n              <div className=\"mb-4\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Zap className=\"h-8 w-8 text-primary\" />\n                </div>\n                <Heading variant=\"h3\" className=\"mb-2\">\n                  Ready to Generate Your Book\n                </Heading>\n                <Text variant=\"body\" className=\"text-muted-foreground max-w-md mx-auto\">\n                  All settings configured! Click the button below to start the AI generation process.\n                  This will create your complete book including content, cover, and formatting.\n                </Text>\n              </div>\n              <Button onClick={onStartGeneration} size=\"lg\" className=\"gap-2\">\n                <Zap className=\"h-5 w-5\" />\n                Start Generation\n              </Button>\n            </div>\n          )}\n\n          {(isGenerating || allStepsCompleted) && (\n            <>\n              {/* Overall Progress */}\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Text variant=\"small\" className=\"font-medium\">\n                    Overall Progress\n                  </Text>\n                  <div className=\"flex items-center gap-2\">\n                    <Badge variant={allStepsCompleted ? \"default\" : \"secondary\"}>\n                      {Math.round(getTotalProgress())}%\n                    </Badge>\n                    {isGenerating && !allStepsCompleted && (\n                      <div className=\"flex gap-1\">\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={handlePauseResume}\n                          className=\"h-8 w-8 p-0\"\n                        >\n                          {isPaused ? <Play className=\"h-4 w-4\" /> : <Pause className=\"h-4 w-4\" />}\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={handleRestart}\n                          className=\"h-8 w-8 p-0\"\n                        >\n                          <RotateCcw className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    )}\n                  </div>\n                </div>\n                <Progress value={getTotalProgress()} className=\"h-3\" />\n              </div>\n\n              <Separator />\n\n              {/* Step Progress */}\n              <div className=\"space-y-4\">\n                <Text variant=\"small\" className=\"font-medium\">\n                  Generation Steps\n                </Text>\n                <div className=\"space-y-3\">\n                  {generationSteps.map((step, index) => {\n                    const Icon = step.icon\n                    return (\n                      <div\n                        key={step.id}\n                        className={`p-4 border rounded-lg transition-all ${getStatusColor(step)}`}\n                      >\n                        <div className=\"flex items-center gap-4\">\n                          <div className=\"flex-shrink-0\">\n                            <Icon className=\"h-5 w-5 text-muted-foreground\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"flex items-center justify-between mb-1\">\n                              <Text variant=\"small\" className=\"font-medium\">\n                                {step.name}\n                              </Text>\n                              <div className=\"flex items-center gap-2\">\n                                <Text variant=\"caption\" className=\"text-muted-foreground\">\n                                  {step.estimatedTime}\n                                </Text>\n                                {getStatusIcon(step)}\n                              </div>\n                            </div>\n                            <Text variant=\"caption\" className=\"text-muted-foreground mb-2\">\n                              {step.description}\n                            </Text>\n                            {step.status === \"running\" && (\n                              <Progress value={step.progress} className=\"h-2\" />\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    )\n                  })}\n                </div>\n              </div>\n\n              {/* Status Messages */}\n              {isPaused && (\n                <div className=\"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                  <div className=\"flex items-center gap-2\">\n                    <Pause className=\"h-4 w-4 text-yellow-600\" />\n                    <Text variant=\"small\" className=\"text-yellow-800 font-medium\">\n                      Generation Paused\n                    </Text>\n                  </div>\n                  <Text variant=\"caption\" className=\"text-yellow-700 mt-1\">\n                    Click the play button to resume the generation process.\n                  </Text>\n                </div>\n              )}\n\n              {allStepsCompleted && (\n                <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg\">\n                  <div className=\"flex items-center gap-2\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                    <Text variant=\"small\" className=\"text-green-800 font-medium\">\n                      Generation Complete!\n                    </Text>\n                  </div>\n                  <Text variant=\"caption\" className=\"text-green-700 mt-1\">\n                    Your book has been successfully generated. Proceed to review the results.\n                  </Text>\n                </div>\n              )}\n            </>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between\">\n        <Button \n          type=\"button\" \n          onClick={onPrevious} \n          variant=\"outline\"\n          disabled={isGenerating && !isPaused}\n        >\n          Previous: Book Details\n        </Button>\n        <Button \n          disabled={!allStepsCompleted}\n          onClick={() => {/* Will be handled by parent wizard */}}\n        >\n          {allStepsCompleted ? \"Review Book\" : \"Generating...\"}\n        </Button>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AA0CO,SAAS,uBAAuB,EACrC,IAAI,EACJ,YAAY,EACZ,iBAAiB,EACjB,UAAU,EACkB;;IAC5B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACvE;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,eAAe;YACf,MAAM,iNAAA,CAAA,WAAQ;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,eAAe;YACf,MAAM,uMAAA,CAAA,QAAK;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,eAAe;YACf,MAAM,uMAAA,CAAA,QAAK;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,eAAe;YACf,MAAM,iNAAA,CAAA,WAAQ;QAChB;KACD;IAED,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,CAAC,gBAAgB,UAAU;YAE/B,MAAM,WAAW;6DAAY;oBAC3B;qEAAmB,CAAA;4BACjB,MAAM,UAAU;mCAAI;6BAAK;4BACzB,MAAM,cAAc,OAAO,CAAC,iBAAiB;4BAE7C,IAAI,eAAe,YAAY,MAAM,KAAK,aAAa;gCACrD,IAAI,YAAY,MAAM,KAAK,WAAW;oCACpC,YAAY,MAAM,GAAG;gCACvB;gCAEA,oBAAoB;gCACpB,YAAY,QAAQ,GAAG,KAAK,GAAG,CAAC,YAAY,QAAQ,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG;gCAE/E,8CAA8C;gCAC9C,IAAI,YAAY,QAAQ,IAAI,KAAK;oCAC/B,YAAY,MAAM,GAAG;oCACrB,YAAY,QAAQ,GAAG;oCAEvB,oBAAoB;oCACpB,IAAI,mBAAmB,QAAQ,MAAM,GAAG,GAAG;wCACzC,oBAAoB,mBAAmB;oCACzC;gCACF;4BACF;4BAEA,OAAO;wBACT;;gBACF;4DAAG;YAEH;oDAAO,IAAM,cAAc;;QAC7B;2CAAG;QAAC;QAAc;QAAkB;KAAS;IAE7C,MAAM,mBAAmB;QACvB,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;QACzF,MAAM,kBAAkB,eAAe,CAAC,iBAAiB,EAAE,YAAY;QACvE,OAAO,CAAC,AAAC,iBAAiB,MAAO,eAAe,IAAI,gBAAgB,MAAM;IAC5E;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,iBAAc;oBAAC,MAAK;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,YAAY,CAAC;IACf;IAEA,MAAM,gBAAgB;QACpB,oBAAoB;QACpB,YAAY;QACZ,mBAAmB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC3C,GAAG,IAAI;oBACP,QAAQ;oBACR,UAAU;gBACZ,CAAC;IACH;IAEA,MAAM,oBAAoB,gBAAgB,KAAK,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IAExE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAG1C,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAKnB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,KAAK,YAAY,IAAI;;;;;;8DAExB,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;sDAI5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,KAAK,MAAM,KAAK,UAAU,UAAU,KAAK,MAAM,KAAK,WAAW,YAAY;;;;;;8DAE9E,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;sDAI5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK;;;;;;8DAExE,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;sDAI5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ;;;;;;8DAEH,6LAAC,yIAAA,CAAA,OAAI;oDAAC,SAAQ;oDAAU,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;4BAO/D,CAAC,gBAAgB,CAAC,mCACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,6LAAC,yIAAA,CAAA,UAAO;gDAAC,SAAQ;gDAAK,WAAU;0DAAO;;;;;;0DAGvC,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAO,WAAU;0DAAyC;;;;;;;;;;;;kDAK1E,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAmB,MAAK;wCAAK,WAAU;;0DACtD,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;4BAMhC,CAAC,gBAAgB,iBAAiB,mBACjC;;kDAEE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAQ,WAAU;kEAAc;;;;;;kEAG9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,oBAAoB,YAAY;;oEAC7C,KAAK,KAAK,CAAC;oEAAoB;;;;;;;4DAEjC,gBAAgB,CAAC,mCAChB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,SAAS;wEACT,WAAU;kFAET,yBAAW,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;iGAAe,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAE9D,6LAAC,qIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,SAAS;wEACT,WAAU;kFAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM/B,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAoB,WAAU;;;;;;;;;;;;kDAGjD,6LAAC,wIAAA,CAAA,YAAS;;;;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAQ,WAAU;0DAAc;;;;;;0DAG9C,6LAAC;gDAAI,WAAU;0DACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;oDAC1B,MAAM,OAAO,KAAK,IAAI;oDACtB,qBACE,6LAAC;wDAEC,WAAW,CAAC,qCAAqC,EAAE,eAAe,OAAO;kEAEzE,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;;;;;;;;;;;8EAElB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,yIAAA,CAAA,OAAI;oFAAC,SAAQ;oFAAQ,WAAU;8FAC7B,KAAK,IAAI;;;;;;8FAEZ,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,yIAAA,CAAA,OAAI;4FAAC,SAAQ;4FAAU,WAAU;sGAC/B,KAAK,aAAa;;;;;;wFAEpB,cAAc;;;;;;;;;;;;;sFAGnB,6LAAC,yIAAA,CAAA,OAAI;4EAAC,SAAQ;4EAAU,WAAU;sFAC/B,KAAK,WAAW;;;;;;wEAElB,KAAK,MAAM,KAAK,2BACf,6LAAC,uIAAA,CAAA,WAAQ;4EAAC,OAAO,KAAK,QAAQ;4EAAE,WAAU;;;;;;;;;;;;;;;;;;uDAvB3C,KAAK,EAAE;;;;;gDA6BlB;;;;;;;;;;;;oCAKH,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAQ,WAAU;kEAA8B;;;;;;;;;;;;0DAIhE,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAU,WAAU;0DAAuB;;;;;;;;;;;;oCAM5D,mCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC,yIAAA,CAAA,OAAI;wDAAC,SAAQ;wDAAQ,WAAU;kEAA6B;;;;;;;;;;;;0DAI/D,6LAAC,yIAAA,CAAA,OAAI;gDAAC,SAAQ;gDAAU,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWpE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,SAAQ;wBACR,UAAU,gBAAgB,CAAC;kCAC5B;;;;;;kCAGD,6LAAC,qIAAA,CAAA,SAAM;wBACL,UAAU,CAAC;wBACX,SAAS,KAA6C;kCAErD,oBAAoB,gBAAgB;;;;;;;;;;;;;;;;;;AAK/C;GApVgB;KAAA", "debugId": null}}, {"offset": {"line": 9692, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/books/new-book-wizard.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { TopicSelectionStep } from \"./wizard-steps/topic-selection-step\"\nimport { ContentGenerationStep } from \"./wizard-steps/content-generation-step\"\nimport { CoverDesignStep } from \"./wizard-steps/cover-design-step\"\nimport { MetadataStep } from \"./wizard-steps/metadata-step\"\nimport { ReviewStep } from \"./wizard-steps/review-step\"\nimport { GenerationProgressStep } from \"./wizard-steps/generation-progress-step\"\nimport { CheckCircle, Circle } from \"lucide-react\"\n\nexport interface BookCreationData {\n  // Step 1: Topic Selection\n  topic: string\n  genre: string\n  targetAudience: string\n  keywords: string[]\n  industryFocus?: string[]\n  trendData?: any\n  \n  // Step 2: Content Generation\n  outline: string[]\n  tone: string\n  length: \"short\" | \"medium\" | \"long\"\n  chapterCount: number\n  \n  // Step 3: Cover Design\n  coverStyle: string\n  coverColors: string[]\n  coverText: string\n  selectedCover?: string\n  \n  // Step 4: Metadata\n  title: string\n  description: string\n  isbn?: string\n  price: number\n  categories: string[]\n  language: string\n  \n  // Generation tracking\n  generationStatus: \"pending\" | \"generating\" | \"completed\" | \"failed\"\n  generatedContent?: {\n    manuscript: string\n    coverUrl: string\n    metadata: any\n  }\n}\n\nconst steps = [\n  { id: 1, name: \"Topic\", description: \"Choose your book topic and niche\" },\n  { id: 2, name: \"Content\", description: \"Configure content generation\" },\n  { id: 3, name: \"Cover\", description: \"Design your book cover\" },\n  { id: 4, name: \"Details\", description: \"Add metadata and pricing\" },\n  { id: 5, name: \"Generate\", description: \"AI generates your book\" },\n  { id: 6, name: \"Review\", description: \"Review and finalize\" },\n]\n\ninterface NewBookWizardProps {\n  onComplete: (bookId: string) => void\n  onCancel: () => void\n}\n\nexport function NewBookWizard({ onComplete, onCancel }: NewBookWizardProps) {\n  const [currentStep, setCurrentStep] = useState(1)\n  const [bookData, setBookData] = useState<Partial<BookCreationData>>({\n    generationStatus: \"pending\",\n  })\n  const [isGenerating, setIsGenerating] = useState(false)\n\n  const progress = ((currentStep - 1) / (steps.length - 1)) * 100\n\n  const updateBookData = (stepData: Partial<BookCreationData>) => {\n    setBookData(prev => ({ ...prev, ...stepData }))\n  }\n\n  const handleNext = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1)\n    }\n  }\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const handleStepClick = (stepNumber: number) => {\n    // Only allow going to previous steps or current step\n    if (stepNumber <= currentStep) {\n      setCurrentStep(stepNumber)\n    }\n  }\n\n  const handleStartGeneration = async () => {\n    setIsGenerating(true)\n    setBookData(prev => ({ ...prev, generationStatus: \"generating\" }))\n    \n    try {\n      // Simulate API call to start book generation\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      // Mock successful generation\n      const mockGeneratedContent = {\n        manuscript: \"Generated manuscript content...\",\n        coverUrl: \"/images/generated-cover.jpg\",\n        metadata: { ...bookData },\n      }\n      \n      setBookData(prev => ({\n        ...prev,\n        generationStatus: \"completed\",\n        generatedContent: mockGeneratedContent,\n      }))\n      \n      setCurrentStep(6) // Move to review step\n    } catch (error) {\n      setBookData(prev => ({ ...prev, generationStatus: \"failed\" }))\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handleComplete = async () => {\n    try {\n      // Save book to database\n      const bookId = \"new-book-id\" // Mock ID\n      onComplete(bookId)\n    } catch (error) {\n      console.error(\"Failed to create book:\", error)\n    }\n  }\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <TopicSelectionStep\n            data={bookData}\n            onUpdate={updateBookData}\n            onNext={handleNext}\n            onCancel={onCancel}\n          />\n        )\n      case 2:\n        return (\n          <ContentGenerationStep\n            data={bookData}\n            onUpdate={updateBookData}\n            onNext={handleNext}\n            onPrevious={handlePrevious}\n          />\n        )\n      case 3:\n        return (\n          <CoverDesignStep\n            data={bookData}\n            onUpdate={updateBookData}\n            onNext={handleNext}\n            onPrevious={handlePrevious}\n          />\n        )\n      case 4:\n        return (\n          <MetadataStep\n            data={bookData}\n            onUpdate={updateBookData}\n            onNext={handleNext}\n            onPrevious={handlePrevious}\n          />\n        )\n      case 5:\n        return (\n          <GenerationProgressStep\n            data={bookData}\n            isGenerating={isGenerating}\n            onStartGeneration={handleStartGeneration}\n            onPrevious={handlePrevious}\n          />\n        )\n      case 6:\n        return (\n          <ReviewStep\n            data={bookData}\n            onComplete={handleComplete}\n            onPrevious={handlePrevious}\n          />\n        )\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Progress Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between mb-4\">\n            <div>\n              <CardTitle>Create Your Book</CardTitle>\n              <CardDescription>\n                Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.description}\n              </CardDescription>\n            </div>\n            <Badge variant=\"outline\" className=\"px-3 py-1\">\n              {Math.round(progress)}% Complete\n            </Badge>\n          </div>\n          <Progress value={progress} className=\"h-2\" />\n        </CardHeader>\n      </Card>\n\n      {/* Step Navigation */}\n      <div className=\"flex items-center justify-between space-x-4 overflow-x-auto pb-4\">\n        {steps.map((step, index) => (\n          <div key={step.id} className=\"flex items-center flex-shrink-0\">\n            <button\n              onClick={() => handleStepClick(step.id)}\n              disabled={step.id > currentStep}\n              className={`flex items-center gap-3 p-3 rounded-lg transition-colors min-w-0 ${\n                step.id === currentStep\n                  ? \"bg-primary text-primary-foreground\"\n                  : step.id < currentStep\n                  ? \"bg-primary/10 text-primary hover:bg-primary/20\"\n                  : \"bg-muted text-muted-foreground cursor-not-allowed\"\n              }`}\n            >\n              {step.id < currentStep ? (\n                <CheckCircle className=\"h-5 w-5 flex-shrink-0\" />\n              ) : (\n                <Circle className=\"h-5 w-5 flex-shrink-0\" />\n              )}\n              <div className=\"text-left\">\n                <div className=\"font-medium text-sm\">{step.name}</div>\n                <div className=\"text-xs opacity-75 hidden sm:block\">\n                  {step.description}\n                </div>\n              </div>\n            </button>\n            {index < steps.length - 1 && (\n              <Separator orientation=\"horizontal\" className=\"w-8 mx-2\" />\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Step Content */}\n      <div className=\"min-h-[500px]\">\n        {renderStepContent()}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAjBA;;;;;;;;;;;;;AAyDA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,MAAM;QAAS,aAAa;IAAmC;IACxE;QAAE,IAAI;QAAG,MAAM;QAAW,aAAa;IAA+B;IACtE;QAAE,IAAI;QAAG,MAAM;QAAS,aAAa;IAAyB;IAC9D;QAAE,IAAI;QAAG,MAAM;QAAW,aAAa;IAA2B;IAClE;QAAE,IAAI;QAAG,MAAM;QAAY,aAAa;IAAyB;IACjE;QAAE,IAAI;QAAG,MAAM;QAAU,aAAa;IAAsB;CAC7D;AAOM,SAAS,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAsB;;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;QAClE,kBAAkB;IACpB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW,AAAC,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK;IAE5D,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,QAAQ;YAAC,CAAC;IAC/C;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,MAAM,MAAM,EAAE;YAC9B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,qDAAqD;QACrD,IAAI,cAAc,aAAa;YAC7B,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,gBAAgB;QAChB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,kBAAkB;YAAa,CAAC;QAEhE,IAAI;YACF,6CAA6C;YAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,6BAA6B;YAC7B,MAAM,uBAAuB;gBAC3B,YAAY;gBACZ,UAAU;gBACV,UAAU;oBAAE,GAAG,QAAQ;gBAAC;YAC1B;YAEA,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,kBAAkB;oBAClB,kBAAkB;gBACpB,CAAC;YAED,eAAe,GAAG,sBAAsB;;QAC1C,EAAE,OAAO,OAAO;YACd,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,kBAAkB;gBAAS,CAAC;QAC9D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,wBAAwB;YACxB,MAAM,SAAS,cAAc,UAAU;;YACvC,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,+KAAA,CAAA,qBAAkB;oBACjB,MAAM;oBACN,UAAU;oBACV,QAAQ;oBACR,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,6LAAC,kLAAA,CAAA,wBAAqB;oBACpB,MAAM;oBACN,UAAU;oBACV,QAAQ;oBACR,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,4KAAA,CAAA,kBAAe;oBACd,MAAM;oBACN,UAAU;oBACV,QAAQ;oBACR,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,qKAAA,CAAA,eAAY;oBACX,MAAM;oBACN,UAAU;oBACV,QAAQ;oBACR,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,mLAAA,CAAA,yBAAsB;oBACrB,MAAM;oBACN,cAAc;oBACd,mBAAmB;oBACnB,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,mKAAA,CAAA,aAAU;oBACT,MAAM;oBACN,YAAY;oBACZ,YAAY;;;;;;YAGlB;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;;gDAAC;gDACT;gDAAY;gDAAK,MAAM,MAAM;gDAAC;gDAAG,KAAK,CAAC,cAAc,EAAE,EAAE;;;;;;;;;;;;;8CAGnE,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAChC,KAAK,KAAK,CAAC;wCAAU;;;;;;;;;;;;;sCAG1B,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,OAAO;4BAAU,WAAU;;;;;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wBAAkB,WAAU;;0CAC3B,6LAAC;gCACC,SAAS,IAAM,gBAAgB,KAAK,EAAE;gCACtC,UAAU,KAAK,EAAE,GAAG;gCACpB,WAAW,CAAC,iEAAiE,EAC3E,KAAK,EAAE,KAAK,cACR,uCACA,KAAK,EAAE,GAAG,cACV,mDACA,qDACJ;;oCAED,KAAK,EAAE,GAAG,4BACT,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAEpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAuB,KAAK,IAAI;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;0DACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;4BAItB,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAa,WAAU;;;;;;;uBAzBxC,KAAK,EAAE;;;;;;;;;;0BAgCrB,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;GA/LgB;KAAA", "debugId": null}}, {"offset": {"line": 10076, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/books/new/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useSession } from \"next-auth/react\"\nimport { useRouter } from \"next/navigation\"\nimport { DashboardLayout } from \"@/components/layout/dashboard-layout\"\nimport { PageHeader } from \"@/components/layout/page-header\"\nimport { ProtectedRoute } from \"@/components/auth/protected-route\"\nimport { NewBookWizard } from \"@/components/books/new-book-wizard\"\nimport { Button } from \"@/components/ui/button\"\nimport { ArrowLeft } from \"lucide-react\"\nimport Link from \"next/link\"\n\nexport default function NewBookPage() {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [isCreating, setIsCreating] = useState(false)\n\n  const user = session?.user\n    ? {\n        name: session.user.name || \"User\",\n        email: session.user.email || \"\",\n        avatar: session.user.image || undefined,\n      }\n    : undefined\n\n  const handleBookCreated = (bookId: string) => {\n    // Navigate to the newly created book\n    router.push(`/dashboard/books/${bookId}`)\n  }\n\n  const handleCancel = () => {\n    router.push(\"/dashboard/books\")\n  }\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"p-6\">\n          <PageHeader\n            title=\"Create New Book\"\n            description=\"Use AI to generate your next bestseller. Follow the steps below to create and publish your book.\"\n            breadcrumb={true}\n            actions={\n              <Link href=\"/dashboard/books\">\n                <Button variant=\"outline\" className=\"gap-2\">\n                  <ArrowLeft className=\"h-4 w-4\" />\n                  Back to Books\n                </Button>\n              </Link>\n            }\n          />\n\n          <div className=\"max-w-4xl mx-auto\">\n            <NewBookWizard\n              onComplete={handleBookCreated}\n              onCancel={handleCancel}\n            />\n          </div>\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,OAAO,SAAS,OAClB;QACE,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;QAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;QAC7B,QAAQ,QAAQ,IAAI,CAAC,KAAK,IAAI;IAChC,IACA;IAEJ,MAAM,oBAAoB,CAAC;QACzB,qCAAqC;QACrC,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,QAAQ;IAC1C;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC,mJAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,sJAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,aAAU;wBACT,OAAM;wBACN,aAAY;wBACZ,YAAY;wBACZ,uBACE,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;kCAOzC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uJAAA,CAAA,gBAAa;4BACZ,YAAY;4BACZ,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;GAlDwB;;QACI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}