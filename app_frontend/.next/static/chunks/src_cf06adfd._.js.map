{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-white hover:bg-primary-600 active:bg-primary-700 shadow-sm hover:shadow-md\",\n        secondary: \"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300 border border-secondary-300\",\n        outline: \"border border-border bg-background hover:bg-surface-200 hover:border-secondary-400 text-foreground\",\n        ghost: \"hover:bg-surface-200 hover:text-accent text-secondary-700\",\n        link: \"text-primary hover:text-primary-600 underline-offset-4 hover:underline p-0 h-auto\",\n        destructive: \"bg-error text-white hover:bg-red-600 active:bg-red-700 shadow-sm\",\n        success: \"bg-success text-white hover:bg-green-600 active:bg-green-700 shadow-sm\",\n      },\n      size: {\n        sm: \"h-8 px-3 py-1.5 text-xs rounded-md\",\n        default: \"h-10 px-4 py-2 text-sm\",\n        lg: \"h-12 px-6 py-3 text-base rounded-xl\",\n        xl: \"h-14 px-8 py-4 text-lg rounded-xl\",\n        icon: \"h-10 w-10 p-0\",\n        \"icon-sm\": \"h-8 w-8 p-0\",\n        \"icon-lg\": \"h-12 w-12 p-0\",\n      },\n      loading: {\n        true: \"cursor-not-allowed\",\n        false: \"\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      loading: false,\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  loadingText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant, \n    size, \n    loading = false,\n    loadingText,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled,\n    asChild = false, \n    ...props \n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    const isDisabled = disabled || loading\n    \n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, loading, className }))}\n        disabled={isDisabled}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            aria-hidden=\"true\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {!loading && leftIcon && <span className=\"mr-1\">{leftIcon}</span>}\n        {loading ? loadingText || children : children}\n        {!loading && rightIcon && <span className=\"ml-1\">{rightIcon}</span>}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,qYACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAaF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,WAAW,EACX,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,aAAa,YAAY;IAE/B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAS;QAAU;QACjE,UAAU;QACV,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,eAAY;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,WAAW,0BAAY,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD,UAAU,eAAe,WAAW;YACpC,CAAC,WAAW,2BAAa,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport {\n  LayoutDashboard,\n  BookOpen,\n  BarChart3,\n  TrendingUp,\n  Upload,\n  Settings,\n  HelpCircle,\n  X,\n  PlusCircle,\n  FileText,\n  DollarSign,\n} from \"lucide-react\"\n\ninterface SidebarProps {\n  isOpen?: boolean\n  onClose?: () => void\n}\n\nconst sidebarItems = [\n  {\n    title: \"Main\",\n    items: [\n      { name: \"Dashboard\", href: \"/dashboard\", icon: LayoutDashboard },\n      { name: \"My Books\", href: \"/dashboard/books\", icon: BookOpen },\n      { name: \"Analytics\", href: \"/dashboard/analytics\", icon: BarChart3 },\n      { name: \"Trends\", href: \"/dashboard/trends\", icon: TrendingUp },\n    ],\n  },\n  {\n    title: \"Publishing\",\n    items: [\n      { name: \"New Book\", href: \"/dashboard/books/new\", icon: PlusCircle },\n      { name: \"Manuscripts\", href: \"/dashboard/manuscripts\", icon: FileText },\n      { name: \"Publications\", href: \"/dashboard/publications\", icon: Upload },\n      { name: \"Revenue\", href: \"/dashboard/revenue\", icon: DollarSign },\n    ],\n  },\n  {\n    title: \"Support\",\n    items: [\n      { name: \"Settings\", href: \"/dashboard/settings\", icon: Settings },\n      { name: \"Help & Docs\", href: \"/dashboard/help\", icon: HelpCircle },\n    ],\n  },\n]\n\nexport function Sidebar({ isOpen = true, onClose }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <>\n      {/* Mobile backdrop */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black/50 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <aside\n        className={cn(\n          \"fixed inset-y-0 left-0 z-50 w-64 bg-surface transition-transform duration-300 md:relative md:translate-x-0\",\n          isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Mobile header */}\n          <div className=\"flex h-16 items-center justify-between border-b px-4 md:hidden\">\n            <span className=\"font-display text-lg font-semibold\">Menu</span>\n            <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n              <X className=\"h-5 w-5\" />\n              <span className=\"sr-only\">Close sidebar</span>\n            </Button>\n          </div>\n\n          {/* Navigation */}\n          <ScrollArea className=\"flex-1 px-3 py-4\">\n            <nav className=\"space-y-6\">\n              {sidebarItems.map((section) => (\n                <div key={section.title}>\n                  <h3 className=\"mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-muted-foreground\">\n                    {section.title}\n                  </h3>\n                  <div className=\"space-y-1\">\n                    {section.items.map((item) => {\n                      const isActive = pathname === item.href\n                      const Icon = item.icon\n                      return (\n                        <Link\n                          key={item.name}\n                          href={item.href}\n                          className={cn(\n                            \"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors\",\n                            isActive\n                              ? \"bg-primary/10 text-primary\"\n                              : \"text-muted-foreground hover:bg-muted hover:text-foreground\"\n                          )}\n                        >\n                          <Icon className=\"h-5 w-5\" />\n                          {item.name}\n                        </Link>\n                      )\n                    })}\n                  </div>\n                </div>\n              ))}\n            </nav>\n          </ScrollArea>\n\n          {/* Footer */}\n          <div className=\"border-t p-4\">\n            <div className=\"rounded-lg bg-primary/10 p-3\">\n              <h4 className=\"text-sm font-semibold\">Upgrade to Pro</h4>\n              <p className=\"mt-1 text-xs text-muted-foreground\">\n                Unlock advanced features and analytics\n              </p>\n              <Button size=\"sm\" className=\"mt-3 w-full\">\n                Upgrade Now\n              </Button>\n            </div>\n          </div>\n        </div>\n      </aside>\n    </>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AA0BA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;gBAAc,MAAM,+NAAA,CAAA,kBAAe;YAAC;YAC/D;gBAAE,MAAM;gBAAY,MAAM;gBAAoB,MAAM,iNAAA,CAAA,WAAQ;YAAC;YAC7D;gBAAE,MAAM;gBAAa,MAAM;gBAAwB,MAAM,qNAAA,CAAA,YAAS;YAAC;YACnE;gBAAE,MAAM;gBAAU,MAAM;gBAAqB,MAAM,qNAAA,CAAA,aAAU;YAAC;SAC/D;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAwB,MAAM,qNAAA,CAAA,aAAU;YAAC;YACnE;gBAAE,MAAM;gBAAe,MAAM;gBAA0B,MAAM,iNAAA,CAAA,WAAQ;YAAC;YACtE;gBAAE,MAAM;gBAAgB,MAAM;gBAA2B,MAAM,yMAAA,CAAA,SAAM;YAAC;YACtE;gBAAE,MAAM;gBAAW,MAAM;gBAAsB,MAAM,qNAAA,CAAA,aAAU;YAAC;SACjE;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAuB,MAAM,6MAAA,CAAA,WAAQ;YAAC;YAChE;gBAAE,MAAM;gBAAe,MAAM;gBAAmB,MAAM,iOAAA,CAAA,aAAU;YAAC;SAClE;IACH;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,IAAI,EAAE,OAAO,EAAgB;;IAC9D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8GACA,SAAS,kBAAkB;0BAG7B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS;;sDAC3C,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAK9B,6LAAC,6IAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;oDAClB,MAAM,WAAW,aAAa,KAAK,IAAI;oDACvC,MAAM,OAAO,KAAK,IAAI;oDACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,+BACA;;0EAGN,6LAAC;gEAAK,WAAU;;;;;;4DACf,KAAK,IAAI;;uDAVL,KAAK,IAAI;;;;;gDAapB;;;;;;;uCAvBM,QAAQ,KAAK;;;;;;;;;;;;;;;sCA+B7B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GAhFgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/container.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nexport interface ContainerProps extends HTMLAttributes<HTMLDivElement> {\n  maxWidth?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"full\"\n}\n\nconst Container = forwardRef<HTMLDivElement, ContainerProps>(\n  ({ className, maxWidth = \"2xl\", children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"mx-auto w-full px-4 sm:px-6 lg:px-8\",\n          {\n            \"max-w-screen-sm\": maxWidth === \"sm\",\n            \"max-w-screen-md\": maxWidth === \"md\",\n            \"max-w-screen-lg\": maxWidth === \"lg\",\n            \"max-w-screen-xl\": maxWidth === \"xl\",\n            \"max-w-screen-2xl\": maxWidth === \"2xl\",\n            \"max-w-full\": maxWidth === \"full\",\n          },\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\nContainer.displayName = \"Container\"\n\nexport { Container }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACzB,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uCACA;YACE,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,mBAAmB,aAAa;YAChC,oBAAoB,aAAa;YACjC,cAAc,aAAa;QAC7B,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEF,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Container } from \"@/components/ui/container\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { BookOpen, Github, Twitter, Linkedin } from \"lucide-react\"\n\nconst footerLinks = {\n  product: [\n    { name: \"Features\", href: \"#features\" },\n    { name: \"Pricing\", href: \"#pricing\" },\n    { name: \"API\", href: \"/docs/api\" },\n    { name: \"Integrations\", href: \"#integrations\" },\n  ],\n  company: [\n    { name: \"About\", href: \"/about\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Careers\", href: \"/careers\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  resources: [\n    { name: \"Documentation\", href: \"/docs\" },\n    { name: \"Guides\", href: \"/guides\" },\n    { name: \"Support\", href: \"/support\" },\n    { name: \"Status\", href: \"/status\" },\n  ],\n  legal: [\n    { name: \"Privacy\", href: \"/privacy\" },\n    { name: \"Terms\", href: \"/terms\" },\n    { name: \"Cookie Policy\", href: \"/cookies\" },\n    { name: \"License\", href: \"/license\" },\n  ],\n}\n\nconst socialLinks = [\n  { name: \"GitHub\", icon: Github, href: \"https://github.com\" },\n  { name: \"Twitter\", icon: Twitter, href: \"https://twitter.com\" },\n  { name: \"LinkedIn\", icon: Linkedin, href: \"https://linkedin.com\" },\n]\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-surface border-t\">\n      <Container>\n        <div className=\"py-12 md:py-16\">\n          {/* Main footer content */}\n          <div className=\"grid grid-cols-2 gap-8 md:grid-cols-5\">\n            {/* Brand section */}\n            <div className=\"col-span-2 md:col-span-1\">\n              <Link href=\"/\" className=\"flex items-center gap-2 mb-4\">\n                <BookOpen className=\"h-6 w-6 text-primary\" />\n                <span className=\"font-display text-lg font-semibold\">\n                  Publish AI\n                </span>\n              </Link>\n              <p className=\"text-sm text-muted-foreground\">\n                AI-powered book publishing platform for modern authors.\n              </p>\n              {/* Social links */}\n              <div className=\"flex gap-4 mt-6\">\n                {socialLinks.map((link) => {\n                  const Icon = link.icon\n                  return (\n                    <a\n                      key={link.name}\n                      href={link.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      <Icon className=\"h-5 w-5\" />\n                      <span className=\"sr-only\">{link.name}</span>\n                    </a>\n                  )\n                })}\n              </div>\n            </div>\n\n            {/* Links sections */}\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-sm mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          <Separator className=\"my-8\" />\n\n          {/* Bottom section */}\n          <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\n            <p className=\"text-sm text-muted-foreground\">\n              © {new Date().getFullYear()} Publish AI. All rights reserved.\n            </p>\n            <p className=\"text-sm text-muted-foreground\">\n              Made with ❤️ by the Publish AI team\n            </p>\n          </div>\n        </div>\n      </Container>\n    </footer>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAO,MAAM;QAAY;QACjC;YAAE,MAAM;YAAgB,MAAM;QAAgB;KAC/C;IACD,SAAS;QACP;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,WAAW;QACT;YAAE,MAAM;YAAiB,MAAM;QAAQ;QACvC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAU,MAAM;QAAU;KACnC;IACD,OAAO;QACL;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAU,MAAM,yMAAA,CAAA,SAAM;QAAE,MAAM;IAAqB;IAC3D;QAAE,MAAM;QAAW,MAAM,2MAAA,CAAA,UAAO;QAAE,MAAM;IAAsB;IAC9D;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;QAAE,MAAM;IAAuB;CAClE;AAEM,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC,wIAAA,CAAA,YAAS;sBACR,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAIvD,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,OAAO,KAAK,IAAI;4CACtB,qBACE,6LAAC;gDAEC,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAW,KAAK,IAAI;;;;;;;+CAP/B,KAAK,IAAI;;;;;wCAUpB;;;;;;;;;;;;0CAKJ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAa1B,6LAAC,wIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCAGrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAgC;oCACxC,IAAI,OAAO,WAAW;oCAAG;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD;KAvHgB", "debugId": null}}, {"offset": {"line": 1023, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border shadow-sm hover:shadow-md\",\n        elevated: \"border-border shadow-md hover:shadow-lg\",\n        outlined: \"border-border shadow-none hover:shadow-sm\",\n        ghost: \"border-transparent shadow-none hover:bg-surface-100\",\n        success: \"border-success bg-green-50 shadow-sm\",\n        warning: \"border-warning bg-yellow-50 shadow-sm\",\n        error: \"border-error bg-red-50 shadow-sm\",\n        info: \"border-info bg-blue-50 shadow-sm\",\n      },\n      size: {\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n      hoverable: {\n        true: \"cursor-pointer hover:scale-[1.02] active:scale-[0.98]\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      hoverable: false,\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, size, hoverable, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, size, hoverable }), className)}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-2\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-tight tracking-tight text-accent\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground leading-relaxed\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"pt-4\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center pt-4\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,8EACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;YACP,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,WAAW;YACT,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,WAAW;IACb;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClD,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAM;QAAU,IAAI;QACzD,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;;AAE5D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/error-boundary.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport { <PERSON>ert<PERSON>riangle } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from \"@/components/ui/card\"\n\ninterface Props {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; reset: () => void }>\n}\n\ninterface State {\n  hasError: boolean\n  error: Error | null\n}\n\nexport class ErrorBoundary extends React.Component<Props, State> {\n  constructor(props: Props) {\n    super(props)\n    this.state = { hasError: false, error: null }\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error(\"ErrorBoundary caught an error:\", error, errorInfo)\n    \n    // Log to monitoring service in production\n    if (process.env.NODE_ENV === 'production') {\n      this.logError(error, errorInfo)\n    }\n  }\n\n  private logError = async (error: Error, errorInfo: React.ErrorInfo) => {\n    try {\n      const errorReport = {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        errorId: crypto.randomUUID(),\n        timestamp: new Date().toISOString(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n      }\n      \n      // Send to backend error reporting endpoint\n      await fetch('/api/errors/report', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(errorReport),\n      })\n    } catch (reportingError) {\n      console.error('Failed to report error:', reportingError)\n    }\n  }\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: null })\n  }\n\n  render() {\n    if (this.state.hasError && this.state.error) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return (\n          <FallbackComponent\n            error={this.state.error}\n            reset={this.handleReset}\n          />\n        )\n      }\n\n      return <DefaultErrorFallback error={this.state.error} reset={this.handleReset} />\n    }\n\n    return this.props.children\n  }\n}\n\ninterface ErrorFallbackProps {\n  error: Error\n  reset: () => void\n}\n\nexport function DefaultErrorFallback({ error, reset }: ErrorFallbackProps) {\n  return (\n    <div className=\"flex items-center justify-center min-h-[400px] p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader>\n          <div className=\"flex items-center gap-2\">\n            <AlertTriangle className=\"h-5 w-5 text-destructive\" />\n            <CardTitle>Something went wrong</CardTitle>\n          </div>\n          <CardDescription>\n            An unexpected error occurred. Please try again.\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <details className=\"rounded-lg bg-muted p-3\">\n            <summary className=\"cursor-pointer text-sm font-medium\">\n              Error details\n            </summary>\n            <pre className=\"mt-2 whitespace-pre-wrap text-xs text-muted-foreground\">\n              {error.message}\n            </pre>\n          </details>\n        </CardContent>\n        <CardFooter className=\"flex gap-2\">\n          <Button onClick={reset}>Try again</Button>\n          <Button\n            variant=\"outline\"\n            onClick={() => window.location.href = \"/dashboard\"}\n          >\n            Go to Dashboard\n          </Button>\n        </CardFooter>\n      </Card>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AA+BQ;;AA7BR;AACA;AACA;AACA;AALA;;;;;;AAiBO,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;YAAO,OAAO;QAAK;IAC9C;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,0CAA0C;QAC1C,uCAA2C;;QAE3C;IACF;IAEQ,WAAW,OAAO,OAAc;QACtC,IAAI;YACF,MAAM,cAAc;gBAClB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;gBAClB,gBAAgB,UAAU,cAAc;gBACxC,SAAS,OAAO,UAAU;gBAC1B,WAAW,IAAI,OAAO,WAAW;gBACjC,KAAK,OAAO,QAAQ,CAAC,IAAI;gBACzB,WAAW,UAAU,SAAS;YAChC;YAEA,2CAA2C;YAC3C,MAAM,MAAM,sBAAsB;gBAChC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;QACF,EAAE,OAAO,gBAAgB;YACvB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF,EAAC;IAED,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAK;IAC/C,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBACE,6LAAC;oBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBACvB,OAAO,IAAI,CAAC,WAAW;;;;;;YAG7B;YAEA,qBAAO,6LAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC,WAAW;;;;;;QAC/E;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAOO,SAAS,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAsB;IACvE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAQ,WAAU;0CAAqC;;;;;;0CAGxD,6LAAC;gCAAI,WAAU;0CACZ,MAAM,OAAO;;;;;;;;;;;;;;;;;8BAIpB,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;sCAAO;;;;;;sCACxB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sCACvC;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAnCgB", "debugId": null}}, {"offset": {"line": 1376, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Sidebar } from \"./sidebar\"\nimport { Footer } from \"./footer\"\nimport { ErrorBoundary } from \"./error-boundary\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  className?: string\n  showFooter?: boolean\n  onMenuClick?: () => void\n}\n\nexport function DashboardLayout({ \n  children, \n  className,\n  showFooter = false,\n  onMenuClick\n}: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <div className=\"flex h-screen\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <main className={cn(\n          \"flex-1 overflow-y-auto\",\n          className\n        )}>\n          <ErrorBoundary>\n            {children}\n          </ErrorBoundary>\n        </main>\n      </div>\n      \n      {showFooter && <Footer />}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAeO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,aAAa,KAAK,EAClB,WAAW,EACU;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0IAAA,CAAA,UAAO;wBACN,QAAQ;wBACR,SAAS,IAAM,eAAe;;;;;;kCAGhC,6LAAC;wBAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,0BACA;kCAEA,cAAA,6LAAC,oJAAA,CAAA,gBAAa;sCACX;;;;;;;;;;;;;;;;;YAKN,4BAAc,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAG5B;GA7BgB;KAAA", "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/breadcrumb.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { ChevronRight, Home } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BreadcrumbItem {\n  label: string\n  href?: string\n}\n\ninterface BreadcrumbProps {\n  items?: BreadcrumbItem[]\n  className?: string\n}\n\nexport function Breadcrumb({ items, className }: BreadcrumbProps) {\n  const pathname = usePathname()\n  \n  // Auto-generate breadcrumbs from pathname if items not provided\n  const breadcrumbItems = items || generateBreadcrumbs(pathname)\n\n  if (breadcrumbItems.length === 0) return null\n\n  return (\n    <nav \n      aria-label=\"Breadcrumb\"\n      className={cn(\"flex items-center space-x-1 text-sm\", className)}\n    >\n      <Link\n        href=\"/dashboard\"\n        className=\"flex items-center text-muted-foreground hover:text-foreground transition-colors\"\n      >\n        <Home className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Home</span>\n      </Link>\n      \n      {breadcrumbItems.map((item, index) => {\n        const isLast = index === breadcrumbItems.length - 1\n        \n        return (\n          <div key={index} className=\"flex items-center\">\n            <ChevronRight className=\"h-4 w-4 text-muted-foreground mx-1\" />\n            {isLast || !item.href ? (\n              <span className=\"font-medium text-foreground\">\n                {item.label}\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"text-muted-foreground hover:text-foreground transition-colors\"\n              >\n                {item.label}\n              </Link>\n            )}\n          </div>\n        )\n      })}\n    </nav>\n  )\n}\n\nfunction generateBreadcrumbs(pathname: string): BreadcrumbItem[] {\n  const segments = pathname.split(\"/\").filter(Boolean)\n  const breadcrumbs: BreadcrumbItem[] = []\n  \n  // Skip the first segment if it's \"dashboard\"\n  const startIndex = segments[0] === \"dashboard\" ? 1 : 0\n  \n  segments.slice(startIndex).forEach((segment, index) => {\n    const href = \"/\" + segments.slice(0, startIndex + index + 1).join(\"/\")\n    const label = segment\n      .split(\"-\")\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(\" \")\n    \n    breadcrumbs.push({ label, href })\n  })\n  \n  return breadcrumbs\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAiBO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;;IAC9D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,kBAAkB,SAAS,oBAAoB;IAErD,IAAI,gBAAgB,MAAM,KAAK,GAAG,OAAO;IAEzC,qBACE,6LAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;;0BAErD,6LAAC,+JAAA,CAAA,UAAI;gBACH,MAAK;gBACL,WAAU;;kCAEV,6LAAC,sMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;YAG3B,gBAAgB,GAAG,CAAC,CAAC,MAAM;gBAC1B,MAAM,SAAS,UAAU,gBAAgB,MAAM,GAAG;gBAElD,qBACE,6LAAC;oBAAgB,WAAU;;sCACzB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBACvB,UAAU,CAAC,KAAK,IAAI,iBACnB,6LAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;iDAGb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,KAAK;;;;;;;mBAXP;;;;;YAgBd;;;;;;;AAGN;GA5CgB;;QACG,qIAAA,CAAA,cAAW;;;KADd;AA8ChB,SAAS,oBAAoB,QAAgB;IAC3C,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAgC,EAAE;IAExC,6CAA6C;IAC7C,MAAM,aAAa,QAAQ,CAAC,EAAE,KAAK,cAAc,IAAI;IAErD,SAAS,KAAK,CAAC,YAAY,OAAO,CAAC,CAAC,SAAS;QAC3C,MAAM,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,aAAa,QAAQ,GAAG,IAAI,CAAC;QAClE,MAAM,QAAQ,QACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAER,YAAY,IAAI,CAAC;YAAE;YAAO;QAAK;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1580, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/typography.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { HTMLAttributes, forwardRef } from \"react\"\n\nconst headingVariants = cva(\n  \"font-semibold text-accent tracking-tight\",\n  {\n    variants: {\n      variant: {\n        h1: \"text-4xl md:text-5xl lg:text-6xl\",\n        h2: \"text-3xl md:text-4xl lg:text-5xl\",\n        h3: \"text-2xl md:text-3xl lg:text-4xl\",\n        h4: \"text-xl md:text-2xl lg:text-3xl\",\n        h5: \"text-lg md:text-xl lg:text-2xl\",\n        h6: \"text-base md:text-lg lg:text-xl\",\n      },\n    },\n    defaultVariants: {\n      variant: \"h1\",\n    },\n  }\n)\n\nconst textVariants = cva(\n  \"\",\n  {\n    variants: {\n      variant: {\n        body: \"text-base text-foreground\",\n        lead: \"text-lg text-muted-foreground\",\n        small: \"text-sm text-foreground\",\n        muted: \"text-sm text-muted-foreground\",\n        caption: \"text-xs text-muted-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"body\",\n    },\n  }\n)\n\nexport interface HeadingProps\n  extends HTMLAttributes<HTMLHeadingElement>,\n    VariantProps<typeof headingVariants> {\n  as?: \"h1\" | \"h2\" | \"h3\" | \"h4\" | \"h5\" | \"h6\"\n}\n\nexport const Heading = forwardRef<HTMLHeadingElement, HeadingProps>(\n  ({ className, variant, as, ...props }, ref) => {\n    const Comp = as || variant || \"h1\"\n    return (\n      <Comp\n        className={cn(headingVariants({ variant: variant || as }), className)}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nHeading.displayName = \"Heading\"\n\nexport interface TextProps\n  extends HTMLAttributes<HTMLParagraphElement>,\n    VariantProps<typeof textVariants> {\n  as?: \"p\" | \"span\" | \"div\"\n}\n\nexport const Text = forwardRef<HTMLParagraphElement, TextProps>(\n  ({ className, variant, as: Comp = \"p\", ...props }, ref) => {\n    return (\n      <Comp\n        className={cn(textVariants({ variant }), className)}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nText.displayName = \"Text\""], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,kBAAkB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACxB,4CACA;IACE,UAAU;QACR,SAAS;YACP,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,IACA;IACE,UAAU;QACR,SAAS;YACP,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AASK,MAAM,wBAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IACrC,MAAM,OAAO,MAAM,WAAW;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;YAAE,SAAS,WAAW;QAAG,IAAI;QAC3D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,QAAQ,WAAW,GAAG;AAQf,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE;IACjD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;QAAQ,IAAI;QACzC,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,KAAK,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/page-header.tsx"], "sourcesContent": ["import { ReactNode } from \"react\"\nimport { Breadcrumb } from \"./breadcrumb\"\nimport { Heading, Text } from \"@/components/ui/typography\"\nimport { cn } from \"@/lib/utils\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  breadcrumb?: boolean\n  actions?: ReactNode\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  breadcrumb = true,\n  actions,\n  className,\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 pb-6\", className)}>\n      {breadcrumb && <Breadcrumb />}\n      \n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between\">\n        <div className=\"space-y-1\">\n          <Heading as=\"h1\" variant=\"h3\">\n            {title}\n          </Heading>\n          {description && (\n            <Text variant=\"muted\" className=\"max-w-2xl\">\n              {description}\n            </Text>\n          )}\n        </div>\n        \n        {actions && (\n          <div className=\"flex items-center gap-2 flex-shrink-0\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAUO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,aAAa,IAAI,EACjB,OAAO,EACP,SAAS,EACO;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAClC,4BAAc,6LAAC,6IAAA,CAAA,aAAU;;;;;0BAE1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,UAAO;gCAAC,IAAG;gCAAK,SAAQ;0CACtB;;;;;;4BAEF,6BACC,6LAAC,yIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAQ,WAAU;0CAC7B;;;;;;;;;;;;oBAKN,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;KA/BgB", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { Loader2 } from \"lucide-react\"\n\nexport interface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n}\n\nconst sizeClasses = {\n  sm: \"h-4 w-4\",\n  md: \"h-6 w-6\",\n  lg: \"h-8 w-8\",\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  return (\n    <Loader2\n      className={cn(\n        \"animate-spin text-primary\",\n        sizeClasses[size],\n        className\n      )}\n    />\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,6LAAC,oNAAA,CAAA,UAAO;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;KAVgB", "debugId": null}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/layout/loading.tsx"], "sourcesContent": ["import { LoadingSpinner } from \"@/components/ui/loading-spinner\"\n\ninterface LoadingProps {\n  fullScreen?: boolean\n  message?: string\n}\n\nexport function Loading({ fullScreen = false, message }: LoadingProps) {\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50\">\n        <div className=\"flex flex-col items-center gap-4\">\n          <LoadingSpinner size=\"lg\" />\n          {message && (\n            <p className=\"text-sm text-muted-foreground animate-pulse\">\n              {message}\n            </p>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center justify-center py-12\">\n      <div className=\"flex flex-col items-center gap-4\">\n        <LoadingSpinner size=\"md\" />\n        {message && (\n          <p className=\"text-sm text-muted-foreground animate-pulse\">\n            {message}\n          </p>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,QAAQ,EAAE,aAAa,KAAK,EAAE,OAAO,EAAgB;IACnE,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,iBAAc;wBAAC,MAAK;;;;;;oBACpB,yBACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;IAMb;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,iJAAA,CAAA,iBAAc;oBAAC,MAAK;;;;;;gBACpB,yBACC,6LAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;;;;;;;AAMb;KA5BgB", "debugId": null}}, {"offset": {"line": 1869, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/auth/protected-route.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession } from \"next-auth/react\"\nimport { useRouter } from \"next/navigation\"\nimport { useEffect } from \"react\"\nimport { Loading } from \"@/components/layout/loading\"\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({ \n  children, \n  redirectTo = \"/auth/login\" \n}: ProtectedRouteProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(redirectTo)\n    }\n  }, [status, router, redirectTo])\n\n  if (status === \"loading\") {\n    return <Loading fullScreen message=\"Checking authentication...\" />\n  }\n\n  if (status === \"unauthenticated\") {\n    return null\n  }\n\n  return <>{children}</>\n}\n\nexport function PublicRoute({ \n  children, \n  redirectTo = \"/dashboard\" \n}: ProtectedRouteProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      router.push(redirectTo)\n    }\n  }, [status, router, redirectTo])\n\n  if (status === \"loading\") {\n    return <Loading fullScreen message=\"Checking authentication...\" />\n  }\n\n  if (status === \"authenticated\") {\n    return null\n  }\n\n  return <>{children}</>\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,eAAe,EAC7B,QAAQ,EACR,aAAa,aAAa,EACN;;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW,mBAAmB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAQ;QAAQ;KAAW;IAE/B,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC,0IAAA,CAAA,UAAO;YAAC,UAAU;YAAC,SAAQ;;;;;;IACrC;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GAtBgB;;QAIoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;KALV;AAwBT,SAAS,YAAY,EAC1B,QAAQ,EACR,aAAa,YAAY,EACL;;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW,iBAAiB;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF;gCAAG;QAAC;QAAQ;QAAQ;KAAW;IAE/B,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC,0IAAA,CAAA,UAAO;YAAC,UAAU;YAAC,SAAQ;;;;;;IACrC;IAEA,IAAI,WAAW,iBAAiB;QAC9B,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;IAtBgB;;QAIoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;MALV", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst tabsListVariants = cva(\n  \"inline-flex items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-surface-100\",\n        outline: \"bg-transparent border border-border\",\n        underline: \"bg-transparent border-b border-border rounded-none p-0\",\n        pills: \"bg-surface-100 gap-1\",\n      },\n      size: {\n        sm: \"h-8 text-xs\",\n        default: \"h-10 text-sm\",\n        lg: \"h-12 text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst tabsTriggerVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n        outline: \"border border-transparent data-[state=active]:border-primary data-[state=active]:bg-primary/5 data-[state=active]:text-primary\",\n        underline: \"rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary px-4 py-3\",\n        pills: \"rounded-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\",\n      },\n      size: {\n        sm: \"px-2 py-1 text-xs\",\n        default: \"px-3 py-1.5 text-sm\",\n        lg: \"px-4 py-2 text-base\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst Tabs = TabsPrimitive.Root\n\nexport interface TabsListProps\n  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>,\n    VariantProps<typeof tabsListVariants> {}\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  TabsListProps\n>(({ className, variant, size, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(tabsListVariants({ variant, size }), className)}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nexport interface TabsTriggerProps\n  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>,\n    VariantProps<typeof tabsTriggerVariants> {\n  /** Icon to display before text */\n  icon?: React.ReactNode\n  /** Badge/counter to display */\n  badge?: React.ReactNode\n}\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  TabsTriggerProps\n>(({ className, variant, size, icon, badge, children, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(tabsTriggerVariants({ variant, size }), className)}\n    {...props}\n  >\n    <div className=\"flex items-center gap-2\">\n      {icon && <span className=\"shrink-0\">{icon}</span>}\n      <span>{children}</span>\n      {badge && <span className=\"shrink-0\">{badge}</span>}\n    </div>\n  </TabsPrimitive.Trigger>\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent, tabsListVariants, tabsTriggerVariants }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,mBAAmB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACzB,yFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,sBAAsB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC5B,mSACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAM/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,oBACzC,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;YAAE;YAAS;QAAK,IAAI;QAClD,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAWrD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;YAAE;YAAS;QAAK,IAAI;QACrD,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;;gBACZ,sBAAQ,6LAAC;oBAAK,WAAU;8BAAY;;;;;;8BACrC,6LAAC;8BAAM;;;;;;gBACN,uBAAS,6LAAC;oBAAK,WAAU;8BAAY;;;;;;;;;;;;;;;;;;AAI5C,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2121, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/stats-card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { TrendingUp, TrendingDown, Minus } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst statsCardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border hover:shadow-md\",\n        success: \"border-green-200 bg-green-50\",\n        warning: \"border-yellow-200 bg-yellow-50\",\n        error: \"border-red-200 bg-red-50\",\n        info: \"border-blue-200 bg-blue-50\",\n      },\n      size: {\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface StatsCardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof statsCardVariants> {\n  /** Main statistic value */\n  value: string | number\n  /** Label/title for the statistic */\n  label: string\n  /** Optional description */\n  description?: string\n  /** Icon to display */\n  icon?: React.ReactNode\n  /** Trend direction and percentage */\n  trend?: {\n    direction: \"up\" | \"down\" | \"neutral\"\n    value: number\n    label?: string\n  }\n  /** Show loading state */\n  loading?: boolean\n  /** Click handler */\n  onClick?: () => void\n}\n\nconst StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(\n  ({ \n    className, \n    variant, \n    size, \n    value, \n    label, \n    description, \n    icon, \n    trend, \n    loading = false,\n    onClick,\n    ...props \n  }, ref) => {\n    const getTrendIcon = () => {\n      if (!trend) return null\n      \n      switch (trend.direction) {\n        case \"up\":\n          return <TrendingUp className=\"h-4 w-4 text-green-600\" />\n        case \"down\":\n          return <TrendingDown className=\"h-4 w-4 text-red-600\" />\n        case \"neutral\":\n          return <Minus className=\"h-4 w-4 text-muted-foreground\" />\n        default:\n          return null\n      }\n    }\n\n    const getTrendColor = () => {\n      if (!trend) return \"\"\n      \n      switch (trend.direction) {\n        case \"up\":\n          return \"text-green-600\"\n        case \"down\":\n          return \"text-red-600\"\n        case \"neutral\":\n          return \"text-muted-foreground\"\n        default:\n          return \"\"\n      }\n    }\n\n    if (loading) {\n      return (\n        <div\n          ref={ref}\n          className={cn(statsCardVariants({ variant, size }), className)}\n          {...props}\n        >\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"h-4 w-20 bg-muted animate-pulse rounded\" />\n              {icon && <div className=\"h-5 w-5 bg-muted animate-pulse rounded\" />}\n            </div>\n            <div className=\"h-8 w-24 bg-muted animate-pulse rounded\" />\n            {description && <div className=\"h-3 w-32 bg-muted animate-pulse rounded\" />}\n            {trend && <div className=\"h-4 w-16 bg-muted animate-pulse rounded\" />}\n          </div>\n        </div>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          statsCardVariants({ variant, size }),\n          onClick && \"cursor-pointer hover:scale-[1.02] active:scale-[0.98]\",\n          className\n        )}\n        onClick={onClick}\n        {...props}\n      >\n        <div className=\"space-y-3\">\n          {/* Header with label and icon */}\n          <div className=\"flex items-center justify-between\">\n            <p className=\"text-sm font-medium text-muted-foreground\">{label}</p>\n            {icon && (\n              <div className=\"flex items-center justify-center h-8 w-8 rounded-lg bg-primary/10 text-primary\">\n                {icon}\n              </div>\n            )}\n          </div>\n\n          {/* Main value */}\n          <div className=\"space-y-1\">\n            <p className=\"text-2xl font-bold text-foreground\">{value}</p>\n            {description && (\n              <p className=\"text-xs text-muted-foreground\">{description}</p>\n            )}\n          </div>\n\n          {/* Trend indicator */}\n          {trend && (\n            <div className=\"flex items-center gap-1\">\n              {getTrendIcon()}\n              <span className={cn(\"text-sm font-medium\", getTrendColor())}>\n                {trend.value}%\n              </span>\n              {trend.label && (\n                <span className=\"text-xs text-muted-foreground\">\n                  {trend.label}\n                </span>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    )\n  }\n)\nStatsCard.displayName = \"StatsCard\"\n\nexport { StatsCard, statsCardVariants }"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,oBAAoB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC1B,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AA0BF,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC/B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,KAAK,EACL,WAAW,EACX,IAAI,EACJ,KAAK,EACL,UAAU,KAAK,EACf,OAAO,EACP,GAAG,OACJ,EAAE;IACD,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,OAAO;QAEnB,OAAQ,MAAM,SAAS;YACrB,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,OAAO,OAAO;QAEnB,OAAQ,MAAM,SAAS;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;gBAAE;gBAAS;YAAK,IAAI;YACnD,GAAG,KAAK;sBAET,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;4BACd,sBAAQ,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;;;;;;oBACd,6BAAe,6LAAC;wBAAI,WAAU;;;;;;oBAC9B,uBAAS,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kBAAkB;YAAE;YAAS;QAAK,IAClC,WAAW,yDACX;QAEF,SAAS;QACR,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;wBACzD,sBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;8BAMP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAClD,6BACC,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;gBAKjD,uBACC,6LAAC;oBAAI,WAAU;;wBACZ;sCACD,6LAAC;4BAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;;gCACxC,MAAM,KAAK;gCAAC;;;;;;;wBAEd,MAAM,KAAK,kBACV,6LAAC;4BAAK,WAAU;sCACb,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;AAQ5B;;AAEF,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/chart-wrapper.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst chartWrapperVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        outlined: \"border-2 border-border\",\n        elevated: \"border-border shadow-md\",\n        flush: \"border-0 shadow-none bg-transparent\",\n      },\n      size: {\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n      height: {\n        sm: \"h-64\",\n        default: \"h-80\",\n        lg: \"h-96\",\n        xl: \"h-[32rem]\",\n        auto: \"h-auto\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      height: \"default\",\n    },\n  }\n)\n\nexport interface ChartWrapperProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof chartWrapperVariants> {\n  /** Chart title */\n  title?: string\n  /** Chart description */\n  description?: string\n  /** Chart content */\n  children: React.ReactNode\n  /** Loading state */\n  loading?: boolean\n  /** Error state */\n  error?: string\n  /** Empty state */\n  empty?: boolean\n  /** Empty state message */\n  emptyMessage?: string\n  /** Header actions */\n  actions?: React.ReactNode\n  /** Footer content */\n  footer?: React.ReactNode\n}\n\nconst ChartWrapper = React.forwardRef<HTMLDivElement, ChartWrapperProps>(\n  ({ \n    className, \n    variant, \n    size, \n    height,\n    title, \n    description, \n    children, \n    loading = false,\n    error,\n    empty = false,\n    emptyMessage = \"No data available\",\n    actions,\n    footer,\n    ...props \n  }, ref) => {\n    const renderContent = () => {\n      if (loading) {\n        return (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"space-y-4 text-center\">\n              <div className=\"h-8 w-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto\" />\n              <p className=\"text-sm text-muted-foreground\">Loading chart data...</p>\n            </div>\n          </div>\n        )\n      }\n\n      if (error) {\n        return (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"space-y-2 text-center\">\n              <div className=\"h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mx-auto\">\n                <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <p className=\"text-sm font-medium text-red-900\">Failed to load chart</p>\n              <p className=\"text-xs text-red-600\">{error}</p>\n            </div>\n          </div>\n        )\n      }\n\n      if (empty) {\n        return (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"space-y-2 text-center\">\n              <div className=\"h-12 w-12 rounded-full bg-surface-100 flex items-center justify-center mx-auto\">\n                <svg className=\"h-6 w-6 text-muted-foreground\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">{emptyMessage}</p>\n            </div>\n          </div>\n        )\n      }\n\n      return children\n    }\n\n    return (\n      <div \n        ref={ref} \n        className={cn(chartWrapperVariants({ variant, size }), className)} \n        {...props}\n      >\n        {/* Header */}\n        {(title || description || actions) && (\n          <div className=\"flex items-start justify-between mb-4\">\n            <div className=\"space-y-1\">\n              {title && (\n                <h3 className=\"text-lg font-semibold text-foreground\">{title}</h3>\n              )}\n              {description && (\n                <p className=\"text-sm text-muted-foreground\">{description}</p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex items-center gap-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Chart content */}\n        <div className={cn(\n          \"relative\",\n          height && chartWrapperVariants({ height })\n        )}>\n          {renderContent()}\n        </div>\n\n        {/* Footer */}\n        {footer && (\n          <div className=\"mt-4 pt-4 border-t border-border\">\n            {footer}\n          </div>\n        )}\n      </div>\n    )\n  }\n)\nChartWrapper.displayName = \"ChartWrapper\"\n\n// Simple chart placeholder component for when no charting library is available\nexport interface ChartPlaceholderProps {\n  type: \"line\" | \"bar\" | \"pie\" | \"area\" | \"scatter\"\n  data?: any[]\n  className?: string\n}\n\nconst ChartPlaceholder = React.forwardRef<HTMLDivElement, ChartPlaceholderProps>(\n  ({ type, data, className }, ref) => {\n    const getChartIcon = () => {\n      switch (type) {\n        case \"line\":\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M3 3v18h18V3H3zm16 16H5V5h14v14z\"/>\n              <path fill=\"currentColor\" d=\"M7 14l2-2 2 2 4-4v2l-4 4-2-2-2 2z\"/>\n            </svg>\n          )\n        case \"bar\":\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M7 17h2v-4H7v4zm4 0h2V7h-2v10zm4 0h2v-7h-2v7z\"/>\n            </svg>\n          )\n        case \"pie\":\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 18c-4.41 0-8-3.59-8-8s3.59-8 8-8v8h8c0 4.41-3.59 8-8 8z\"/>\n            </svg>\n          )\n        case \"area\":\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M3 3v18h18V3H3zm16 16H5V5h14v14z\"/>\n              <path fill=\"currentColor\" d=\"M7 14l2-2 2 2 4-4v6H7v-2z\"/>\n            </svg>\n          )\n        default:\n          return (\n            <svg viewBox=\"0 0 24 24\" className=\"h-16 w-16 text-primary/20\">\n              <path fill=\"currentColor\" d=\"M3 3v18h18V3H3zm16 16H5V5h14v14z\"/>\n            </svg>\n          )\n      }\n    }\n\n    return (\n      <div \n        ref={ref}\n        className={cn(\n          \"flex flex-col items-center justify-center h-full space-y-2 text-center\",\n          className\n        )}\n      >\n        {getChartIcon()}\n        <p className=\"text-sm font-medium text-muted-foreground capitalize\">{type} Chart</p>\n        {data && (\n          <p className=\"text-xs text-muted-foreground\">{data.length} data points</p>\n        )}\n      </div>\n    )\n  }\n)\nChartPlaceholder.displayName = \"ChartPlaceholder\"\n\nexport { ChartWrapper, ChartPlaceholder, chartWrapperVariants }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAuB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC7B,4DACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,QAAQ;YACN,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,QAAQ;IACV;AACF;AA0BF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAClC,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,MAAM,EACN,KAAK,EACL,WAAW,EACX,QAAQ,EACR,UAAU,KAAK,EACf,KAAK,EACL,QAAQ,KAAK,EACb,eAAe,mBAAmB,EAClC,OAAO,EACP,MAAM,EACN,GAAG,OACJ,EAAE;IACD,MAAM,gBAAgB;QACpB,IAAI,SAAS;YACX,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;QAIrD;QAEA,IAAI,OAAO;YACT,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAuB,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC3E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;sCAChD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;QAI7C;QAEA,IAAI,OAAO;YACT,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAgC,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACpF,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;;;;;;QAItD;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;YAAE;YAAS;QAAK,IAAI;QACtD,GAAG,KAAK;;YAGR,CAAC,SAAS,eAAe,OAAO,mBAC/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,uBACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;4BAExD,6BACC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;oBAGjD,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAOT,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,YACA,UAAU,qBAAqB;oBAAE;gBAAO;0BAEvC;;;;;;YAIF,wBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;;AAEF,aAAa,WAAW,GAAG;AAS3B,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QACtC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE;IAC1B,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;;sCACjC,6LAAC;4BAAK,MAAK;4BAAe,GAAE;;;;;;sCAC5B,6LAAC;4BAAK,MAAK;4BAAe,GAAE;;;;;;;;;;;;YAGlC,KAAK;gBACH,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;8BACjC,cAAA,6LAAC;wBAAK,MAAK;wBAAe,GAAE;;;;;;;;;;;YAGlC,KAAK;gBACH,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;8BACjC,cAAA,6LAAC;wBAAK,MAAK;wBAAe,GAAE;;;;;;;;;;;YAGlC,KAAK;gBACH,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;;sCACjC,6LAAC;4BAAK,MAAK;4BAAe,GAAE;;;;;;sCAC5B,6LAAC;4BAAK,MAAK;4BAAe,GAAE;;;;;;;;;;;;YAGlC;gBACE,qBACE,6LAAC;oBAAI,SAAQ;oBAAY,WAAU;8BACjC,cAAA,6LAAC;wBAAK,MAAK;wBAAe,GAAE;;;;;;;;;;;QAGpC;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA;;YAGD;0BACD,6LAAC;gBAAE,WAAU;;oBAAwD;oBAAK;;;;;;;YACzE,sBACC,6LAAC;gBAAE,WAAU;;oBAAiC,KAAK,MAAM;oBAAC;;;;;;;;;;;;;AAIlE;;AAEF,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2815, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/activity-timeline.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { Check, Clock, AlertCircle, X, Info } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst timelineVariants = cva(\"relative\", {\n  variants: {\n    size: {\n      sm: \"space-y-4\",\n      default: \"space-y-6\",\n      lg: \"space-y-8\",\n    }\n  },\n  defaultVariants: {\n    size: \"default\",\n  },\n})\n\nconst timelineItemVariants = cva(\n  \"relative flex gap-4 pb-6 last:pb-0\",\n  {\n    variants: {\n      variant: {\n        default: \"\",\n        compact: \"gap-3 pb-4\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface TimelineItem {\n  id: string\n  title: string\n  description?: string\n  timestamp: string\n  status: \"completed\" | \"in-progress\" | \"pending\" | \"failed\" | \"info\"\n  icon?: React.ReactNode\n  metadata?: Record<string, any>\n  href?: string\n}\n\nexport interface ActivityTimelineProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof timelineVariants> {\n  /** Timeline items */\n  items: TimelineItem[]\n  /** Show connecting lines */\n  showLines?: boolean\n  /** Compact variant */\n  compact?: boolean\n  /** Loading state */\n  loading?: boolean\n  /** Number of skeleton items to show when loading */\n  skeletonCount?: number\n}\n\nconst ActivityTimeline = React.forwardRef<HTMLDivElement, ActivityTimelineProps>(\n  ({ \n    className, \n    size, \n    items, \n    showLines = true, \n    compact = false,\n    loading = false,\n    skeletonCount = 5,\n    ...props \n  }, ref) => {\n    const getStatusIcon = (status: TimelineItem[\"status\"], customIcon?: React.ReactNode) => {\n      if (customIcon) return customIcon\n      \n      switch (status) {\n        case \"completed\":\n          return <Check className=\"h-4 w-4\" />\n        case \"in-progress\":\n          return <Clock className=\"h-4 w-4\" />\n        case \"failed\":\n          return <X className=\"h-4 w-4\" />\n        case \"info\":\n          return <Info className=\"h-4 w-4\" />\n        case \"pending\":\n        default:\n          return <AlertCircle className=\"h-4 w-4\" />\n      }\n    }\n\n    const getStatusColor = (status: TimelineItem[\"status\"]) => {\n      switch (status) {\n        case \"completed\":\n          return \"bg-green-100 text-green-600 border-green-200\"\n        case \"in-progress\":\n          return \"bg-blue-100 text-blue-600 border-blue-200\"\n        case \"failed\":\n          return \"bg-red-100 text-red-600 border-red-200\"\n        case \"info\":\n          return \"bg-blue-100 text-blue-600 border-blue-200\"\n        case \"pending\":\n        default:\n          return \"bg-muted text-muted-foreground border-border\"\n      }\n    }\n\n    const getLineColor = (status: TimelineItem[\"status\"]) => {\n      switch (status) {\n        case \"completed\":\n          return \"bg-green-200\"\n        case \"in-progress\":\n          return \"bg-blue-200\"\n        case \"failed\":\n          return \"bg-red-200\"\n        case \"info\":\n          return \"bg-blue-200\"\n        case \"pending\":\n        default:\n          return \"bg-border\"\n      }\n    }\n\n    if (loading) {\n      return (\n        <div ref={ref} className={cn(timelineVariants({ size }), className)} {...props}>\n          {Array.from({ length: skeletonCount }).map((_, index) => (\n            <div key={index} className={cn(timelineItemVariants({ variant: compact ? \"compact\" : \"default\" }))}>\n              {/* Icon skeleton */}\n              <div className=\"flex-shrink-0 relative\">\n                <div className=\"h-8 w-8 rounded-full bg-muted animate-pulse\" />\n                {showLines && index < skeletonCount - 1 && (\n                  <div className=\"absolute top-8 left-1/2 w-0.5 h-6 bg-muted animate-pulse transform -translate-x-1/2\" />\n                )}\n              </div>\n              \n              {/* Content skeleton */}\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 w-32 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-48 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-20 bg-muted animate-pulse rounded\" />\n              </div>\n            </div>\n          ))}\n        </div>\n      )\n    }\n\n    return (\n      <div ref={ref} className={cn(timelineVariants({ size }), className)} {...props}>\n        {items.map((item, index) => {\n          const isLast = index === items.length - 1\n          const ItemComponent = item.href ? \"a\" : \"div\"\n          \n          return (\n            <div key={item.id} className={cn(timelineItemVariants({ variant: compact ? \"compact\" : \"default\" }))}>\n              {/* Timeline icon and line */}\n              <div className=\"flex-shrink-0 relative\">\n                <div\n                  className={cn(\n                    \"flex items-center justify-center h-8 w-8 rounded-full border-2\",\n                    getStatusColor(item.status)\n                  )}\n                >\n                  {getStatusIcon(item.status, item.icon)}\n                </div>\n                \n                {/* Connecting line */}\n                {showLines && !isLast && (\n                  <div\n                    className={cn(\n                      \"absolute top-8 left-1/2 w-0.5 h-full transform -translate-x-1/2\",\n                      getLineColor(item.status)\n                    )}\n                  />\n                )}\n              </div>\n\n              {/* Content */}\n              <ItemComponent\n                className={cn(\n                  \"flex-1 space-y-1\",\n                  item.href && \"hover:bg-surface-50 -m-2 p-2 rounded-lg transition-colors\"\n                )}\n                {...(item.href ? { href: item.href } : {})}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"text-sm font-medium text-foreground\">{item.title}</h4>\n                  <time className=\"text-xs text-muted-foreground\">{item.timestamp}</time>\n                </div>\n                \n                {item.description && (\n                  <p className=\"text-sm text-muted-foreground\">{item.description}</p>\n                )}\n                \n                {/* Metadata */}\n                {item.metadata && Object.keys(item.metadata).length > 0 && (\n                  <div className=\"flex flex-wrap gap-2 mt-2\">\n                    {Object.entries(item.metadata).map(([key, value]) => (\n                      <span\n                        key={key}\n                        className=\"inline-flex items-center px-2 py-1 rounded-md text-xs bg-surface-100 text-muted-foreground\"\n                      >\n                        <span className=\"font-medium mr-1\">{key}:</span>\n                        <span>{String(value)}</span>\n                      </span>\n                    ))}\n                  </div>\n                )}\n              </ItemComponent>\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nActivityTimeline.displayName = \"ActivityTimeline\"\n\nexport { ActivityTimeline, timelineVariants, timelineItemVariants }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,mBAAmB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAAE,YAAY;IACvC,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAEA,MAAM,uBAAuB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC7B,sCACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AA6BF,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OACtC,CAAC,EACC,SAAS,EACT,IAAI,EACJ,KAAK,EACL,YAAY,IAAI,EAChB,UAAU,KAAK,EACf,UAAU,KAAK,EACf,gBAAgB,CAAC,EACjB,GAAG,OACJ,EAAE;IACD,MAAM,gBAAgB,CAAC,QAAgC;QACrD,IAAI,YAAY,OAAO;QAEvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;YACtB,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;YACL;gBACE,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;gBAAE;YAAK,IAAI;YAAa,GAAG,KAAK;sBAC3E,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAc,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC7C,6LAAC;oBAAgB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;wBAAE,SAAS,UAAU,YAAY;oBAAU;;sCAE7F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;gCACd,aAAa,QAAQ,gBAAgB,mBACpC,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;mBAbT;;;;;;;;;;IAmBlB;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;YAAE;QAAK,IAAI;QAAa,GAAG,KAAK;kBAC3E,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,SAAS,UAAU,MAAM,MAAM,GAAG;YACxC,MAAM,gBAAgB,KAAK,IAAI,GAAG,MAAM;YAExC,qBACE,6LAAC;gBAAkB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;oBAAE,SAAS,UAAU,YAAY;gBAAU;;kCAE/F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,eAAe,KAAK,MAAM;0CAG3B,cAAc,KAAK,MAAM,EAAE,KAAK,IAAI;;;;;;4BAItC,aAAa,CAAC,wBACb,6LAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mEACA,aAAa,KAAK,MAAM;;;;;;;;;;;;kCAOhC,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oBACA,KAAK,IAAI,IAAI;wBAEd,GAAI,KAAK,IAAI,GAAG;4BAAE,MAAM,KAAK,IAAI;wBAAC,IAAI,CAAC,CAAC;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC,KAAK,KAAK;;;;;;kDAC/D,6LAAC;wCAAK,WAAU;kDAAiC,KAAK,SAAS;;;;;;;;;;;;4BAGhE,KAAK,WAAW,kBACf,6LAAC;gCAAE,WAAU;0CAAiC,KAAK,WAAW;;;;;;4BAI/D,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,QAAQ,EAAE,MAAM,GAAG,mBACpD,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC9C,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;;oDAAoB;oDAAI;;;;;;;0DACxC,6LAAC;0DAAM,OAAO;;;;;;;uCAJT;;;;;;;;;;;;;;;;;eA7CP,KAAK,EAAE;;;;;QAyDrB;;;;;;AAGN;;AAEF,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3158, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/quick-actions.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst quickActionsVariants = cva(\n  \"grid gap-4\",\n  {\n    variants: {\n      columns: {\n        1: \"grid-cols-1\",\n        2: \"grid-cols-1 sm:grid-cols-2\",\n        3: \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3\",\n        4: \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-4\",\n        auto: \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n      },\n      size: {\n        sm: \"gap-3\",\n        default: \"gap-4\",\n        lg: \"gap-6\",\n      }\n    },\n    defaultVariants: {\n      columns: \"auto\",\n      size: \"default\",\n    },\n  }\n)\n\nconst quickActionItemVariants = cva(\n  \"group relative flex items-center gap-3 rounded-lg border bg-card p-4 text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border hover:border-primary/20 hover:bg-primary/5\",\n        primary: \"border-primary/20 bg-primary/5 hover:bg-primary/10\",\n        secondary: \"border-secondary/20 bg-secondary/5 hover:bg-secondary/10\",\n        success: \"border-green-200 bg-green-50 hover:bg-green-100\",\n        warning: \"border-yellow-200 bg-yellow-50 hover:bg-yellow-100\",\n        error: \"border-red-200 bg-red-50 hover:bg-red-100\",\n      },\n      size: {\n        sm: \"p-3 text-sm\",\n        default: \"p-4\",\n        lg: \"p-6 text-lg\",\n      },\n      orientation: {\n        horizontal: \"flex-row\",\n        vertical: \"flex-col text-center\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      orientation: \"horizontal\",\n    },\n  }\n)\n\nexport interface QuickActionItem {\n  id: string\n  title: string\n  description?: string\n  icon: React.ReactNode\n  href?: string\n  onClick?: () => void\n  variant?: \"default\" | \"primary\" | \"secondary\" | \"success\" | \"warning\" | \"error\"\n  disabled?: boolean\n  badge?: string | number\n  loading?: boolean\n}\n\nexport interface QuickActionsProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof quickActionsVariants> {\n  /** Quick action items */\n  items: QuickActionItem[]\n  /** Orientation of action items */\n  orientation?: \"horizontal\" | \"vertical\"\n  /** Size of action items */\n  itemSize?: \"sm\" | \"default\" | \"lg\"\n  /** Loading state */\n  loading?: boolean\n  /** Number of skeleton items when loading */\n  skeletonCount?: number\n}\n\nconst QuickActions = React.forwardRef<HTMLDivElement, QuickActionsProps>(\n  ({ \n    className, \n    columns, \n    size, \n    items, \n    orientation = \"horizontal\",\n    itemSize = \"default\",\n    loading = false,\n    skeletonCount = 6,\n    ...props \n  }, ref) => {\n    if (loading) {\n      return (\n        <div \n          ref={ref} \n          className={cn(quickActionsVariants({ columns, size }), className)} \n          {...props}\n        >\n          {Array.from({ length: skeletonCount }).map((_, index) => (\n            <div\n              key={index}\n              className={cn(quickActionItemVariants({ \n                variant: \"default\", \n                size: itemSize, \n                orientation \n              }))}\n            >\n              <div className=\"h-6 w-6 bg-muted animate-pulse rounded\" />\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 w-24 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 w-32 bg-muted animate-pulse rounded\" />\n              </div>\n            </div>\n          ))}\n        </div>\n      )\n    }\n\n    return (\n      <div \n        ref={ref} \n        className={cn(quickActionsVariants({ columns, size }), className)} \n        {...props}\n      >\n        {items.map((item) => {\n          const Component = item.href ? \"a\" : \"button\"\n          \n          return (\n            <Component\n              key={item.id}\n              className={cn(\n                quickActionItemVariants({ \n                  variant: item.variant || \"default\", \n                  size: itemSize, \n                  orientation \n                }),\n                item.disabled && \"opacity-50 cursor-not-allowed\",\n                !item.disabled && (item.href || item.onClick) && \"cursor-pointer hover:scale-[1.02] active:scale-[0.98]\"\n              )}\n              onClick={item.disabled ? undefined : item.onClick}\n              disabled={item.disabled}\n              {...(item.href ? { href: item.href } : {})}\n            >\n              {/* Icon with loading state */}\n              <div className=\"relative flex-shrink-0\">\n                {item.loading ? (\n                  <div className=\"h-6 w-6 border-2 border-primary border-t-transparent rounded-full animate-spin\" />\n                ) : (\n                  <div className={cn(\n                    \"flex items-center justify-center h-6 w-6\",\n                    orientation === \"vertical\" && \"h-8 w-8\"\n                  )}>\n                    {item.icon}\n                  </div>\n                )}\n                \n                {/* Badge */}\n                {item.badge && !item.loading && (\n                  <span className=\"absolute -top-1 -right-1 flex items-center justify-center h-4 w-4 text-xs font-bold text-white bg-primary rounded-full\">\n                    {item.badge}\n                  </span>\n                )}\n              </div>\n\n              {/* Content */}\n              <div className={cn(\n                \"flex-1 text-left\",\n                orientation === \"vertical\" && \"text-center space-y-1\"\n              )}>\n                <h3 className=\"font-medium text-foreground group-hover:text-primary transition-colors\">\n                  {item.title}\n                </h3>\n                {item.description && (\n                  <p className=\"text-sm text-muted-foreground\">{item.description}</p>\n                )}\n              </div>\n\n              {/* Arrow indicator for links */}\n              {(item.href || item.onClick) && !item.disabled && orientation === \"horizontal\" && (\n                <svg \n                  className=\"h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors\" \n                  fill=\"none\" \n                  viewBox=\"0 0 24 24\" \n                  stroke=\"currentColor\"\n                >\n                  <path \n                    strokeLinecap=\"round\" \n                    strokeLinejoin=\"round\" \n                    strokeWidth={2} \n                    d=\"M9 5l7 7-7 7\" \n                  />\n                </svg>\n              )}\n            </Component>\n          )\n        })}\n      </div>\n    )\n  }\n)\nQuickActions.displayName = \"QuickActions\"\n\nexport { QuickActions, quickActionsVariants, quickActionItemVariants }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAuB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC7B,cACA;IACE,UAAU;QACR,SAAS;YACP,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,0BAA0B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAChC,mJACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,aAAa;YACX,YAAY;YACZ,UAAU;QACZ;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,aAAa;IACf;AACF;AA+BF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAClC,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,cAAc,YAAY,EAC1B,WAAW,SAAS,EACpB,UAAU,KAAK,EACf,gBAAgB,CAAC,EACjB,GAAG,OACJ,EAAE;IACD,IAAI,SAAS;QACX,qBACE,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;gBAAE;gBAAS;YAAK,IAAI;YACtD,GAAG,KAAK;sBAER,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAc,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC7C,6LAAC;oBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;wBACpC,SAAS;wBACT,MAAM;wBACN;oBACF;;sCAEA,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;mBAVZ;;;;;;;;;;IAgBf;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;YAAE;YAAS;QAAK,IAAI;QACtD,GAAG,KAAK;kBAER,MAAM,GAAG,CAAC,CAAC;YACV,MAAM,YAAY,KAAK,IAAI,GAAG,MAAM;YAEpC,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wBAAwB;oBACtB,SAAS,KAAK,OAAO,IAAI;oBACzB,MAAM;oBACN;gBACF,IACA,KAAK,QAAQ,IAAI,iCACjB,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,OAAO,KAAK;gBAEnD,SAAS,KAAK,QAAQ,GAAG,YAAY,KAAK,OAAO;gBACjD,UAAU,KAAK,QAAQ;gBACtB,GAAI,KAAK,IAAI,GAAG;oBAAE,MAAM,KAAK,IAAI;gBAAC,IAAI,CAAC,CAAC;;kCAGzC,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,OAAO,iBACX,6LAAC;gCAAI,WAAU;;;;;qDAEf,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,4CACA,gBAAgB,cAAc;0CAE7B,KAAK,IAAI;;;;;;4BAKb,KAAK,KAAK,IAAI,CAAC,KAAK,OAAO,kBAC1B,6LAAC;gCAAK,WAAU;0CACb,KAAK,KAAK;;;;;;;;;;;;kCAMjB,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oBACA,gBAAgB,cAAc;;0CAE9B,6LAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;4BAEZ,KAAK,WAAW,kBACf,6LAAC;gCAAE,WAAU;0CAAiC,KAAK,WAAW;;;;;;;;;;;;oBAKjE,CAAC,KAAK,IAAI,IAAI,KAAK,OAAO,KAAK,CAAC,KAAK,QAAQ,IAAI,gBAAgB,8BAChE,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,SAAQ;wBACR,QAAO;kCAEP,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;;eA5DH,KAAK,EAAE;;;;;QAkElB;;;;;;AAGN;;AAEF,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3402, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center gap-1 rounded-full border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-surface-100 text-accent hover:bg-surface-200\",\n        secondary: \"border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n        info: \"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200\",\n        primary: \"border-transparent bg-primary-100 text-primary-900 hover:bg-primary-200\",\n        outline: \"border-border text-accent hover:bg-surface-50\",\n        ghost: \"border-transparent hover:bg-surface-100 text-accent\",\n      },\n      size: {\n        sm: \"px-2 py-0.5 text-xs\",\n        default: \"px-2.5 py-1 text-sm\",\n        lg: \"px-3 py-1.5 text-base\",\n      },\n      dot: {\n        true: \"\",\n        false: \"\",\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      dot: false,\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {\n  /** Show a colored dot indicator */\n  dot?: boolean\n  /** Icon to display before the text */\n  icon?: React.ReactNode\n  /** Make the badge clickable */\n  onClick?: () => void\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, dot, icon, onClick, children, ...props }, ref) => {\n    const isClickable = !!onClick\n    \n    const getDotColor = () => {\n      switch (variant) {\n        case 'success': return 'bg-green-500'\n        case 'warning': return 'bg-yellow-500'\n        case 'error': return 'bg-red-500'\n        case 'info': return 'bg-blue-500'\n        case 'primary': return 'bg-primary'\n        case 'secondary': return 'bg-secondary'\n        default: return 'bg-accent'\n      }\n    }\n\n    if (isClickable) {\n      return (\n        <button\n          ref={ref as any}\n          type=\"button\"\n          className={cn(\n            badgeVariants({ variant, size }),\n            \"cursor-pointer hover:scale-105 active:scale-95\",\n            className\n          )}\n          onClick={onClick}\n          {...(props as React.ButtonHTMLAttributes<HTMLButtonElement>)}\n        >\n          {dot && (\n            <span\n              className={cn(\n                \"h-1.5 w-1.5 rounded-full\",\n                getDotColor()\n              )}\n            />\n          )}\n          \n          {icon && (\n            <span className=\"shrink-0\">\n              {icon}\n            </span>\n          )}\n          \n          {children}\n        </button>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...(props as React.HTMLAttributes<HTMLDivElement>)}\n      >\n        {dot && (\n          <span\n            className={cn(\n              \"h-1.5 w-1.5 rounded-full\",\n              getDotColor()\n            )}\n          />\n        )}\n        \n        {icon && (\n          <span className=\"shrink-0\">\n            {icon}\n          </span>\n        )}\n        \n        {children}\n      </div>\n    )\n  }\n)\nBadge.displayName = \"Badge\"\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,iKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;QACN;QACA,KAAK;YACH,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,KAAK;IACP;AACF;AAcF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrE,MAAM,cAAc,CAAC,CAAC;IAEtB,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YACC,KAAK;YACL,MAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;gBAAE;gBAAS;YAAK,IAC9B,kDACA;YAEF,SAAS;YACR,GAAI,KAAK;;gBAET,qBACC,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;gBAKL,sBACC,6LAAC;oBAAK,WAAU;8BACb;;;;;;gBAIJ;;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAI,KAAK;;YAET,qBACC,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA;;;;;;YAKL,sBACC,6LAAC;gBAAK,WAAU;0BACb;;;;;;YAIJ;;;;;;;AAGP;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3543, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Baby_SSD/github/AI%20projects/publish-ai/app_frontend/src/app/dashboard/analytics/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useSession } from \"next-auth/react\"\nimport { DashboardLayout } from \"@/components/layout/dashboard-layout\"\nimport { PageHeader } from \"@/components/layout/page-header\"\nimport { ProtectedRoute } from \"@/components/auth/protected-route\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from \"@/components/ui/tabs\"\nimport { StatsCard } from \"@/components/ui/stats-card\"\nimport { ChartWrapper, ChartPlaceholder } from \"@/components/ui/chart-wrapper\"\nimport { ActivityTimeline, type TimelineItem } from \"@/components/ui/activity-timeline\"\nimport { BookCard, type BookData } from \"@/components/ui/book-card\"\nimport { QuickActions, type QuickActionItem } from \"@/components/ui/quick-actions\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  Bar<PERSON>hart3, \n  Download, \n  Filter, \n  Refresh<PERSON><PERSON>,\n  DollarSign,\n  BookOpen,\n  TrendingUp,\n  Users,\n  Target,\n  Calendar,\n  Eye,\n  Star,\n  Globe,\n  PieChart,\n  LineChart,\n  Activity,\n  Edit\n} from \"lucide-react\"\n\n// Mock analytics data\nconst analyticsData = {\n  overview: {\n    totalRevenue: 4250.75,\n    totalSales: 287,\n    activeBooks: 12,\n    averageRating: 4.3,\n    revenueGrowth: 15.2,\n    salesGrowth: 23.5,\n    booksGrowth: 8.1,\n    ratingChange: 0.2\n  },\n  salesData: [\n    { month: \"Jan\", sales: 45, revenue: 675, books: 3 },\n    { month: \"Feb\", sales: 52, revenue: 780, books: 4 },\n    { month: \"Mar\", sales: 38, revenue: 570, books: 3 },\n    { month: \"Apr\", sales: 67, revenue: 1005, books: 5 },\n    { month: \"May\", sales: 85, revenue: 1275, books: 6 },\n    { month: \"Jun\", sales: 0, revenue: 0, books: 0 }\n  ],\n  topBooks: [\n    {\n      id: \"1\",\n      title: \"AI in Business\",\n      sales: 89,\n      revenue: 1250.50,\n      rating: 4.5,\n      trend: \"up\"\n    },\n    {\n      id: \"2\", \n      title: \"Machine Learning Guide\",\n      sales: 67,\n      revenue: 945.75,\n      rating: 4.2,\n      trend: \"up\"\n    },\n    {\n      id: \"3\",\n      title: \"Digital Transformation\",\n      sales: 45,\n      revenue: 630.25,\n      rating: 4.0,\n      trend: \"down\"\n    }\n  ],\n  recentActivity: [\n    {\n      id: \"1\",\n      title: \"Monthly revenue milestone reached\",\n      description: \"You've earned over $1,000 this month for the first time!\",\n      timestamp: \"2 hours ago\",\n      status: \"completed\" as const\n    },\n    {\n      id: \"2\",\n      title: \"New book published successfully\",\n      description: \"'Python Programming Guide' is now live on Amazon KDP.\",\n      timestamp: \"1 day ago\", \n      status: \"completed\" as const\n    },\n    {\n      id: \"3\",\n      title: \"High rating received\",\n      description: \"'AI in Business' received a 5-star review from verified purchaser.\",\n      timestamp: \"2 days ago\",\n      status: \"info\" as const\n    },\n    {\n      id: \"4\",\n      title: \"Sales goal achieved\",\n      description: \"Monthly sales target of 80 books has been exceeded.\",\n      timestamp: \"3 days ago\",\n      status: \"completed\" as const\n    }\n  ],\n  demographics: {\n    topCountries: [\n      { country: \"United States\", percentage: 65, sales: 187 },\n      { country: \"United Kingdom\", percentage: 18, sales: 52 },\n      { country: \"Canada\", percentage: 8, sales: 23 },\n      { country: \"Australia\", percentage: 5, sales: 14 },\n      { country: \"Germany\", percentage: 4, sales: 11 }\n    ],\n    ageGroups: [\n      { group: \"25-34\", percentage: 35 },\n      { group: \"35-44\", percentage: 28 },\n      { group: \"45-54\", percentage: 22 },\n      { group: \"18-24\", percentage: 10 },\n      { group: \"55+\", percentage: 5 }\n    ]\n  }\n}\n\nexport default function AnalyticsPage() {\n  const { data: session } = useSession()\n  const [timeRange, setTimeRange] = useState(\"6months\")\n  \n  const user = session?.user\n    ? {\n        name: session.user.name || \"User\",\n        email: session.user.email || \"\",\n        avatar: session.user.image || undefined,\n      }\n    : undefined\n\n  const quickActions: QuickActionItem[] = [\n    {\n      id: \"1\",\n      title: \"Export Report\",\n      description: \"Download detailed analytics report\",\n      icon: <Download className=\"h-5 w-5\" />,\n      onClick: () => console.log('Export analytics')\n    },\n    {\n      id: \"2\",\n      title: \"Schedule Report\",\n      description: \"Set up automated reporting\",\n      icon: <Calendar className=\"h-5 w-5\" />,\n      onClick: () => console.log('Schedule report')\n    },\n    {\n      id: \"3\",\n      title: \"Set Goals\",\n      description: \"Configure performance targets\",\n      icon: <Target className=\"h-5 w-5\" />,\n      onClick: () => console.log('Set goals')\n    },\n    {\n      id: \"4\",\n      title: \"View Trends\",\n      description: \"Analyze market trends\",\n      icon: <TrendingUp className=\"h-5 w-5\" />,\n      onClick: () => console.log('View trends')\n    }\n  ]\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"p-6\">\n          <PageHeader\n            title=\"Analytics\"\n            description=\"Comprehensive insights into your publishing performance and revenue\"\n            actions={\n              <div className=\"flex gap-2\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <Filter className=\"h-4 w-4 mr-2\" />\n                  Filter\n                </Button>\n                <Button variant=\"outline\" size=\"sm\">\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  Refresh\n                </Button>\n                <Button size=\"sm\">\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  Export\n                </Button>\n              </div>\n            }\n          />\n\n          <Tabs defaultValue=\"overview\" className=\"space-y-6\">\n            <TabsList>\n              <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n              <TabsTrigger value=\"revenue\">Revenue</TabsTrigger>\n              <TabsTrigger value=\"books\">Books</TabsTrigger>\n              <TabsTrigger value=\"audience\">Audience</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"overview\" className=\"space-y-6\">\n              {/* Key Metrics */}\n              <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n                <StatsCard\n                  value={`$${analyticsData.overview.totalRevenue.toFixed(2)}`}\n                  label=\"Total Revenue\"\n                  description=\"All-time earnings\"\n                  icon={<DollarSign className=\"h-5 w-5\" />}\n                  trend={{\n                    direction: \"up\",\n                    value: analyticsData.overview.revenueGrowth,\n                    label: \"vs last month\"\n                  }}\n                  variant=\"success\"\n                />\n                <StatsCard\n                  value={analyticsData.overview.totalSales.toString()}\n                  label=\"Total Sales\"\n                  description=\"Books sold\"\n                  icon={<BookOpen className=\"h-5 w-5\" />}\n                  trend={{\n                    direction: \"up\",\n                    value: analyticsData.overview.salesGrowth,\n                    label: \"vs last month\"\n                  }}\n                />\n                <StatsCard\n                  value={analyticsData.overview.activeBooks.toString()}\n                  label=\"Active Books\"\n                  description=\"Published titles\"\n                  icon={<TrendingUp className=\"h-5 w-5\" />}\n                  trend={{\n                    direction: \"up\",\n                    value: analyticsData.overview.booksGrowth,\n                    label: \"new this month\"\n                  }}\n                  variant=\"info\"\n                />\n                <StatsCard\n                  value={analyticsData.overview.averageRating.toString()}\n                  label=\"Average Rating\"\n                  description=\"Across all books\"\n                  icon={<Star className=\"h-5 w-5\" />}\n                  trend={{\n                    direction: \"up\",\n                    value: analyticsData.overview.ratingChange,\n                    label: \"vs last month\"\n                  }}\n                  variant=\"warning\"\n                />\n              </div>\n\n              {/* Charts Section */}\n              <div className=\"grid gap-6 lg:grid-cols-2\">\n                <ChartWrapper\n                  title=\"Revenue & Sales Trends\"\n                  description=\"Monthly performance over the last 6 months\"\n                  height=\"lg\"\n                  actions={\n                    <div className=\"flex gap-2\">\n                      <Badge variant={timeRange === \"6months\" ? \"default\" : \"secondary\"}>\n                        6M\n                      </Badge>\n                      <Badge variant={timeRange === \"1year\" ? \"default\" : \"secondary\"}>\n                        1Y\n                      </Badge>\n                    </div>\n                  }\n                  footer={\n                    <div className=\"flex justify-between text-sm text-muted-foreground\">\n                      <span>Last updated: 1 hour ago</span>\n                      <span>Peak month: May ({analyticsData.salesData[4].sales} sales)</span>\n                    </div>\n                  }\n                >\n                  <ChartPlaceholder type=\"area\" data={analyticsData.salesData} />\n                </ChartWrapper>\n\n                <ChartWrapper\n                  title=\"Top Performing Books\"\n                  description=\"Revenue breakdown by book\"\n                  height=\"lg\"\n                  variant=\"elevated\"\n                >\n                  <ChartPlaceholder \n                    type=\"pie\" \n                    data={analyticsData.topBooks.map(book => ({\n                      name: book.title,\n                      value: book.revenue\n                    }))} \n                  />\n                </ChartWrapper>\n              </div>\n\n              {/* Quick Actions */}\n              <div>\n                <h2 className=\"text-xl font-semibold mb-4\">Quick Actions</h2>\n                <QuickActions items={quickActions} columns={4} />\n              </div>\n\n              {/* Activity Timeline */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>Recent Activity</CardTitle>\n                  <CardDescription>Latest updates and milestones</CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <ActivityTimeline \n                    items={analyticsData.recentActivity} \n                    compact \n                    size=\"sm\"\n                  />\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"revenue\" className=\"space-y-6\">\n              <div className=\"grid gap-6\">\n                {/* Revenue Metrics */}\n                <div className=\"grid gap-4 md:grid-cols-3\">\n                  <StatsCard\n                    value={`$${(analyticsData.overview.totalRevenue / analyticsData.overview.totalSales).toFixed(2)}`}\n                    label=\"Revenue per Sale\"\n                    description=\"Average book price\"\n                    icon={<DollarSign className=\"h-5 w-5\" />}\n                    variant=\"success\"\n                  />\n                  <StatsCard\n                    value={`$${analyticsData.salesData[4].revenue || 0}`}\n                    label=\"This Month\"\n                    description=\"Current month revenue\"\n                    icon={<Calendar className=\"h-5 w-5\" />}\n                  />\n                  <StatsCard\n                    value={`$${(analyticsData.overview.totalRevenue * 0.7).toFixed(2)}`}\n                    label=\"Net Revenue\"\n                    description=\"After platform fees\"\n                    icon={<Target className=\"h-5 w-5\" />}\n                    variant=\"info\"\n                  />\n                </div>\n\n                {/* Revenue Chart */}\n                <ChartWrapper\n                  title=\"Monthly Revenue Breakdown\"\n                  description=\"Detailed revenue analysis over time\"\n                  height=\"xl\"\n                  actions={\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Download className=\"h-4 w-4 mr-2\" />\n                      Export Data\n                    </Button>\n                  }\n                >\n                  <ChartPlaceholder type=\"bar\" data={analyticsData.salesData} />\n                </ChartWrapper>\n\n                {/* Top Earning Books */}\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Top Earning Books</CardTitle>\n                    <CardDescription>Your most profitable titles</CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      {analyticsData.topBooks.map((book, index) => (\n                        <div key={book.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                          <div className=\"flex items-center gap-4\">\n                            <div className=\"w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-semibold\">\n                              {index + 1}\n                            </div>\n                            <div>\n                              <h4 className=\"font-medium\">{book.title}</h4>\n                              <p className=\"text-sm text-muted-foreground\">{book.sales} sales • {book.rating}★</p>\n                            </div>\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"font-bold text-green-600\">${book.revenue.toFixed(2)}</div>\n                            <div className=\"text-sm text-muted-foreground flex items-center gap-1\">\n                              {book.trend === \"up\" ? (\n                                <TrendingUp className=\"h-3 w-3 text-green-500\" />\n                              ) : (\n                                <TrendingUp className=\"h-3 w-3 text-red-500 rotate-180\" />\n                              )}\n                              <span>{book.trend === \"up\" ? \"Growing\" : \"Declining\"}</span>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"books\" className=\"space-y-6\">\n              <div className=\"grid gap-6\">\n                {/* Book Metrics */}\n                <div className=\"grid gap-4 md:grid-cols-4\">\n                  <StatsCard\n                    value={analyticsData.overview.activeBooks.toString()}\n                    label=\"Published Books\"\n                    description=\"Live on platforms\"\n                    icon={<BookOpen className=\"h-5 w-5\" />}\n                  />\n                  <StatsCard\n                    value=\"3\"\n                    label=\"Drafts\"\n                    description=\"In progress\"\n                    icon={<Edit className=\"h-5 w-5\" />}\n                    variant=\"warning\"\n                  />\n                  <StatsCard\n                    value=\"2\"\n                    label=\"In Review\"\n                    description=\"Pending approval\"\n                    icon={<Eye className=\"h-5 w-5\" />}\n                    variant=\"info\"\n                  />\n                  <StatsCard\n                    value={analyticsData.overview.averageRating.toString()}\n                    label=\"Avg Rating\"\n                    description=\"All books\"\n                    icon={<Star className=\"h-5 w-5\" />}\n                    variant=\"success\"\n                  />\n                </div>\n\n                {/* Books Performance Chart */}\n                <ChartWrapper\n                  title=\"Books Performance\"\n                  description=\"Sales comparison across your book portfolio\"\n                  height=\"lg\"\n                >\n                  <ChartPlaceholder \n                    type=\"bar\" \n                    data={analyticsData.topBooks.map(book => ({\n                      title: book.title,\n                      sales: book.sales,\n                      revenue: book.revenue\n                    }))} \n                  />\n                </ChartWrapper>\n\n                {/* Genre Performance */}\n                <div className=\"grid gap-6 lg:grid-cols-2\">\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Genre Performance</CardTitle>\n                      <CardDescription>Sales by book category</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-4\">\n                        {[\n                          { genre: \"Technology\", sales: 156, percentage: 54 },\n                          { genre: \"Business\", sales: 89, percentage: 31 },\n                          { genre: \"Education\", sales: 42, percentage: 15 }\n                        ].map((item) => (\n                          <div key={item.genre} className=\"space-y-2\">\n                            <div className=\"flex justify-between\">\n                              <span className=\"text-sm font-medium\">{item.genre}</span>\n                              <span className=\"text-sm text-muted-foreground\">{item.sales} sales</span>\n                            </div>\n                            <div className=\"w-full bg-muted rounded-full h-2\">\n                              <div \n                                className=\"bg-primary h-2 rounded-full\" \n                                style={{width: `${item.percentage}%`}}\n                              ></div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Publishing Velocity</CardTitle>\n                      <CardDescription>Books published over time</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <ChartPlaceholder \n                        type=\"line\" \n                        data={analyticsData.salesData.map(d => ({\n                          month: d.month,\n                          books: d.books\n                        }))} \n                      />\n                    </CardContent>\n                  </Card>\n                </div>\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"audience\" className=\"space-y-6\">\n              <div className=\"grid gap-6\">\n                {/* Audience Metrics */}\n                <div className=\"grid gap-4 md:grid-cols-4\">\n                  <StatsCard\n                    value=\"34\"\n                    label=\"Avg Age\"\n                    description=\"Primary demographic\"\n                    icon={<Users className=\"h-5 w-5\" />}\n                  />\n                  <StatsCard\n                    value=\"127\"\n                    label=\"Repeat Customers\"\n                    description=\"Multiple purchases\"\n                    icon={<Target className=\"h-5 w-5\" />}\n                    variant=\"success\"\n                  />\n                  <StatsCard\n                    value=\"5\"\n                    label=\"Countries\"\n                    description=\"Global reach\"\n                    icon={<Globe className=\"h-5 w-5\" />}\n                    variant=\"info\"\n                  />\n                  <StatsCard\n                    value=\"68%\"\n                    label=\"Mobile Readers\"\n                    description=\"Reading on mobile\"\n                    icon={<Activity className=\"h-5 w-5\" />}\n                  />\n                </div>\n\n                {/* Geographic Distribution */}\n                <div className=\"grid gap-6 lg:grid-cols-2\">\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Geographic Distribution</CardTitle>\n                      <CardDescription>Sales by country</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-4\">\n                        {analyticsData.demographics.topCountries.map((country) => (\n                          <div key={country.country} className=\"space-y-2\">\n                            <div className=\"flex justify-between\">\n                              <span className=\"text-sm font-medium\">{country.country}</span>\n                              <span className=\"text-sm text-muted-foreground\">{country.sales} sales</span>\n                            </div>\n                            <div className=\"w-full bg-muted rounded-full h-2\">\n                              <div \n                                className=\"bg-primary h-2 rounded-full\" \n                                style={{width: `${country.percentage}%`}}\n                              ></div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Age Demographics</CardTitle>\n                      <CardDescription>Reader age distribution</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <ChartPlaceholder \n                        type=\"pie\" \n                        data={analyticsData.demographics.ageGroups.map(group => ({\n                          name: group.group,\n                          value: group.percentage\n                        }))} \n                      />\n                    </CardContent>\n                  </Card>\n                </div>\n\n                {/* Reader Behavior */}\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Reader Behavior Insights</CardTitle>\n                    <CardDescription>Understanding your audience preferences</CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold\">156</div>\n                        <div className=\"text-sm text-muted-foreground\">Avg. Pages Read</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold\">4.2</div>\n                        <div className=\"text-sm text-muted-foreground\">Completion Rate</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold\">23%</div>\n                        <div className=\"text-sm text-muted-foreground\">Review Rate</div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </div>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAhBA;;;;;;;;;;;;;;;AAoCA,sBAAsB;AACtB,MAAM,gBAAgB;IACpB,UAAU;QACR,cAAc;QACd,YAAY;QACZ,aAAa;QACb,eAAe;QACf,eAAe;QACf,aAAa;QACb,aAAa;QACb,cAAc;IAChB;IACA,WAAW;QACT;YAAE,OAAO;YAAO,OAAO;YAAI,SAAS;YAAK,OAAO;QAAE;QAClD;YAAE,OAAO;YAAO,OAAO;YAAI,SAAS;YAAK,OAAO;QAAE;QAClD;YAAE,OAAO;YAAO,OAAO;YAAI,SAAS;YAAK,OAAO;QAAE;QAClD;YAAE,OAAO;YAAO,OAAO;YAAI,SAAS;YAAM,OAAO;QAAE;QACnD;YAAE,OAAO;YAAO,OAAO;YAAI,SAAS;YAAM,OAAO;QAAE;QACnD;YAAE,OAAO;YAAO,OAAO;YAAG,SAAS;YAAG,OAAO;QAAE;KAChD;IACD,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;KACD;IACD,gBAAgB;QACd;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;QACV;KACD;IACD,cAAc;QACZ,cAAc;YACZ;gBAAE,SAAS;gBAAiB,YAAY;gBAAI,OAAO;YAAI;YACvD;gBAAE,SAAS;gBAAkB,YAAY;gBAAI,OAAO;YAAG;YACvD;gBAAE,SAAS;gBAAU,YAAY;gBAAG,OAAO;YAAG;YAC9C;gBAAE,SAAS;gBAAa,YAAY;gBAAG,OAAO;YAAG;YACjD;gBAAE,SAAS;gBAAW,YAAY;gBAAG,OAAO;YAAG;SAChD;QACD,WAAW;YACT;gBAAE,OAAO;gBAAS,YAAY;YAAG;YACjC;gBAAE,OAAO;gBAAS,YAAY;YAAG;YACjC;gBAAE,OAAO;gBAAS,YAAY;YAAG;YACjC;gBAAE,OAAO;gBAAS,YAAY;YAAG;YACjC;gBAAE,OAAO;gBAAO,YAAY;YAAE;SAC/B;IACH;AACF;AAEe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,SAAS,OAClB;QACE,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;QAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;QAC7B,QAAQ,QAAQ,IAAI,CAAC,KAAK,IAAI;IAChC,IACA;IAEJ,MAAM,eAAkC;QACtC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;KACD;IAED,qBACE,6LAAC,mJAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,sJAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,aAAU;wBACT,OAAM;wBACN,aAAY;wBACZ,uBACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;;sDACX,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAO7C,6LAAC,mIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAW,WAAU;;0CACtC,6LAAC,mIAAA,CAAA,WAAQ;;kDACP,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAU;;;;;;kDAC7B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAQ;;;;;;kDAC3B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;;;;;;;0CAGhC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4IAAA,CAAA,YAAS;gDACR,OAAO,CAAC,CAAC,EAAE,cAAc,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;gDAC3D,OAAM;gDACN,aAAY;gDACZ,oBAAM,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAC5B,OAAO;oDACL,WAAW;oDACX,OAAO,cAAc,QAAQ,CAAC,aAAa;oDAC3C,OAAO;gDACT;gDACA,SAAQ;;;;;;0DAEV,6LAAC,4IAAA,CAAA,YAAS;gDACR,OAAO,cAAc,QAAQ,CAAC,UAAU,CAAC,QAAQ;gDACjD,OAAM;gDACN,aAAY;gDACZ,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAC1B,OAAO;oDACL,WAAW;oDACX,OAAO,cAAc,QAAQ,CAAC,WAAW;oDACzC,OAAO;gDACT;;;;;;0DAEF,6LAAC,4IAAA,CAAA,YAAS;gDACR,OAAO,cAAc,QAAQ,CAAC,WAAW,CAAC,QAAQ;gDAClD,OAAM;gDACN,aAAY;gDACZ,oBAAM,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAC5B,OAAO;oDACL,WAAW;oDACX,OAAO,cAAc,QAAQ,CAAC,WAAW;oDACzC,OAAO;gDACT;gDACA,SAAQ;;;;;;0DAEV,6LAAC,4IAAA,CAAA,YAAS;gDACR,OAAO,cAAc,QAAQ,CAAC,aAAa,CAAC,QAAQ;gDACpD,OAAM;gDACN,aAAY;gDACZ,oBAAM,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACtB,OAAO;oDACL,WAAW;oDACX,OAAO,cAAc,QAAQ,CAAC,YAAY;oDAC1C,OAAO;gDACT;gDACA,SAAQ;;;;;;;;;;;;kDAKZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+IAAA,CAAA,eAAY;gDACX,OAAM;gDACN,aAAY;gDACZ,QAAO;gDACP,uBACE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,cAAc,YAAY,YAAY;sEAAa;;;;;;sEAGnE,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,cAAc,UAAU,YAAY;sEAAa;;;;;;;;;;;;gDAKrE,sBACE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAK;gEAAkB,cAAc,SAAS,CAAC,EAAE,CAAC,KAAK;gEAAC;;;;;;;;;;;;;0DAI7D,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;oDAAC,MAAK;oDAAO,MAAM,cAAc,SAAS;;;;;;;;;;;0DAG7D,6LAAC,+IAAA,CAAA,eAAY;gDACX,OAAM;gDACN,aAAY;gDACZ,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;oDACf,MAAK;oDACL,MAAM,cAAc,QAAQ,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;4DACxC,MAAM,KAAK,KAAK;4DAChB,OAAO,KAAK,OAAO;wDACrB,CAAC;;;;;;;;;;;;;;;;;kDAMP,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC,+IAAA,CAAA,eAAY;gDAAC,OAAO;gDAAc,SAAS;;;;;;;;;;;;kDAI9C,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC,mJAAA,CAAA,mBAAgB;oDACf,OAAO,cAAc,cAAc;oDACnC,OAAO;oDACP,MAAK;;;;;;;;;;;;;;;;;;;;;;;0CAMb,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAO,CAAC,CAAC,EAAE,CAAC,cAAc,QAAQ,CAAC,YAAY,GAAG,cAAc,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI;oDACjG,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAC5B,SAAQ;;;;;;8DAEV,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAO,CAAC,CAAC,EAAE,cAAc,SAAS,CAAC,EAAE,CAAC,OAAO,IAAI,GAAG;oDACpD,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAE5B,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAO,CAAC,CAAC,EAAE,CAAC,cAAc,QAAQ,CAAC,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC,IAAI;oDACnE,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACxB,SAAQ;;;;;;;;;;;;sDAKZ,6LAAC,+IAAA,CAAA,eAAY;4CACX,OAAM;4CACN,aAAY;4CACZ,QAAO;4CACP,uBACE,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;sDAKzC,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,MAAK;gDAAM,MAAM,cAAc,SAAS;;;;;;;;;;;sDAI5D,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;;sEACT,6LAAC,mIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,mIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAEnB,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;kEACZ,cAAc,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,6LAAC;gEAAkB,WAAU;;kFAC3B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,QAAQ;;;;;;0FAEX,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAe,KAAK,KAAK;;;;;;kGACvC,6LAAC;wFAAE,WAAU;;4FAAiC,KAAK,KAAK;4FAAC;4FAAU,KAAK,MAAM;4FAAC;;;;;;;;;;;;;;;;;;;kFAGnF,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;oFAA2B;oFAAE,KAAK,OAAO,CAAC,OAAO,CAAC;;;;;;;0FACjE,6LAAC;gFAAI,WAAU;;oFACZ,KAAK,KAAK,KAAK,qBACd,6LAAC,qNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;6GAEtB,6LAAC,qNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;kGAExB,6LAAC;kGAAM,KAAK,KAAK,KAAK,OAAO,YAAY;;;;;;;;;;;;;;;;;;;+DAlBrC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CA6B7B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;0CACnC,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAO,cAAc,QAAQ,CAAC,WAAW,CAAC,QAAQ;oDAClD,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAE5B,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAM;oDACN,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACtB,SAAQ;;;;;;8DAEV,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAM;oDACN,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACrB,SAAQ;;;;;;8DAEV,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAO,cAAc,QAAQ,CAAC,aAAa,CAAC,QAAQ;oDACpD,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACtB,SAAQ;;;;;;;;;;;;sDAKZ,6LAAC,+IAAA,CAAA,eAAY;4CACX,OAAM;4CACN,aAAY;4CACZ,QAAO;sDAEP,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,MAAK;gDACL,MAAM,cAAc,QAAQ,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wDACxC,OAAO,KAAK,KAAK;wDACjB,OAAO,KAAK,KAAK;wDACjB,SAAS,KAAK,OAAO;oDACvB,CAAC;;;;;;;;;;;sDAKL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mIAAA,CAAA,OAAI;;sEACH,6LAAC,mIAAA,CAAA,aAAU;;8EACT,6LAAC,mIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,mIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAEnB,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC;gEAAI,WAAU;0EACZ;oEACC;wEAAE,OAAO;wEAAc,OAAO;wEAAK,YAAY;oEAAG;oEAClD;wEAAE,OAAO;wEAAY,OAAO;wEAAI,YAAY;oEAAG;oEAC/C;wEAAE,OAAO;wEAAa,OAAO;wEAAI,YAAY;oEAAG;iEACjD,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;wEAAqB,WAAU;;0FAC9B,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGAAuB,KAAK,KAAK;;;;;;kGACjD,6LAAC;wFAAK,WAAU;;4FAAiC,KAAK,KAAK;4FAAC;;;;;;;;;;;;;0FAE9D,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC;oFACC,WAAU;oFACV,OAAO;wFAAC,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC;oFAAA;;;;;;;;;;;;uEARhC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;8DAiB5B,6LAAC,mIAAA,CAAA,OAAI;;sEACH,6LAAC,mIAAA,CAAA,aAAU;;8EACT,6LAAC,mIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,mIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAEnB,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,MAAK;gEACL,MAAM,cAAc,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;wEACtC,OAAO,EAAE,KAAK;wEACd,OAAO,EAAE,KAAK;oEAChB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQb,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAM;oDACN,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEzB,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAM;oDACN,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACxB,SAAQ;;;;;;8DAEV,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAM;oDACN,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDACvB,SAAQ;;;;;;8DAEV,6LAAC,4IAAA,CAAA,YAAS;oDACR,OAAM;oDACN,OAAM;oDACN,aAAY;oDACZ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mIAAA,CAAA,OAAI;;sEACH,6LAAC,mIAAA,CAAA,aAAU;;8EACT,6LAAC,mIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,mIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAEnB,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC;gEAAI,WAAU;0EACZ,cAAc,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,wBAC5C,6LAAC;wEAA0B,WAAU;;0FACnC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGAAuB,QAAQ,OAAO;;;;;;kGACtD,6LAAC;wFAAK,WAAU;;4FAAiC,QAAQ,KAAK;4FAAC;;;;;;;;;;;;;0FAEjE,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC;oFACC,WAAU;oFACV,OAAO;wFAAC,OAAO,GAAG,QAAQ,UAAU,CAAC,CAAC,CAAC;oFAAA;;;;;;;;;;;;uEARnC,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;8DAiBjC,6LAAC,mIAAA,CAAA,OAAI;;sEACH,6LAAC,mIAAA,CAAA,aAAU;;8EACT,6LAAC,mIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,mIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAEnB,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,MAAK;gEACL,MAAM,cAAc,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;wEACvD,MAAM,MAAM,KAAK;wEACjB,OAAO,MAAM,UAAU;oEACzB,CAAC;;;;;;;;;;;;;;;;;;;;;;;sDAOT,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;;sEACT,6LAAC,mIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,mIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAEnB,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAqB;;;;;;kFACpC,6LAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;0EAEjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAqB;;;;;;kFACpC,6LAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;0EAEjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAqB;;;;;;kFACpC,6LAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvE;GA5dwB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}]}